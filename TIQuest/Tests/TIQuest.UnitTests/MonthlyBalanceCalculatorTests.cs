using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using TIQuest.Api;
using TIQuest.Api.Entities;

namespace TIQuest.UnitTests
{
    [TestClass]
    public class MonthlyBalanceCalculatorTests
    {
        private static DateTime _monthStartDate = new(2024, 02, 01);
        private static DateTime _monthEndDate = _monthStartDate.AddMonths(1);
        private static decimal _startingBalance = 10000;
        private static MonthlyBalanceCalculator _calculator = new(_monthStartDate, _monthEndDate);

        [DataTestMethod]
        [DynamicData(nameof(GetTransactionData), DynamicDataSourceType.Method)]
        public void GetMonthlyAverageBalance(List<Transaction> transactions, decimal startingBalance, decimal expectedResult)
        {
            // Act
            var result = _calculator.CalculateAverageMonthlyBalance(transactions, startingBalance);

            // Assert
            Assert.AreEqual(expectedResult, result);
        }

        private static IEnumerable<object[]> GetTransactionData()
        {
            var negativeStartingBalance = _startingBalance * -1;
            // Define test data
            var testData = new List<object[]>
            {
                // investment accounts
                // No change in balance
                new object[] { new List<Transaction>(), _startingBalance, _startingBalance },
                // Increase in balance on first day
                new object[] { new List<Transaction>() { new() { Date = _monthStartDate, Amount = 10000 } }, _startingBalance, 20000m },
                // Decrease in balance on first day
                new object[] { new List<Transaction>() { new() { Date = _monthStartDate, Amount = -5000 } }, _startingBalance, 5000m },
                // Increase in balance on last day
                new object[] { new List<Transaction>() { new() { Date = _monthEndDate.AddHours(-1), Amount = 10000 } }, _startingBalance, ((_startingBalance * 28) + 20000) / 28 },
                // Decrease in balance on last day
                new object[] { new List<Transaction>() { new() { Date = _monthEndDate.AddHours(-1), Amount = -5000 } }, _startingBalance, ((_startingBalance * 28) + 5000) / 28 },
                // Increase in balance on random days
                new object[]
                {
                    new List<Transaction>()
                        {
                            new() { Date = new DateTime(2024,02,05), Amount = 5000 },
                            new() { Date = new DateTime(2024,02,15), Amount = 10000 },
                            new() { Date = new DateTime(2024,02,25), Amount = 20000 }
                        },
                    _startingBalance,
                    ((_startingBalance * 4) + (15000 * 10) + (25000 * 10) + (45000 * 5)) / 28
                },
                // Decrease in balance on random days
                new object[]
                {
                    new List<Transaction>()
                    {
                        new() { Date = new DateTime(2024,02,05), Amount = -1000 },
                        new() { Date = new DateTime(2024,02,15), Amount = -2000 },
                        new() { Date = new DateTime(2024,02,25), Amount = -5000 }
                    },
                    _startingBalance,
                    ((_startingBalance * 4) + (9000 * 10) + (7000 * 10) + (2000 * 5)) / 28
                },
                // Mix of Increase & Decrease in balance on random days
                new object[]
                {
                    new List<Transaction>()
                    {
                        new() { Date = new DateTime(2024,02,05), Amount = -1000 },
                        new() { Date = new DateTime(2024,02,15), Amount = -2000 },
                        new() { Date = new DateTime(2024,02,25), Amount = 5000 }
                    },
                    _startingBalance,
                    ((_startingBalance * 4) + (9000 * 10) + (7000 * 10) + (12000 * 5)) / 28
                },

                // loan accounts
                // No change in balance
                new object[] { new List<Transaction>(), negativeStartingBalance, negativeStartingBalance },
                // Decrease in balance on first day
                new object[] { new List<Transaction>() { new() { Date = _monthStartDate, Amount = -10000 } }, negativeStartingBalance, -20000m },
                // Increase in balance on first day
                new object[] { new List<Transaction>() { new() { Date = _monthStartDate, Amount = 5000 } }, negativeStartingBalance, -5000m },
                // Decrease in balance on last day
                new object[] { new List<Transaction>() { new() { Date = _monthEndDate.AddHours(-1), Amount = -10000 } }, negativeStartingBalance, ((negativeStartingBalance * 28) - 20000) / 28 },
                // Increase in balance on last day
                new object[] { new List<Transaction>() { new() { Date = _monthEndDate.AddHours(-1), Amount = 5000 } }, negativeStartingBalance, ((negativeStartingBalance * 28) - 5000) / 28 },
                // Decrease in balance on random days
                new object[]
                {
                    new List<Transaction>()
                        {
                            new() { Date = new DateTime(2024,02,05), Amount = -5000 },
                            new() { Date = new DateTime(2024,02,15), Amount = -10000 },
                            new() { Date = new DateTime(2024,02,25), Amount = -20000 }
                        },
                    negativeStartingBalance,
                    ((negativeStartingBalance * 4) + (-15000 * 10) + (-25000 * 10) + (-45000 * 5)) / 28
                },
                // Increase in balance on random days
                new object[]
                {
                    new List<Transaction>()
                    {
                        new() { Date = new DateTime(2024,02,05), Amount = 1000 },
                        new() { Date = new DateTime(2024,02,15), Amount = 2000 },
                        new() { Date = new DateTime(2024,02,25), Amount = 5000 }
                    },
                    negativeStartingBalance,
                    ((negativeStartingBalance * 4) + (-9000 * 10) + (-7000 * 10) + (-2000 * 5)) / 28
                },
                // Mix of Increase & Decrease in balance on random days
                new object[]
                {
                    new List<Transaction>()
                    {
                        new() { Date = new DateTime(2024,02,05), Amount = 1000 },
                        new() { Date = new DateTime(2024,02,15), Amount = 2000 },
                        new() { Date = new DateTime(2024,02,25), Amount = -5000 }
                    },
                    negativeStartingBalance,
                    ((negativeStartingBalance * 4) + (-9000 * 10) + (-7000 * 10) + (-12000 * 5)) / 28
                }
            };

            return testData;
        }
    }
}