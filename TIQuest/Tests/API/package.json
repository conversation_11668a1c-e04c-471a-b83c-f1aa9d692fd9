{"name": "tritonbackend-qa", "version": "1.0.0", "description": "End To End Automation Framework", "main": "index.js", "scripts": {"test": "cypress run", "allure:report": "allure generate allure-results --clean -o allure-reports", "lint": "eslint cypress/**/*.js"}, "author": "<PERSON><PERSON>", "license": "ISC", "devDependencies": {"@cypress/grep": "^4.0.1", "@shelex/cypress-allure-plugin": "^2.40.1", "ajv-formats": "2.1.1", "allure-commandline": "2.24.1", "cypress": "^13.9.0", "eslint": "8.52.0", "eslint-plugin-cypress": "2.15.1", "eslint-plugin-react": "7.33.2", "faker": "5.5.3"}, "dependencies": {"cypress-mochawesome-reporter": "3.6.1", "cypress-plugin-api": "2.11.1", "cypress-xpath": "2.0.1"}}