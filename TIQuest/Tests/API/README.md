# TritonBackend-QA
This Cypress test automation framework built with JavaScript (JS). 

# Setup
1. Install NodeJS and NPM package manager
2. Code Editor of your choice e.g. Visual Studio Code

# Setup Command
1. npm init to setup node project with package.json
2. npm install --save-dev cypress to install cypress as dev dependency
3. npx cypress open to open the cypress test runner and choose E2E Testing which will create cypress config, support and fixture folders.

# How to user this
1. Clone git repo
2. Navigate to folder and open terminal
3. Run npm install to install the framework dependencies

 
