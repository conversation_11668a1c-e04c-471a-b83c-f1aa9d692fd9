const { defineConfig } = require("cypress");

module.exports = defineConfig({
  video: false,
  reporter: 'cypress-mochawesome-reporter',
  reporterOptions: {
    charts: true,
    reportPageTitle: 'Triton API Test Reports',
    embeddedScreenshots: true,
    inlineAssets: true,
    saveAllAttempts: false,
    ignoreVideos: false,
  },
  e2e: {
    specPattern: 'cypress/test/**/*.cy.{js,jsx,ts,tsx}',
    baseUrl: "https://tiquest-staging-api.azurewebsites.net",
    setupNodeEvents(on, config) {
      require('cypress-mochawesome-reporter/plugin')(on);
      require('@cypress/grep/src/plugin')(config);
    },
    experimentalMemoryManagement: true,
    numTestsKeptInMemory: 0,
  },
})