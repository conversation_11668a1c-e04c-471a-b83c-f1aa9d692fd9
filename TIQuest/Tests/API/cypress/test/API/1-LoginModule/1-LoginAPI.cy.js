describe("Test Scenario: End to End flow to Test Login Features", () => {
    let authToken;

    it("Test Case 01: Verify that a valid email and password combination returns a 200 status code and a token", { tags: ["@PR", "@Smoke"] }, () => {
        cy.fixture("loginCredentials.json").then((credentials) => {
            const base64Password = btoa(credentials.password);
            cy.api({
                method: "POST",
                url: "/auth/login",
                body: {
                    email: credentials.email,
                    password: base64Password,
                },
            }).then((response) => {
                expect(response.status).to.equal(200);
                expect(response.body).to.have.property("token");
                authToken = response.body.token;

                expect(response.duration).to.be.below(5000);
                expect(response.headers["content-type"]).to.include("application/json");
                expect(authToken).to.be.a("string");
            });
        });
    });


    it("Test Case 02: Verify that an invalid email and password combination returns a 401 Unauthorized status code", { tags: ["@PR", "@Smoke"] }, () => {
        cy.fixture("loginCredentials.json").then((credentials) => {
            const base64Password = btoa(credentials.password);
            cy.api({
                method: "POST",
                url: "/auth/login",
                body: {
                    email: "<EMAIL>",
                    password: base64Password,
                },
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.equal(401);
            });
        });
    });

    it("Test Case 03: Verify that an empty email payload returns a 400 Bad Request status code", { tags: ["@PR", "@Smoke"] }, () => {
        cy.fixture("loginCredentials.json").then((credentials) => {
            const base64Password = btoa(credentials.password);
            cy.api({
                method: "POST",
                url: "/auth/login",
                body: {
                    password: base64Password,
                },
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.equal(400);
            });
        });
    });

    it("Test Case 04: Verify that an empty password payload returns a 400 Bad Request status code", { tags: ["@PR", "@Smoke"] }, () => {
        cy.fixture("loginCredentials.json").then((credentials) => {
            const base64Password = btoa(credentials.password);
            cy.api({
                method: "POST",
                url: "/auth/login",
                body: {
                    email: "<EMAIL>",
                },
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.equal(400);
            });
        });
    });

    it("Test Case 05: Verify that a request with additional unrecognized fields is handled correctly", { tags: ["@PR", "@Smoke"] }, () => {
        cy.fixture("loginCredentials.json").then((credentials) => {
            const base64Password = btoa(credentials.password);
            cy.api({
                method: "POST",
                url: "/auth/login",
                body: {
                    email: credentials.email,
                    password: base64Password,
                    extraField: "extraValue",
                },
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.equal(200);
            });
        });
    });

    it("Test Case 06: Verify that the API returns a 415 Unsupported Media Type when the Content-Type header is not set to application/json", { tags: ["@PR", "@Smoke"] }, () => {
        cy.fixture("loginCredentials.json").then((credentials) => {
            const base64Password = btoa(credentials.password);
            cy.api({
                method: "POST",
                url: "/auth/login",
                body: {
                    email: credentials.email,
                    password: base64Password,
                },
                failOnStatusCode: false,
                headers: {
                    "Content-Type": "text/plain",
                },
            }).then((response) => {
                expect(response.status).to.equal(415);
            });
        });
    });

    it("Test Case 07: Verify that the API returns a 405 Method Not Allowed if the request method is not POST", { tags: ["@PR", "@Smoke"] }, () => {
        cy.fixture("loginCredentials.json").then((credentials) => {
            const base64Password = btoa(credentials.password);
            cy.api({
                method: "GET",
                url: "/auth/login",
                body: {
                    email: credentials.email,
                    password: base64Password,
                },
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.equal(405);
            });
        });
    });

    it("Test Case 08: Verify that the Content-Type header of the response is application/json; charset=utf-8.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.fixture("loginCredentials.json").then((credentials) => {
            const base64Password = btoa(credentials.password);
            cy.api({
                method: "POST",
                url: "/auth/login",
                body: {
                    email: credentials.email,
                    password: base64Password,
                },
                failOnStatusCode: false,
                headers: {
                    "Content-Type": "application/json",
                },
            }).then((response) => {
                expect(response.headers['content-type']).to.include('application/json; charset=utf-8');
            });
        });
    });
});