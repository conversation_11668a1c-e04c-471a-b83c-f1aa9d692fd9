
describe("Test Scenario: End to End flow for Change Password Features", () => {
    it("Test Case 01: Verify the Change Password API should return the Status code 204 and empty response", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "POST",
                url: "/auth/change-password",
                headers: {
                    'Authorization': `Bear<PERSON> ${token}`
                }
            }).then((response) => {
                expect(response.status).to.eq(204);
                expect(response.body).to.be.empty;
            });
        });
    });

    it("Test Case 02: Verify the Change Password API should return the Status code 401 ", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "POST",
                url: "/auth/change-password",
                headers: {
                    'Authorization': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Lre_7V73x2mj5ZcfsSBj2EUVJ_iuYacDck_M_DRqukA'
                },
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.eq(401);
            });
        });
    });

    it("Test Case 03: Verify that sending an improperly formatted request returns a 400 Bad Request status code.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "POST",
                url: "/auth/change-password1",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.eq(404);
            });
        });
    });

    it("Test Case 04: Verify that sending a GET request instead of a POST request returns a 405 Method Not Allowed status code.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "GET",
                url: "/auth/change-password",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.eq(405);
            });
        });
    });
});