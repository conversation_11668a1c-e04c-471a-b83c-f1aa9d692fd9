
describe("Test Scenario: End to End flow to Test Logout Flow", () => {
    it("Test Case 01: Verify that a POST request with a valid Base64 encoded new password returns a 403 ", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            const base64Password = btoa('123456aA!');
            cy.api({
                method: "POST",
                url: "/auth/reset-password",
                body: base64Password,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.eq(403);
                expect(response.body).to.be.empty;
            });
        });
    });

    it("Test Case 02: Verify that the reset password request requires a valid reset token or identifier to be passed along with the request - 401 ", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            const base64Password = btoa('123456aA!');
            cy.api({
                method: "POST",
                url: "/auth/reset-password",
                body: 'base64Password',
                headers: {
                    'Authorization': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIzIiwianRpIjoiNjM1ZTcxNjgtYjkwMi00MzUwLTgxNzQtMTdmMTE1MzY2YWQ0IiwiaWF0IjoiMTIvMDcvMjAyMyAwNzozNToyOCIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6IlBhc3N3b3JkUmVzZXQiLCJuYmYiOjE3MDE5MzQ1MjgsImV4cCI6MTcwMjAyMDkyOCwiaXNzIjoiYXBpLnRpcXVlc3QuY29tIiwiYXVkIjoid3d3LnRpcXVlc3QuY29tIn0.uuegGC6I_CtZ3RcwZIDmBYDzXr0K8-pUSnyvpmfgkUE'
                },
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.eq(401);
            });
        });
    });

    it("Test Case 03: Verify that the API returns a 415 Unsupported Media Type when the Content-Type header is not set to application/json", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            const base64Password = btoa('123456aA!');
            cy.api({
                method: "POST",
                url: "/auth/reset-password",
                body: 'base64Password',
                headers: {
                    "Content-Type": "text/plain",
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.eq(415);
            });
        });
    });

    it("Test Case 04: Verify that the API returns a 405 Method Not Allowed if the request method is not POST.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            const base64Password = btoa('123456aA!');
            cy.api({
                method: "GET",
                url: "/auth/reset-password",
                body: 'base64Password',
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.eq(405);
            });
        });
    })
});