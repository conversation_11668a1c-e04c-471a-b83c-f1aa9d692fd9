
describe("Test Scenario: End to End flow for Forgot Password Features", () => {
    it("Test Case 01: Verify the Forgot Password API should return the Status code 204 and empty response", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "POST",
                url: "/auth/forgot-password",
                headers: {
                    'Content-Type': 'application/json',
                },
                body: '"<EMAIL>"',
            }).then((response) => {
                expect(response.status).to.eq(204);
                expect(response.body).to.be.empty;
            });
        });
    });

    it("Test Case 02: Verify the Forgot Password API should return the Status code 415 for Invalid Input  ", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "POST",
                url: "/auth/forgot-password",
                headers: {
                    'Content-Type': 'application/plain'
                },
                body: '"<EMAIL>"',
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.eq(415);
            });
        });
    });

    it("Test Case 03: Verify that making a request with an email not associated with any account still returns 204 No Content to prevent email enumeration", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "POST",
                url: "/auth/forgot-password",
                headers: {
                    'Content-Type': 'application/json',
                },
                body: '"<EMAIL>"',
            }).then((response) => {
                expect(response.status).to.eq(204);
                expect(response.body).to.be.empty;
            });
        });
    });

    it("Test Case 04: Verify that submitting an invalid email format returns a 400 Bad Request status code", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "POST",
                url: "/auth/forgot-password",
                headers: {
                    'Content-Type': 'application/json',
                },
                failOnStatusCode: false,
                body: '"nitin.kr.pathak"',
            }).then((response) => {
                expect(response.status).to.eq(400);
            });
        });
    });

    it("Test Case 05: Verify that the endpoint requires the email field and returns a 400 Bad Request status if it is missing.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "POST",
                url: "/auth/forgot-password",
                headers: {
                    'Content-Type': 'application/json',
                },
                failOnStatusCode: false,
                //body: '"<EMAIL>"', 
            }).then((response) => {
                expect(response.status).to.eq(400);
                expect(response.body).to.have.property('title', 'One or more validation errors occurred.');
                expect(response.body).to.have.property('status', 400);
                expect(response.body).to.have.property('traceId').and.to.be.a('string');
                expect(response.body).to.have.property('errors');
                expect(response.body.errors).to.have.property('');
                expect(response.body.errors['']).to.include('A non-empty request body is required.');
                expect(response.body.errors).to.have.property('email');
                expect(response.body.errors['email']).to.include('The email field is required.');

            });
        });
    });

    it("Test Case 06: Verify that the API returns a 405 Method Not Allowed if the request method is not POST.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "GET",
                url: "/auth/forgot-password",
                headers: {
                    'Content-Type': 'application/json',
                },
                failOnStatusCode: false,
                body: '"<EMAIL>"',
            }).then((response) => {
                expect(response.status).to.eq(405);
            });
        });
    });
});