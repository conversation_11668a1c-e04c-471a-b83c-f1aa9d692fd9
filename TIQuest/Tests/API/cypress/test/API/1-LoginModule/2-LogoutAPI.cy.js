
describe("Test Scenario: End to End flow to Test Logout Flow", () => {
    let authToken;

    it("Test Case 01: Verify that admin should successfully log in, get authentication token", { tags: ['@PR', '@Smoke'] }, () => {
        cy.fixture("loginCredentials.json").then((credentials) => {
            const base64Password = btoa(credentials.password);

            cy.api({
                method: "POST",
                url: "/auth/login",
                body: {
                    email: credentials.email,
                    password: base64Password,
                },
            }).then((response) => {
                cy.log(`Received login response with status: ${response.status}`);
                expect(response.status).to.equal(200);
                expect(response.body).to.have.property("token");
                authToken = response.body.token;
            });
        });
    });

    it("Test Case 02: Verify that admin should successfully logout from Application", { tags: ['@PR', '@Smoke'] }, () => {
        cy.request({
            method: "POST",
            url: "/auth/logout",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${authToken}`,
            },
        }).then((response) => {
            expect(response.status).to.eq(204);
            expect(response.body).to.be.empty;

            // To ensure no sensitive headers are present, you can list them and assert their absence
            const sensitiveHeaders = ['Set-Cookie', 'Authorization'];
            sensitiveHeaders.forEach(header => {
                expect(response.headers).to.not.have.property(header);
            });
        });
    });

    it("Test Case 03: Verify that the endpoint recognizes and rejects invalid tokens with an appropriate error status code - 401", { tags: ['@PR', '@Smoke'] }, () => {
        cy.request({
            method: "POST",
            url: "/auth/logout",
            headers: {
                "Content-Type": "application/json",
                'Authorization': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Lre_7V73x2mj5ZcfsSBj2EUVJ_iuYacDck_M_DRqukA'
            },
            failOnStatusCode: false,
        }).then((response) => {
            expect(response.status).to.eq(401);
        });
    });

    it("Test Case 04: Verify that after a successful logout, the token is invalidated and cannot be used for further requests.", { tags: ['@PR', '@Smoke'] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "POST",
                url: "/auth/logout",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                },
                failOnStatusCode: false,
            }).then((logoutResponse) => {
                expect(logoutResponse.status).to.eq(204);

                // Then, attempt to change the password using the same token
                cy.api({
                    method: "POST",
                    url: "/auth/change-password",
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    failOnStatusCode: false,
                }).then((changePasswordResponse) => {
                    // Expecting a 401 Unauthorized status because the token should be invalidated
                    expect(changePasswordResponse.status).to.eq(401);
                });
            });
        });
    });

    it("Test Case 05: Verify that a GET request without the Authorization header is rejected and returns a 401 Unauthorized status code.", { tags: ['@PR', '@Smoke'] }, () => {
        cy.request({
            method: "GET",
            url: "/auth/logout",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${authToken}`,
            },
            failOnStatusCode: false,
        }).then((response) => {
            expect(response.status).to.eq(401);
        });
    });
});