describe("Test Scenario: End to End flow to Users Features", () => {

    function decodeJWT(token) {
        const base64Url = token.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function (c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
        return JSON.parse(jsonPayload);
    };


    it("Test Case 01: Verify the Users API should return the detail of LoggedIN User", { tags: ["@PR", "@Smoke"] }, () => {

        cy.login().then((token) => {
            //const userId = decodeJWT(token).sub;
            const userId = parseInt(decodeJWT(token).sub, 10);
            cy.api({
                method: "GET",
                url: `/users/${userId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                expect(response.status).to.eq(200);
                expect(response.body).to.have.property('id', userId);
                expect(response.body).to.have.property('firstName', 'Nitin');
                expect(response.body).to.have.property('lastName', 'Pathak');
                expect(response.body).to.have.property('email', '<EMAIL>');
                expect(response.body).to.have.property('isActive', true);
            });
        });
    });

    it("Test Case 02: Verify that the Users API does not return details when an invalid token is provided.", { tags: ["@PR", "@Smoke"] }, () => {
        
        cy.login().then((token) => {
            const userId = parseInt(decodeJWT(token).sub, 10);
            cy.api({
                method: "GET",
                url: `/users/${userId}`,
                headers: {
                    'Authorization': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Lre_7V73x2mj5ZcfsSBj2EUVJ_iuYacDck_M_DRqukA'
                },
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.eq(401);
            });
        });
    });
});