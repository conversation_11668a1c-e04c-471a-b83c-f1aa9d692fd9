import { getCreatedEntityIdFromHeader } from "../../../fixtures/apihelperFunctions";

describe("Test Scenario: Verify the validation of the Account Listing.", () => {

    let accountId;

    it("Test Case 01: Verify the creation of an Investment Account with ACH Interest Type results in a 200 OK response.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                    const accountData = {
                        "investor": investorId,
                        "name": `Automation_ScriptAccount_Pensioners ${uniqueSuffix}`,
                        "accountType": "Investment",
                        "startDate": startDate,
                        "endDate": null,
                        "rate": null,
                        "interestType": "ACH"
                    };
                    cy.api({
                        method: "POST",
                        url: "/accounts",
                        timeout: 60000,
                        headers: {
                            'Authorization': `<PERSON><PERSON> ${token}`
                        },
                        body: accountData
                    }).then((response) => {
                        expect(response.status).to.eq(201);

                        accountId = getCreatedEntityIdFromHeader(response.headers);
                        const responseBody = response.body;
                        expect(responseBody.id).to.eq(accountId);
                        expect(responseBody).to.have.property('investor', accountData.investor);
                        expect(responseBody).to.have.property('name', accountData.name);
                        expect(responseBody).to.have.property('accountType', accountData.accountType);
                        expect(responseBody).to.have.property('interestType', accountData.interestType);

                        if (accountData.accountType === 'Investment') {
                            expect(responseBody).to.have.property('rate').to.be.null;
                        } else {
                            expect(responseBody).to.have.property('rate', accountData.rate);
                        }

                    });
                });
            });
        });
    });
    it("Test Case 02: Verify that newly created account should be available in the response", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "GET",
                url: "/accounts",
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {

                expect(response.status).to.eq(200);
                const accounts = response.body;

                const stringAccountId = String(accountId);
                cy.wrap(accounts).should('satisfy', (accountsList) => {
                    return accountsList.some((account) => String(account.id) === stringAccountId);
                });
            });
        });
    });
});