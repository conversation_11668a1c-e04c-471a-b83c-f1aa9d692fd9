
import { getInvestorId } from '../../../fixtures/apihelperFunctions';

describe("Test Scenario: Validate Edit API Feature", () => {
    let investorId;
    let accountId;

    before(() => {
        getInvestorId().then((id) => {
            investorId = id;
        });
    });

    it("Test Case 01: Verify that All Account API should return data and save in variable for next response", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "GET",
                url: "/accounts",
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                expect(response.status).to.eq(200);
                accountId = response.body[1].id;
            });
        });
    });

    it('Test Case 02: Verify that the account successfully updates with valid data for all fields. ', { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                const accountData = {
                    "id": accountId,
                    "name": `Automation_ScriptAccount_Pensioners ${uniqueSuffix}`,
                    "investor": investorId,
                    "startDate": startDate,
                    "endDate": null,
                    "rate": 2,
                    "accountType": "Investment",
                    "interestType": "ACH"
                };
                cy.api({
                    method: "PUT",
                    url: `/accounts/${accountId}`,
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: accountData
                }).then((response) => {
                    expect(response.status).to.eq(200);
                    expect(response.body).to.have.property('id', accountData.id);
                    expect(response.body).to.have.property('name', accountData.name);
                    expect(response.body).to.have.property('investor', accountData.investor);
                    // expect(response.body).to.have.property('startDate', accountData.startDate);
                    expect(response.body).to.have.property('endDate', accountData.endDate);
                    //expect(response.body).to.have.property('rate', null);
                    expect(response.body).to.have.property('accountType', accountData.accountType);
                    expect(response.body).to.have.property('interestType', accountData.interestType);
                })
            });
        });
    });

    it('Test Case 03: Verify that an error is returned when the id in the URL does not match any existing account ', { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                const accountData = {
                    "id": 90001,
                    "name": `Automation_ScriptAccount_Pensioners ${uniqueSuffix}`,
                    "investor": investorId,
                    "startDate": startDate,
                    "endDate": null,
                    "rate": null,
                    "accountType": "Investment",
                    "interestType": "ACH"
                };
                cy.api({
                    method: "PUT",
                    url: '/accounts/90001',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: accountData,
                    failOnStatusCode: false
                }).then((response) => {
                    expect(response.status).to.eq(404);
                })
            });
        });
    });

    it('Test Case 04: Verify that an error is returned when the id in the URL does not match with body ID', { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                const accountData = {
                    "id": 90001,
                    "name": `Automation_ScriptAccount_Pensioners ${uniqueSuffix}`,
                    "investor": investorId,
                    "startDate": startDate,
                    "endDate": null,
                    "rate": null,
                    "accountType": "Investment",
                    "interestType": "ACH"
                };
                cy.api({
                    method: "PUT",
                    url: '/accounts/90002',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: accountData,
                    failOnStatusCode: false
                }).then((response) => {
                    expect(response.status).to.eq(400);
                    const responseBody = response.body;
                    expect(responseBody.title).to.include("One or more validation errors occurred.");
                    expect(responseBody.errors.id[0]).to.include("Id in the request body does not match the id in the url.");
                })
            });
        });
    });

    it('Test Case 05: Verify that empty strings for StartDate is not accepted', { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                const accountData = {
                    "id": accountId,
                    "name": `Automation_ScriptAccount_Pensioners ${uniqueSuffix}`,
                    "investor": investorId,
                    "startDate": "",
                    "endDate": null,
                    "rate": null,
                    "accountType": "Investment",
                    "interestType": "ACH"
                };
                cy.api({
                    method: "PUT",
                    url: `/accounts/${accountId}`,
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: accountData,
                    failOnStatusCode: false
                }).then((response) => {
                    expect(response.status).to.eq(400);
                    expect(response.body.errors).to.have.property('$.startDate');
                    expect(response.body.errors['$.startDate'][0]).to.contain('The JSON value could not be converted to System.DateTimeOffset.');
                })
            });
        });
    });

    it('Test Case 06: Verify that empty strings for endDate is not accepted', { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            const accountData = {
                "id": accountId,
                "name": "Automation_ScriptAccount_Pensioners savings bank account",
                "investor": investorId,
                "startDate": "2025-12-13T10:21:59.797+00:00",
                "endDate": "2025-12-13T10:21:59.797+00:00",
                "rate": null,
                "accountType": "Investment",
                "interestType": "ACH1"
            };
            cy.api({
                method: "PUT",
                url: `/accounts/${accountId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: accountData,
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(400);
            })
        });
    });

    it('Test Case 07: Verify that an invalid InterestType value (not "ACH) results in an error.', { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            const accountData = {
                "id": accountId,
                "name": "Automation_ScriptAccount_Pensioners savings bank account",
                "investor": investorId,
                "startDate": "2025-12-13T10:21:59.797+00:00",
                "endDate": "2025-12-13T10:21:59.797+00:00",
                "rate": null,
                "accountType": "Investment",
                "interestType": "ACH1"
            };
            cy.api({
                method: "PUT",
                url: `/accounts/${accountId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: accountData,
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(400);
                expect(response.body).to.have.property('type', 'https://tools.ietf.org/html/rfc9110#section-15.5.1');
                expect(response.body).to.have.property('title', 'One or more validation errors occurred.');
                expect(response.body.errors).to.have.property('InterestType');
                expect(response.body.errors.InterestType[0]).to.eq('ACH1 is not a valid value for InterestType.');
            })
        });
    });


    it('Test Case 08: Verify that the system returns an error when AccountType(Debit) is not one of the predefined types', { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            const accountData = {
                "id": accountId,
                "name": "Automation_ScriptAccount_Pensioners savings bank account",
                "investor": investorId,
                "startDate": "2025-12-13T10:21:59.797+00:00",
                "endDate": "2025-12-13T10:21:59.797+00:00",
                "rate": 2.5,
                "accountType": "Debit",
                "interestType": "ACH1"
            };
            cy.api({
                method: "PUT",
                url: `/accounts/${accountId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: accountData,
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(400);
                expect(response.body).to.have.property('type', 'https://tools.ietf.org/html/rfc9110#section-15.5.1');
                expect(response.body).to.have.property('title', 'One or more validation errors occurred.');
                expect(response.body.errors).to.have.property('AccountType');
                expect(response.body.errors.AccountType[0]).to.eq('Debit is not a valid value for AccountType.');
            })
        });
    });

    it("Test Case 09: Verify that update an account with a blank body results in a 405 status code", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                const accountData = {
                    "id": accountId
                };
                cy.api({
                    method: "PUT",
                    url: `/accounts/${accountId}`,
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: accountData,
                    failOnStatusCode: false
                }).then((response) => {
                    expect(response.status).to.eq(400);
                    expect(response.body.errors).to.have.all.keys('Name', 'AccountType');
                    expect(response.body.errors.Name).to.eql(['The Name field is required.']);
                    expect(response.body.errors.AccountType).to.eql(['The AccountType field is required.']);
                });
            });
        });
    });

    it("Test Case 10: Verify that an account is not edited when the investor field is missing, and ensure it returns a 400 status code", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                const accountData = {
                    "id": accountId,
                    "name": "Automation_ScriptAccount_Pensioners savings bank account",
                    //"investor": investorId,
                    "startDate": "2023-12-13T10:21:59.797+00:00",
                    "endDate": "2025-12-13T10:21:59.797+00:00",
                    "rate": null,
                    "accountType": "Investment",
                    "interestType": "ACH"
                };
                cy.api({
                    method: "PUT",
                    url: `/accounts/${accountId}`,
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: accountData,
                    failOnStatusCode: false
                }).then((response) => {
                    expect(response.status).to.eq(400);
                    expect(response.body.errors).to.have.property('Investor');
                    expect(response.body.errors.Investor).to.eql(['Investor with id 0 does not exist or is inactive.']);
                });
            });
        });
    });

    it("Test Case 11: Verify that an account updation attempt without the Name field results in a 400 status code", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                const accountData = {
                    "id": accountId,
                    //"name": "Automation_ScriptAccount_Pensioners savings bank account",
                    "investor": investorId,
                    "startDate": "2023-12-13T10:21:59.797+00:00",
                    "endDate": "2025-12-13T10:21:59.797+00:00",
                    "rate": null,
                    "accountType": "Investment",
                    "interestType": "ACH"
                };
                cy.api({
                    method: "PUT",
                    url: `/accounts/${accountId}`,
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: accountData,
                    failOnStatusCode: false
                }).then((response) => {
                    expect(response.status).to.eq(400);
                    expect(response.body.errors).to.have.all.keys('Name');
                    expect(response.body.errors.Name).to.eql(['The Name field is required.']);
                });
            });
        });
    });

    it("Test Case 12: Verify that an account is not updated when the Account field is missing, and ensure it returns a 400 status code.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                const accountData = {
                    "id": accountId,
                    "name": "Automation_ScriptAccount_Pensioners savings bank account",
                    "investor": investorId,
                    "startDate": "2023-12-13T10:21:59.797+00:00",
                    "endDate": "2025-12-13T10:21:59.797+00:00",
                    "rate": 2.5,
                    //"accountType": "Investment",
                    "interestType": "ACH"
                };
                cy.api({
                    method: "PUT",
                    url: `/accounts/${accountId}`,
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: accountData,
                    failOnStatusCode: false
                }).then((response) => {
                    expect(response.status).to.eq(400);
                    expect(response.body).to.have.property('status', 400);
                    expect(response.body.errors).to.have.property('AccountType');
                    expect(response.body.errors.AccountType).to.eql(['The AccountType field is required.']);
                });
            });
        });
    });

    it("Test Case 13: Verify that an account is not updated if the Account field is left blank, and confirm that it returns a 400 status code.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                const accountData = {
                    "id": accountId,
                    "name": "Automation_ScriptAccount_Pensioners savings bank account",
                    "investor": investorId,
                    "startDate": "2023-12-13T10:21:59.797+00:00",
                    "endDate": "2025-12-13T10:21:59.797+00:00",
                    "rate": 2.5,
                    "accountType": "",
                    "interestType": "ACH"
                };
                cy.api({
                    method: "PUT",
                    url: `/accounts/${accountId}`,
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: accountData,
                    failOnStatusCode: false
                }).then((response) => {
                    expect(response.status).to.eq(400);
                    expect(response.body).to.have.property('status', 400);
                    expect(response.body.errors).to.have.property('AccountType');
                    expect(response.body.errors.AccountType).to.include.members([
                        'The AccountType field is required.',
                        ' is not a valid value for AccountType.'
                    ]);
                });
            });
        });
    });
});