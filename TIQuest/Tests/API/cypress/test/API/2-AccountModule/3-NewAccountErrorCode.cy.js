
describe("Test Scenario: Validate the API error response for the validation of the Account API", () => {
    it("Test Case 01: Verify that creating an account with a blank body results in a 400 status code", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                const accountData = {
                };
                cy.api({
                    method: "POST",
                    url: "/accounts",
                    headers: {
                        'Authorization': `Bear<PERSON> ${token}`
                    },
                    body: accountData,
                    failOnStatusCode: false
                }).then((response) => {
                    expect(response.status).to.eq(400);
                    expect(response.body.errors).to.have.all.keys('Name', 'AccountType');
                    expect(response.body.errors.Name).to.eql(['The Name field is required.']);
                    expect(response.body.errors.AccountType).to.eql(['The AccountType field is required.']);
                });
            });
        });
    });

    it("Test Case 02: Verify that an account is not created when the investor field is missing, and ensure it returns a 400 status code", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                    const accountData = {
                        "name": `Automation_ScriptAccount_Pensioners ${uniqueSuffix}`,
                        "accountType": "Investment",
                        "startDate": startDate,
                        "endDate": null,
                        "rate": 2.5,
                        "interestType": "ACH"
                    };

                    cy.api({
                        method: "POST",
                        url: "/accounts",
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: accountData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(400);
                        expect(response.body.errors).to.have.property('Investor');
                        expect(response.body.errors.Investor).to.eql(['Investor with id 0 does not exist or is inactive.']);
                    });
                });
            });
        });
    });

    it("Test Case 03: Verify that an account creation attempt without the Name field results in a 400 status code", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                    const accountData = {
                        "investor": investorId,
                        "accountType": "Investment",
                        "startDate": startDate,
                        "endDate": null,
                        "rate": 2.5,
                        "interestType": "ACH"
                    };

                    cy.api({
                        method: "POST",
                        url: "/accounts",
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: accountData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(400);
                        expect(response.body.errors).to.have.all.keys('Name');
                        expect(response.body.errors.Name).to.eql(['The Name field is required.']);
                    });
                });
            });
        });
    });

    it("Test Case 04: Verify that an account is not created if the Name field is blank, and ensure it returns a 400 status code", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                    const accountData = {
                        "investor": investorId,
                        "name": "",
                        "accountType": "Investment",
                        "startDate": startDate,
                        "endDate": null,
                        "rate": 2.5,
                        "interestType": "ACH"
                    };

                    cy.api({
                        method: "POST",
                        url: "/accounts",
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: accountData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(400);
                        expect(response.body.errors).to.have.all.keys('Name');
                        expect(response.body.errors.Name).to.eql(['The Name field is required.']);
                    });
                });
            });
        });
    });


    it("Test Case 05: Verify that an account is not created when the Account field is missing, and ensure it returns a 400 status code.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                    const accountData = {
                        "investor": investorId,
                        "name": `Automation_ScriptAccount_Pensioners ${uniqueSuffix}`,
                        "startDate": startDate,
                        "endDate": null,
                        "rate": 2.5,
                        "interestType": "ACH"
                    };

                    cy.api({
                        method: "POST",
                        url: "/accounts",
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: accountData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(400);
                        expect(response.body).to.have.property('status', 400);
                        expect(response.body.errors).to.have.property('AccountType');
                        expect(response.body.errors.AccountType).to.eql(['The AccountType field is required.']);
                    });
                });
            })
        });
    });

    it("Test Case 06: Verify that an account is not created if the Account field is left blank, and confirm that it returns a 400 status code.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                    const accountData = {
                        "investor": investorId,
                        "name": `Automation_ScriptAccount_Pensioners ${uniqueSuffix}`,
                        "accountType": "",
                        "startDate": startDate,
                        "endDate": null,
                        "rate": 2.5,
                        "interestType": "ACH"
                    };

                    cy.api({
                        method: "POST",
                        url: "/accounts",
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: accountData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(400);
                        expect(response.body).to.have.property('status', 400);
                        expect(response.body.errors).to.have.property('AccountType');
                        expect(response.body.errors.AccountType).to.include.members([
                            'The AccountType field is required.',
                            ' is not a valid value for AccountType.'
                        ]);
                    });
                });
            });
        });
    });

    it("Test Case 07: Verify that an account is not created if the Account field contains invalid data, and ensure it returns a 400 status code.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                    const accountData = {
                        "investor": investorId,
                        "name": `Automation_ScriptAccount_Pensioners ${uniqueSuffix}`,
                        "accountType": "ABV",
                        "startDate": "2000-12-13T10:21:59.797+00:00",
                        "endDate": "2025-12-13T10:21:59.797+00:00",
                        "rate": 2.5,
                        "interestType": "ACH"
                    };

                    cy.api({
                        method: "POST",
                        url: "/accounts",
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: accountData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(400);
                        expect(response.body).to.have.property('status', 400);
                        expect(response.body.errors).to.have.property('AccountType');
                        expect(response.body.errors).to.have.property('AccountType');
                        expect(response.body.errors.AccountType).to.include.members([
                            'ABV is not a valid value for AccountType.'
                        ]);
                    });
                });
            });
        });
    });

    it("Test Case 08: Verify that an investment account is not created if it is missing the InterestType field, and confirm that a 400 status code is returned", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                    const accountData = {
                        "investor": investorId,
                        "name": `Automation_ScriptAccount_Pensioners ${uniqueSuffix}`,
                        "accountType": "Investment",
                        "startDate": startDate,
                        "endDate": null,
                        "rate": 2.5,
                    };

                    cy.api({
                        method: "POST",
                        url: "/accounts",
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: accountData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(400);
                        expect(response.body.errors).to.have.property('InterestType');
                        expect(response.body.errors.InterestType).to.include('InterestType must be provided for investment accounts.');
                    });
                });
            });
        });
    });

    it("Test Case 09: Verify that an account is not created if the InterestType value is invalid, and ensure it returns a 400 status code", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                    const accountData = {
                        "investor": investorId,
                        "name": `Automation_ScriptAccount_Pensioners ${uniqueSuffix}`,
                        "accountType": "Investment",
                        "startDate": startDate,
                        "endDate": null,
                        "rate": 2.5,
                        "interestType": "Test"
                    };

                    cy.api({
                        method: "POST",
                        url: "/accounts",
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: accountData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(400);
                        expect(response.body).to.have.property('status', 400);
                        expect(response.body.errors).to.have.property('InterestType');
                        expect(response.body.errors.InterestType).to.include.members([
                            'Test is not a valid value for InterestType.'
                        ]);
                    });
                });
            });
        });
    });

    it("Test Case 10: Verify that an account is not created if the Start date is later than the End date, and confirm that a 400 status code is returned.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                    const accountData = {
                        "investor": investorId,
                        "name": `Automation_ScriptAccount_Pensioners ${uniqueSuffix}`,
                        "accountType": "Investment",
                        "startDate": "2029-12-13T10:21:59.797+00:00",
                        "endDate": "2025-12-13T10:21:59.797+00:00",
                        "rate": 2.5,
                        "interestType": "ACH"
                    };

                    cy.api({
                        method: "POST",
                        url: "/accounts",
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: accountData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(400);
                        expect(response.body.errors).to.have.property('EndDate');
                        expect(response.body.errors.EndDate).to.include('EndDate must be greater than StartDate.');
                    });
                });
            });
        });
    });
});