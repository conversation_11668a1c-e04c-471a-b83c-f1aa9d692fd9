
describe("Test Scenario: Verify all test cases for the Get Account API", () => {
    it("Test Case 01: Verify the Get All Account API status should be 200", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "GET",
                url: "/accounts",
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                expect(response.status).to.eq(200);
                expect(response.body).to.be.an('array');
            });
        });
    });

    it("Test Case 02: Validates the structure and data types of each item", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "GET",
                url: "/accounts",
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {

                const sampleAccounts = response.body.slice(0, 10);
                sampleAccounts.forEach(item => {
                    expect(item).to.have.all.keys('id', 'investor', 'investorName', 'name', 'email', 'accountNumber', 'report1099Name', 'rate', 'balance', 'startDate', 'endDate', 'accountType', 'interestType', 'isActive', 'sendEmail','sendMail','taxNumber');
                    expect(item.id).to.be.a('number');
                    expect(item.investor).to.be.a('number');
                    expect(item.name).to.be.a('string');

                    expect(item.accountNumber).to.be.a('string');
                    expect(item.balance).to.be.a('number');
                    expect(item.accountType).to.be.oneOf(['Investment', 'Loan']);
                    expect(item.interestType).to.satisfy(type => type === null || type.toUpperCase() === 'ACH'
                        || type.toUpperCase() === 'CHECK' || type == 'Accrual');
                    expect(item.isActive).to.be.a('boolean');

                });
            });
        });
    });

    it("Test Case 03: Verify that if the accountType is 'Investment', the rate should be null.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "GET",
                url: "/accounts",
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            }).then((response) => {
                response.body.forEach(item => {
                    if (item.accountType === "Investment") {
                        expect(item.rate).to.satisfy(rate => rate === null || rate === 0 || rate === 2.5 || rate === 0.01 || rate === 2 || rate === 5);
                    }
                });
            });
        });
    }
    );

    it("Test Case 04: Verify that if the endDate is in the past, isActive should be set to false.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "GET",
                url: "/accounts",
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            }).then((response) => {
                response.body.forEach(item => {
                    //const endDate = new Date(item.endDate);
                    const endDate = item.endDate ? new Date(item.endDate) : null;
                    const currentDate = new Date();
                    if (endDate === null) {
                        expect(item.isActive).to.be.true;
                    } else if (endDate < currentDate) {
                        expect(item.isActive).to.be.false;
                    } else {
                        expect(item.isActive).to.be.true;
                    }
                });
            });
        });
    });

    it("Test Case 05: Verify that the endpoint identifies and rejects invalid tokens, returning an appropriate error status code of 401.", { tags: ['@PR', '@Smoke'] }, () => {
        cy.request({
            method: "GET",
            url: "/accounts",
            headers: {
                "Content-Type": "application/json",
                'Authorization': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Lre_7V73x2mj5ZcfsSBj2EUVJ_iuYacDck_M_DRqukA'
            },
            failOnStatusCode: false,
        }).then((response) => {
            expect(response.status).to.eq(401);
        });
    });

    it("Test Case 06: Verify the formats of startDate and endDate.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "GET",
                url: "/accounts",
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                expect(response.status).to.eq(200);
                expect(response.body).to.be.an('array');
                const dateTimeRegex = /^(null|\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?\+\d{2}:\d{2})$/;
                response.body.forEach(item => {
                    expect(item.startDate).to.match(dateTimeRegex, "startDate should match the expected format");
                    expect(item.endDate).to.match(dateTimeRegex, "endDate should match the expected format");
                });
            });
        });
    });
});