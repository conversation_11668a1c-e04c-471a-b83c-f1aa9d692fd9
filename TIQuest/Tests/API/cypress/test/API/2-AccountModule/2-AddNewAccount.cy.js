import { getCreatedEntityIdFromHeader } from "../../../fixtures/apihelperFunctions";

describe("Test Scenario: Verify the end-to-end flow for the Add Account Module.", () => {
    it("Test Case 01: Verify the creation of an Investment Account with ACH Interest Type results in a 201 Created response.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                    const accountData = {
                        "investor": investorId,
                        "name": `Automation_Account_Pensioners ${uniqueSuffix}`,
                        "report1099Name": "Test Report",
                        "accountType": "Investment",
                        "startDate": startDate,
                        "endDate": null,
                        "rate": null,
                        "interestType": "ACH"

                    };
                    cy.api({
                        method: "POST",
                        url: "/accounts",
                        timeout: 60000,
                        headers: {
                            'Authorization': `Bear<PERSON> ${token}`
                        },
                        body: accountData
                    }).then((response) => {
                        expect(response.status).to.eq(201);
                        const entityId = getCreatedEntityIdFromHeader(response.headers);
                        const responseBody = response.body;
                        expect(responseBody.id).to.eq(entityId);
                        expect(responseBody).to.have.property('investor', accountData.investor);
                        expect(responseBody).to.have.property('name', accountData.name);
                        expect(responseBody).to.have.property('accountType', accountData.accountType);
                        expect(responseBody).to.have.property('interestType', accountData.interestType);

                        if (accountData.accountType === 'Investment') {
                            expect(responseBody).to.have.property('rate').to.be.null;
                        } else {
                            expect(responseBody).to.have.property('rate', accountData.rate);
                        }

                    });
                });
            });
        });
    });
    it("Test Case 02: Verify the creation of an Investment Account with CHECK Interest Type results in a 200 OK response.", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                    const accountData = {
                        "investor": investorId,
                        "name": `Automation_Account_Pensioners ${uniqueSuffix}`,
                        "accountType": "Investment",
                        "startDate": startDate,
                        "endDate": null,
                        "rate": null,
                        "interestType": "CHECK",
                        "report1099Name": "Test Report"
                    };
                    cy.api({
                        method: "POST",
                        url: "/accounts",
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: accountData
                    }).then((response) => {
                        expect(response.status).to.eq(201);
                        const entityId = getCreatedEntityIdFromHeader(response.headers);
                        const responseBody = response.body;
                        expect(responseBody.id).to.eq(entityId);
                        expect(responseBody).to.have.property('investor', accountData.investor);
                        expect(responseBody).to.have.property('name', accountData.name);
                        expect(responseBody).to.have.property('accountType', accountData.accountType);
                        expect(responseBody).to.have.property('interestType', accountData.interestType);

                        if (accountData.accountType === 'Investment') {
                            expect(responseBody).to.have.property('rate').to.be.null;
                        } else {
                            expect(responseBody).to.have.property('rate', accountData.rate);
                        }

                    });
                });
            });
        });
    });

    it("Test Case 03: Verify the creation of an Investment Account with Accrual Interest Type results in a 200 OK response", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                    const accountData = {
                        "investor": investorId,
                        "name": `Automation_Account_Pensioners ${uniqueSuffix}`,
                        "accountType": "Investment",
                        "startDate": startDate,
                        "endDate": null,
                        "rate": null,
                        "interestType": "Accrual",
                        "report1099Name": "Test Report"
                    };
                    cy.api({
                        method: "POST",
                        url: "/accounts",
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: accountData
                    }).then((response) => {
                        expect(response.status).to.eq(201);
                        const entityId = getCreatedEntityIdFromHeader(response.headers);
                        const responseBody = response.body;
                        expect(responseBody.id).to.eq(entityId);
                        expect(responseBody).to.have.property('investor', accountData.investor);
                        expect(responseBody).to.have.property('name', accountData.name);
                        expect(responseBody).to.have.property('accountType', accountData.accountType);
                        expect(responseBody).to.have.property('interestType', accountData.interestType);

                        if (accountData.accountType === 'Investment') {
                            expect(responseBody).to.have.property('rate').to.be.null;
                        } else {
                            expect(responseBody).to.have.property('rate', accountData.rate);
                        }

                    });
                });
            });
        });
    });

    it("Test Case 04: Verify the creation of a Loan Account results in a 200 OK response", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.fetchInvestor(token).then((investorId) => {
                cy.generateDateAndSuffix().then(({ startDate, uniqueSuffix }) => {
                    const accountData = {
                        "investor": investorId,
                        "name": `Automation_Account_Pensioners ${uniqueSuffix}`,
                        "accountType": "Loan",
                        "startDate": startDate,
                        "endDate": null,
                        "rate": 5,
                        "interestType": null,
                        "report1099Name": "Test Report"
                    };
                    cy.api({
                        method: "POST",
                        url: "/accounts",
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: accountData
                    }).then((response) => {
                        expect(response.status).to.eq(201);

                        const entityId = getCreatedEntityIdFromHeader(response.headers);
                        const responseBody = response.body;
                        expect(responseBody.id).to.eq(entityId);
                        expect(responseBody).to.have.property('investor', accountData.investor);
                        expect(responseBody).to.have.property('name', accountData.name);
                        expect(responseBody).to.have.property('accountType', accountData.accountType);

                        if (accountData.accountType === 'Loan') {
                            expect(responseBody.interestType).to.be.null;
                        } else {
                            expect(responseBody.interestType).to.equal(accountData.interestType);
                        }

                        if (accountData.accountType === 'Investment') {
                            expect(responseBody).to.have.property('rate').to.be.null;
                        } else {
                            expect(responseBody).to.have.property('rate', accountData.rate);
                        }
                    });
                });
            });
        });
    });
});