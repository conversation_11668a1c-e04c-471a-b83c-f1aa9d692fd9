import { validateNullableProperties } from '../../../fixtures/apihelperFunctions';
describe("Test Scenario: Verify All cases for Investor List API", () => {
    it("Test Case 01: Verify the Investor API should return the List of All Investor", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "GET",
                url: "/investors",
                headers: {
                    'Authorization': `<PERSON><PERSON> ${token}`
                }
            }).then((response) => {
                expect(response.status).to.eq(200);
            });
        });
    });
    it("Test Case 02: Verify the Investor API response is an array of objects with specific properties", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "GET",
                url: "/investors",
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                assert.isArray(response.body, 'Response is an array');
                // Limit to the latest 20 records
                const recentInvestors = response.body.slice(0, 20);
                recentInvestors.forEach((investor) => {
                    expect(investor).to.be.an('object').and.to.include.keys(
                        'id', 'firstName', 'lastName', 'companyName', 'taxNumber', 'type',
                        'officePhone', 'homePhone', 'mobile', 'fax', 'apartment', 'address',
                        'city', 'state', 'zip', 'country', 'note', 'isActive'
                    );
    
                    expect(investor.id).to.be.a('number').and.not.to.be.null;
                    if (investor.type === 'Individual') {
                        expect(investor.firstName).to.be.a('string').and.not.to.be.null;
                        expect(investor.lastName).to.be.a('string').and.not.to.be.null;
                    }
                    if (investor.type === 'Company') {
                        expect(validateNullableProperties(investor.companyName, 'string')).to.be.true;
                    }
                    expect(investor.taxNumber).to.be.a('string').and.not.to.be.null;
                    expect(investor.type).to.be.a('string').and.not.to.be.null;
                    expect(validateNullableProperties(investor.officePhone, 'string')).to.be.true;
                    expect(validateNullableProperties(investor.homePhone, 'string')).to.be.true;
                    expect(validateNullableProperties(investor.mobile, 'string')).to.be.true;
                    expect(validateNullableProperties(investor.fax, 'string')).to.be.true;
                    expect(validateNullableProperties(investor.apartment, 'string')).to.be.true;
                    expect(validateNullableProperties(investor.address, 'string')).to.be.true;
                    expect(validateNullableProperties(investor.city, 'string')).to.be.true;
                    expect(validateNullableProperties(investor.state, 'string')).to.be.true;
                    expect(validateNullableProperties(investor.zip, 'string')).to.be.true;
                    expect(investor.country).to.be.a('string').and.not.to.be.null;
                    expect(validateNullableProperties(investor.note, 'string')).to.be.true;
                    expect(validateNullableProperties(investor.isActive, 'boolean')).to.be.true;
                });
            });
        });
    });
    

    it("Test Case 03: Verify the Investor API Response time is less than 5000ms", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            const startTime = new Date().getTime();
            cy.api({
                method: "GET",
                url: "/investors",
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                const endTime = new Date().getTime();
                const duration = endTime - startTime;
                expect(duration).to.be.lessThan(5000);
            });
        });
    });

    it("Test Case 04: Verify each investor's isActive property is a boolean and set to true or false", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "GET",
                url: "/investors",
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                assert.isArray(response.body, 'Response is an array');

                response.body.forEach((investor) => {
                    expect(investor.isActive).to.satisfy((isActive) => {
                        return typeof isActive === 'boolean';
                    }, 'isActive is a boolean');
                    expect(investor.isActive).to.be.oneOf([true, false], 'isActive is either true or false');
                });
            });
        });
    });

    it("Test Case 05: Verify each investor's ID is a non-negative integer", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "GET",
                url: "/investors",
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                assert.isArray(response.body, 'Response is an array');

                response.body.forEach((investor) => {
                    expect(investor.id, 'ID').to.satisfy(Number.isInteger, 'is an integer');
                    expect(investor.id, 'ID').to.be.at.least(0, 'is non-negative');

                });
            });
        });
    });

    it("Test Case 06: Verify each investor's name is a non-empty string", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "GET",
                url: "/investors",
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                assert.isArray(response.body, 'Response is an array');

                response.body.forEach((investor) => {
                    expect(investor.id, 'ID').to.satisfy(Number.isInteger, 'is an integer');
                    expect(investor.id, 'ID').to.be.at.least(0, 'is non-negative');

                });
            });
        });
    });
    it("Test Case 07: Verify the Investor API should not return the List of All Investor if Token is expired/Invalid", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.api({
                method: "GET",
                url: "/investors",
                headers: {
                    'Authorization': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Lre_7V73x2mj5ZcfsSBj2EUVJ_iuYacDck_M_DRqukA'
                },
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.eq(401);
            });
        });
    });
});