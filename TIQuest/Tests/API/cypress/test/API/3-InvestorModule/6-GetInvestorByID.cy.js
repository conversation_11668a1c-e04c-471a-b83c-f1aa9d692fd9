import { getInvestorId } from '../../../fixtures/apihelperFunctions';
describe("Test Scenario: Verify All Cases For Get Investors API", () => {

    let investorId;
    let token;

    before(() => {
        getInvestorId().then((id) => {
            investorId = id;
        });

        cy.login().then((authToken) => {
            token = authToken;
        });
    });

    it("Test Case 01: Verify the Investor API for List and Specific Investor", { tags: ["@PR", "@Smoke"] }, () => {

        cy.api({
            method: "GET",
            url: `/investors/${investorId}`,
            headers: {
                'Authorization': `Bearer ${token}`
            }
        }).then((response) => {
            expect(response.status).to.eq(200);
        });
    });

    it("Test Case 02: Verify the Investor API Response is an object with specific properties", { tags: ["@PR", "@Smoke"] }, () => {

        cy.api({
            method: "GET",
            url: `/investors/${investorId}`,
            headers: {
                'Authorization': `Bearer ${token}`
            }
        }).then((response) => {
            expect(response.body).to.be.an('object');
            expect(response.body).to.have.property('id');
            expect(response.body).to.have.property('firstName');
            expect(response.body).to.have.property('lastName');
            expect(response.body).to.have.property('state');
            expect(response.body).to.have.property('zip');
            expect(response.body).to.have.property('country');
            expect(response.body).to.have.property('note');
            expect(response.body).to.have.property('isActive');
        });
    });

    it("Test Case 03: Verify the Investor API Response time is less than 5000ms", { tags: ["@PR", "@Smoke"] }, () => {

        cy.api({
            method: "GET",
            url: `/investors/${investorId}`,
            headers: {
                'Authorization': `Bearer ${token}`
            }
        }).then((response) => {
            expect(response.duration).to.be.lessThan(5000);
        });
    });

    it("Test Case 04: Verify the Investor API Response 'Type' field should be one of the expected types", { tags: ["@PR", "@Smoke"] }, () => {
        const expectedTypes = ['individual', 'company'];

        cy.api({
            method: "GET",
            url: `/investors/${investorId}`,
            headers: {
                'Authorization': `Bearer ${token}`
            }
        }).then((response) => {
            expect(response.body).to.have.property('type');
            let responseType = response.body.type.toLowerCase();
            expect(responseType).to.be.oneOf(expectedTypes);
        });

    });

    it("Test Case 05: Verify the Investor API Response should return 401 if JWT token is missing or invalid ", { tags: ["@PR", "@Smoke"] }, () => {
        const expiredToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ppG-K1k-FL1sfym4r-57fZfVMySX6SlcmVlITbCjMMI";

        cy.api({
            method: "GET",
            url: `/investors/${investorId}`,
            headers: {
                'Authorization': `Bearer ${expiredToken}`
            },
            failOnStatusCode: false,
        }).then((response) => {
            expect(response.status).to.eq(401);
        });
    });

    it("Test Case 06: Verify that using a valid token (Non-Admin) results in a 403 Forbidden response", { tags: ["@PR", "@Smoke"] }, () => {

        cy.clientLogin().then((token) => {
            cy.api({
                method: "GET",
                url: `/investors/${investorId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.eq(403);
            });
        });
    });

    it("Test Case 07: Verify the Investor API Response should return 404 if id is not found", { tags: ["@PR", "@Smoke"] }, () => {

        cy.api({
            method: "GET",
            url: "/investors/234561",
            headers: {
                'Authorization': `Bearer ${token}`
            },
            failOnStatusCode: false,
        }).then((response) => {
            expect(response.status).to.eq(404);
        });
    });
});