import { createInvestor, createAccount } from '../../../fixtures/apihelperFunctions';

describe('Test Scenario: Verify account information for an investor', () => {
    let investorId;
    let accountResponseData;

    before(() => {
        cy.login().then((token) => {
            // Create an investor and obtain the investorId
            createInvestor(token).then((id) => {
                investorId = id;

                // Create an account for the investor and obtain the accountId
                createAccount(token, investorId).then((response) => {
                    accountResponseData = response.accountDetails;
                });
            });
        });
    });

    it('Test Case01: Verify that Investor Account information should match with actual account information', () => {
        cy.login().then((token) => {
            
            cy.api({
                method: "GET",
                url: `/investors/${investorId}/accounts`,
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((thirdApiResponse) => {
                expect(thirdApiResponse.status).to.eq(200);
                expect(thirdApiResponse.body[0]).to.deep.equal(accountResponseData);
            });
        });
    });
});
