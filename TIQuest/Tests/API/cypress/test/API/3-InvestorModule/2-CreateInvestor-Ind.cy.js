
import faker from 'faker';
import { validateNullableProperties, getCreatedEntityIdFromHeader } from '../../../fixtures/apihelperFunctions';
describe('Test Scenario: Verify All Test Cases for Create New Investor API', () => {

    let createInvestorResponse
    let createInvestorData;
    it('Test Case01: Verify that Create Investor API should successfully create a new investor - Type: Individual', () => {
        cy.login().then((token) => {
            createInvestorData = {
                firstName: faker.name.firstName(),
                lastName: faker.name.lastName(),
                companyName: faker.company.companyName(),
                taxNumber: faker.phone.phoneNumberFormat(),
                type: 'Individual',
                email: faker.internet.email(),
                officePhone: faker.phone.phoneNumberFormat(),
                homePhone: faker.phone.phoneNumberFormat(),
                mobile: faker.phone.phoneNumberFormat(),
                fax: faker.phone.phoneNumberFormat(),
                apartment: 'B-304',
                address: '47 W 13th St',
                city: 'New York',
                state: 'NY',
                zip: '10011',
                country: 'USA',
                note: 'This is Automation Investor',
                isActive: true
            };

            cy.api({
                method: "POST",
                url: "/investors",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: createInvestorData
            }).then((response) => {
                createInvestorResponse = response;
                expect(response.status).to.eq(201);
            });
        });
    });

    it('Test Case02: Verify that Create Investor API should have a valid JSON response', () => {
        expect(createInvestorResponse.body).to.not.be.empty;
        expect(createInvestorResponse.headers['content-type']).to.include('application/json');
    });

    it('Test Case03: Verify that Create Investor API should have the expected properties in the response body', () => {

        expect(createInvestorResponse.body).to.have.property('id');
        expect(createInvestorResponse.body).to.have.property('firstName');
        expect(createInvestorResponse.body).to.have.property('lastName');
        expect(createInvestorResponse.body).to.have.property('companyName');
        expect(createInvestorResponse.body).to.have.property('taxNumber');
        expect(createInvestorResponse.body).to.have.property('type');
        expect(createInvestorResponse.body).to.have.property('officePhone');
        expect(createInvestorResponse.body).to.have.property('homePhone');
        expect(createInvestorResponse.body).to.have.property('mobile');
        expect(createInvestorResponse.body).to.have.property('fax');
        expect(createInvestorResponse.body).to.have.property('apartment');
        expect(createInvestorResponse.body).to.have.property('address');
        expect(createInvestorResponse.body).to.have.property('city');
        expect(createInvestorResponse.body).to.have.property('state');
        expect(createInvestorResponse.body).to.have.property('zip');
        expect(createInvestorResponse.body).to.have.property('country');
        expect(createInvestorResponse.body).to.have.property('note');
        expect(createInvestorResponse.body).to.have.property('isActive');

    });

    it('Test Case04: Verify that Create Investor API should have the correct values in the response body', () => {
        expect(createInvestorResponse.body.firstName).to.equal(createInvestorData.firstName);
        expect(createInvestorResponse.body.lastName).to.equal(createInvestorData.lastName);
        expect(createInvestorResponse.body.type).to.equal(createInvestorData.type);
        expect(createInvestorResponse.body.officePhone).to.equal(createInvestorData.officePhone);
        expect(createInvestorResponse.body.homePhone).to.equal(createInvestorData.homePhone);
        expect(createInvestorResponse.body.mobile).to.equal(createInvestorData.mobile);
        expect(createInvestorResponse.body.fax).to.equal(createInvestorData.fax);
        expect(createInvestorResponse.body.apartment).to.equal(createInvestorData.apartment);
        expect(createInvestorResponse.body.address).to.equal(createInvestorData.address);
        expect(createInvestorResponse.body.city).to.equal(createInvestorData.city);
        expect(createInvestorResponse.body.state).to.equal(createInvestorData.state);
        expect(createInvestorResponse.body.zip).to.equal(createInvestorData.zip);
        expect(createInvestorResponse.body.country).to.equal(createInvestorData.country);
        expect(createInvestorResponse.body.note).to.equal(createInvestorData.note);
    });

    it('Test Case05: Verify the properties and data types of the Investor API response', () => {

        expect(createInvestorResponse.body).to.be.an('object');
        expect(createInvestorResponse.body).to.include.keys(
            'id', 'firstName', 'lastName', 'companyName', 'taxNumber', 'type',
            'officePhone', 'homePhone', 'mobile', 'fax', 'apartment', 'address',
            'city', 'state', 'zip', 'country', 'note', 'isActive'
        );
        expect(createInvestorResponse.body.id).to.be.a('number').and.not.to.be.null;
        const entityId = getCreatedEntityIdFromHeader(createInvestorResponse.headers);
        expect(createInvestorResponse.body.id).to.eq(entityId);
        expect(createInvestorResponse.body.firstName).to.be.a('string').and.not.to.be.null;
        expect(createInvestorResponse.body.lastName).to.be.a('string').and.not.to.be.null;
        expect(validateNullableProperties(createInvestorResponse.body.companyName, 'string')).to.be.true;
        expect(createInvestorResponse.body.taxNumber).to.be.a('string').and.not.to.be.null;
        expect(createInvestorResponse.body.type).to.be.a('string').and.not.to.be.null;
        expect(validateNullableProperties(createInvestorResponse.body.officePhone, 'string')).to.be.true;
        expect(validateNullableProperties(createInvestorResponse.body.homePhone, 'string')).to.be.true;
        expect(validateNullableProperties(createInvestorResponse.body.mobile, 'string')).to.be.true;
        expect(validateNullableProperties(createInvestorResponse.body.fax, 'string')).to.be.true;
        expect(validateNullableProperties(createInvestorResponse.body.apartment, 'string')).to.be.true;
        expect(createInvestorResponse.body.address).to.be.a('string').and.not.to.be.null;
        expect(validateNullableProperties(createInvestorResponse.body.city, 'string')).to.be.true;
        expect(validateNullableProperties(createInvestorResponse.body.state, 'string')).to.be.true;
        expect(validateNullableProperties(createInvestorResponse.body.zip, 'string')).to.be.true;
        expect(createInvestorResponse.body.country).to.be.a('string').and.not.to.be.null;
        expect(validateNullableProperties(createInvestorResponse.body.note, 'string')).to.be.true;
        expect(validateNullableProperties(createInvestorResponse.body.isActive, 'boolean')).to.be.true;
    });
});