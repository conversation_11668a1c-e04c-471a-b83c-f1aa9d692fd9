import faker from 'faker';
import { createInvestor, createAccount, createIndividualInvestor } from '../../../fixtures/apihelperFunctions';

describe('Test Scenario: Verify Edit Feature for an Investor API', () => {
    let investorId;
    let accountResponseData;

    before(() => {
        cy.login().then((token) => {
            // Create an investor and obtain the investorId
            createInvestor(token).then((id) => {
                investorId = id;

                // Create an account for the investor and obtain the accountId
                createAccount(token, investorId).then((response) => {
                    accountResponseData = response.accountDetails;
                });
            });
        });
    });

    it('Test Case01: Verify that Investor Account information should be edited without any error with valid data', () => {
        cy.login().then((token) => {

            const createInvestorData = {
                id: investorId,
                firstName: faker.name.firstName(),
                lastName: faker.name.lastName(),
                companyName: faker.company.companyName(),
                taxNumber: faker.phone.phoneNumberFormat(),
                type: 'Company',
                email: faker.internet.email(),
                officePhone: faker.phone.phoneNumberFormat(),
                homePhone: faker.phone.phoneNumberFormat(),
                mobile: faker.phone.phoneNumberFormat(),
                fax: faker.phone.phoneNumberFormat(),
                apartment: 'Edited B-304',
                address: 'Edited 47 W 13th St',
                city: 'New York',
                state: 'AZ',
                zip: '411057',
                country: 'USA',
                note: 'This is Automation Edited Investor',
                isActive: true
            };

            cy.api({
                method: "PUT",
                url: `/investors/${investorId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: createInvestorData

            }).then((thirdApiResponse) => {
                expect(thirdApiResponse.status).to.eq(200);
            });
        });
    });

    it('Test Case02: Verify that Individual Investor Account information should not be edited without First and Last Name', () => {
        cy.login().then((token) => {
            createIndividualInvestor().then((id) => {
                const createInvestorData = {
                    id: id,
                    //firstName: faker.name.firstName(),
                    //lastName: faker.name.lastName(),
                    companyName: faker.company.companyName(),
                    taxNumber: faker.phone.phoneNumberFormat(),
                    type: 'Individual',
                    email: faker.internet.email(),
                    officePhone: faker.phone.phoneNumberFormat(),
                    homePhone: faker.phone.phoneNumberFormat(),
                    mobile: faker.phone.phoneNumberFormat(),
                    fax: faker.phone.phoneNumberFormat(),
                    apartment: 'Edited B-304',
                    address: 'Edited 47 W 13th St',
                    city: 'New York',
                    state: 'AZ',
                    zip: '411057',
                    country: 'USA',
                    note: 'This is Automation Edited Investor',
                    isActive: true
                };

                cy.api({
                    method: "PUT",
                    url: `/investors/${id}`,
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: createInvestorData,
                    failOnStatusCode: false

                }).then((thirdApiResponse) => {
                    expect(thirdApiResponse.status).to.eq(400);
                    expect(thirdApiResponse.body.errors).to.have.property('LastName');
                    expect(thirdApiResponse.body.errors.LastName[0]).to.eq('Last name is required for individual type investors.');
                    expect(thirdApiResponse.body.errors).to.have.property('FirstName');
                    expect(thirdApiResponse.body.errors.FirstName[0]).to.eq('First name is required for individual type investors.');
                });
            });
        });
    });

    it('Test Case03: Verify that Company Investor Account information should not be edited without Company Name', () => {
        cy.login().then((token) => {

            const createInvestorData = {
                id: investorId,
                firstName: faker.name.firstName(),
                lastName: faker.name.lastName(),
                //companyName: faker.company.companyName(),
                taxNumber: faker.phone.phoneNumberFormat(),
                type: 'Company',
                email: faker.internet.email(),
                officePhone: faker.phone.phoneNumberFormat(),
                homePhone: faker.phone.phoneNumberFormat(),
                mobile: faker.phone.phoneNumberFormat(),
                fax: faker.phone.phoneNumberFormat(),
                apartment: 'Edited B-304',
                address: 'Edited 47 W 13th St',
                city: 'New York',
                state: 'AZ',
                zip: '411057',
                country: 'USA',
                note: 'This is Automation Edited Investor',
                isActive: true
            };

            cy.api({
                method: "PUT",
                url: `/investors/${investorId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: createInvestorData,
                failOnStatusCode: false

            }).then((thirdApiResponse) => {
                expect(thirdApiResponse.status).to.eq(400);
                expect(thirdApiResponse.body.errors).to.have.property('CompanyName');
                expect(thirdApiResponse.body.errors.CompanyName[0]).to.eq('Company name is required for company type investors.');
            });
        });
    });

    it('Test Case04: Verify that Investor Account information can be edited without Address', () => {
        cy.login().then((token) => {

            const createInvestorData = {
                id: investorId,
                firstName: faker.name.firstName(),
                lastName: faker.name.lastName(),
                companyName: faker.company.companyName(),
                taxNumber: faker.phone.phoneNumberFormat(),
                type: 'Company',
                email: faker.internet.email(),
                officePhone: faker.phone.phoneNumberFormat(),
                homePhone: faker.phone.phoneNumberFormat(),
                mobile: faker.phone.phoneNumberFormat(),
                fax: faker.phone.phoneNumberFormat(),
                apartment: 'Edited B-304',
                //address: 'Edited 47 W 13th St',
                city: 'New York',
                state: 'AZ',
                zip: '411057',
                country: 'USA',
                note: 'This is Automation Edited Investor',
                isActive: true
            };

            cy.api({
                method: "PUT",
                url: `/investors/${investorId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: createInvestorData,
                failOnStatusCode: false

            }).then((thirdApiResponse) => {
                expect(thirdApiResponse.status).to.eq(200);
            });
        });
    });

    it('Test Case05: Verify that Investor Account information should not be edited without Country Name', () => {
        cy.login().then((token) => {

            const createInvestorData = {
                id: investorId,
                firstName: faker.name.firstName(),
                lastName: faker.name.lastName(),
                companyName: faker.company.companyName(),
                taxNumber: faker.phone.phoneNumberFormat(),
                type: 'Company',
                email: faker.internet.email(),
                officePhone: faker.phone.phoneNumberFormat(),
                homePhone: faker.phone.phoneNumberFormat(),
                mobile: faker.phone.phoneNumberFormat(),
                fax: faker.phone.phoneNumberFormat(),
                apartment: 'Edited B-304',
                address: 'Edited 47 W 13th St',
                city: 'New York',
                state: 'AZ',
                zip: '411057',
                //country: 'USA',
                note: 'This is Automation Edited Investor',
                isActive: true
            };

            cy.api({
                method: "PUT",
                url: `/investors/${investorId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: createInvestorData,
                failOnStatusCode: false

            }).then((thirdApiResponse) => {
                expect(thirdApiResponse.status).to.eq(400);
                expect(thirdApiResponse.body.errors).to.have.property('Country');
                expect(thirdApiResponse.body.errors.Country[0]).to.eq('The Country field is required.');
            });
        });
    });
});
