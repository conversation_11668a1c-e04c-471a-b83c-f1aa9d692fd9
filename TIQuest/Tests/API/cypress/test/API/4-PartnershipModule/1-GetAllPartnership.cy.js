describe("Test Scenario: Verify all test cases for the Get All Partnership API", () => {

    let response;
    beforeEach(() => {
        cy.login().then((token) => {
            cy.api({
                method: "GET",
                url: "/partnerships",
                headers: {
                    'Authorization': `<PERSON><PERSON> ${token}`
                }
            }).then((res) => {
                response = res;
            });
        });
    });

    it("Test Case 01: Verify the response status is 200", { tags: ["@PR", "@Smoke"] }, () => {
        expect(response.status).to.eq(200);
    });

    it("Test Case 02: Verify the response is an array of objects", { tags: ["@PR", "@Smoke"] }, () => {
        expect(response.body).to.be.an('array').that.is.not.empty;
    });

    it("Test Case 03: Verify the response has the correct Content-Type", { tags: ["@PR", "@Smoke"] }, () => {
        expect(response.headers['content-type']).to.include('application/json');
    });

    it("Test Case 04: Verify each object in the response has a valid ID", { tags: ["@PR", "@Smoke"] }, () => {
        response.body.forEach((partnership) => {
            expect(partnership.id).to.be.a('number');
            expect(partnership.id).to.be.greaterThan(0);
        });
    });

    it("Test Case 05: Verify each object has a non-empty name", { tags: ["@PR", "@Smoke"] }, () => {
        response.body.forEach((partnership) => {
            expect(partnership.name).to.exist;
            expect(partnership.name).to.have.length.greaterThan(0);
        });
    });

    it("Test Case 06: Verify each object has an isActive property of boolean type", { tags: ["@PR", "@Smoke"] }, () => {
        response.body.forEach((partnership) => {
            expect(partnership.isActive).to.be.a('boolean');
        });
    });

    it("Test Case 07: Verify each object has a description field", { tags: ["@PR", "@Smoke"] }, () => {
        response.body.forEach((partnership) => {
            expect(partnership).to.have.property('description');
        });
    });

    it("Test Case 08: Verify response time is less than 5000ms", { tags: ["@PR", "@Smoke"] }, () => {
        expect(response.duration).to.be.lessThan(5000);
    });
});