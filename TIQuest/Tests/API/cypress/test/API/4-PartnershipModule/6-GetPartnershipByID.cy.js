import { createAndGetLastPartnershipId } from '../../../fixtures/apihelperFunctions';

describe('Test Scenario: Verify test cases for the Get Partnership Detail by Partnership ID', () => {
    let token;

    before(() => {
        cy.login().then((authToken) => {
            token = authToken;
        });
    });

    it('Test Case01: Verify response status code is 200', () => {
        createAndGetLastPartnershipId(token).then((lastPartnershipId) => {
            cy.api({
                method: "GET",
                url: `/partnerships/${lastPartnershipId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                expect(response.status).to.eq(200);
            });
        });
    });

    it('Test Case02: Verify response object includes all required keys', { tags: ["@PR", "@Smoke"] }, () => {
        createAndGetLastPartnershipId().then((lastPartnershipId) => {
            cy.api({
                method: "GET",
                url: `/partnerships/${lastPartnershipId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                expect(response.body).to.be.an('object').and.to.include.all.keys('id', 'name', 'description', 'apartment', 'address', 'city', 'state', 'zip', 'country', 'isActive');
            });
        });
    })

    it('Test Case03: Verify response Content-Type is application/json', { tags: ["@PR", "@Smoke"] }, () => {
        createAndGetLastPartnershipId().then((lastPartnershipId) => {
            cy.api({
                method: "GET",
                url: `/partnerships/${lastPartnershipId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                expect(response.headers['content-type']).to.include('application/json');
            });
        });
    });

    it('Test Case04: Verify Id is a non-negative integer', () => {
        createAndGetLastPartnershipId().then((lastPartnershipId) => {
            cy.api({
                method: "GET",
                url: `/partnerships/${lastPartnershipId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                expect(response.body.id).to.be.a('number').and.to.be.at.least(0);
            });
        });
    });

    it('Test Case05: Verify Name is a non-empty string', () => {
        createAndGetLastPartnershipId().then((lastPartnershipId) => {
            cy.api({
                method: "GET",
                url: `/partnerships/${lastPartnershipId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                expect(response.body.name).to.be.a('string').and.to.have.lengthOf.at.least(1);
            });
        });
    });

    it('Test Case06: Verify isActive is a boolean value', () => {
        createAndGetLastPartnershipId().then((lastPartnershipId) => {
            cy.api({
                method: "GET",
                url: `/partnerships/${lastPartnershipId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                expect(response.body.isActive).to.be.a('boolean');
            });
        });
    });

    it('Test Case07: Verify Description is a non-empty string', () => {
        createAndGetLastPartnershipId().then((lastPartnershipId) => {
            cy.api({
                method: "GET",
                url: `/partnerships/${lastPartnershipId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                expect(response.body.description).to.be.a('string').and.to.have.lengthOf.at.least(1);
            });
        });
    });

    it('Test Case08: Verify isActive property should be present and not null or undefined', () => {
        createAndGetLastPartnershipId().then((lastPartnershipId) => {
            cy.api({
                method: "GET",
                url: `/partnerships/${lastPartnershipId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                expect(response.body).to.have.property('isActive').that.is.not.null.and.not.undefined;
            });
        });
    });

    it(`Test Case09: Verify 'apartment' property is either null or a non-empty string`, () => {
        createAndGetLastPartnershipId().then((lastPartnershipId) => {
            cy.api({
                method: "GET",
                url: `/partnerships/${lastPartnershipId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                expect(response.body).to.have.property('apartment').that.satisfies((apartment) => {
                    return apartment === null || typeof apartment === 'string' && apartment.length > 0;
                });
            });
        });
    });

    it('Test Case10: Verify Id is a non-null value', () => {
        createAndGetLastPartnershipId().then((lastPartnershipId) => {
            cy.api({
                method: "GET",
                url: `/partnerships/${lastPartnershipId}`,
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                expect(response.body.id).to.exist.and.to.not.be.null;
            });
        });
    });

    it('Test Case11: Verify that New Partnership should be created successfully without any error', () => {
        createAndGetLastPartnershipId().then((id) => {
            expect(id).to.be.a('number');
        });
    });
});