import { createAndGetLastPartnershipId } from '../../../fixtures/apihelperFunctions';

describe('Test Scenario: Verify all test cases for Remove Investors To Partnership', () => {

    let activeId = null;
    let token;

    before(() => {
        cy.login().then((authToken) => {
            token = authToken;
        });
    });

    it('Test Case01: Remove an Investor From New Partnership and Verifies the Response', { tags: ["@PR", "@Smoke"] }, () => {
        cy.fetchInvestor(token).then((investorId) => {
            cy.api({
                method: "GET",
                url: `/investors/${investorId}/accounts`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(200);
                for (let account of response.body) {
                    if (account.isActive) {
                        activeId = account.id;
                        break;
                    }
                }

                const addInvestorData = {
                    account: activeId,
                    percentage: 50
                };
                createAndGetLastPartnershipId(token).then((newPartnershipId) => {
                    cy.api({
                        method: "POST",
                        url: `/partnerships/${newPartnershipId}/investors`,
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: addInvestorData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(204);
                    });

                    cy.api({
                        method: "GET",
                        url: `/partnerships/${newPartnershipId}/investors`,
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        failOnStatusCode: false
                    }).then((response) => {
                        response.status = 200;
                        expect(response.duration).to.be.lessThan(5000);

                        const responseBody = response.body;

                        const partnership = responseBody[0].partnership;
                        const accountId = responseBody[0].account.id;

                        cy.log('Partnership:', partnership);
                        cy.log('Account ID:', accountId);

                        cy.api({
                            method: "DELETE",
                            url: `/partnerships/${partnership}/investors/${accountId}`,
                            headers: {
                                'Authorization': `Bearer ${token}`
                            },
                            failOnStatusCode: false
                        }).then((response) => {
                            response.status = 204;
                        });
                    });
                });
            });
        });
    });
});