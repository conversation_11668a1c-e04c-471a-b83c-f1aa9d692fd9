import faker from 'faker';
describe('Test Scenario: Verify test cases for the Edit Partnership', () => {
    let token;
    let createPartnershipResponse;
    let newPartnershipId;
    let firstApiResponse;
    let secondApiResponse;

    beforeEach(() => {
        cy.login().then((authToken) => {
            token = authToken;
        });
    });

    it('Test Case01: Verify that Newly Created Partnership API should successfully edited without any error', { tags: ["@PR", "@Smoke"] }, () => {
        const createPartnershipData = {
            name: faker.name.firstName(),
            description: 'This is Automation Partnership',
            apartment: 'B-304',
            address: '47 W 13th St',
            city: 'New York',
            state: 'NY',
            zip: '10011',
            country: 'USA'
        };

        cy.api({
            method: "POST",
            url: "/partnerships",
            headers: {
                'Authorization': `Bearer ${token}`
            },
            body: createPartnershipData
        }).then((response) => {
            createPartnershipResponse = response;
            expect(createPartnershipResponse.status).to.eq(201);
            expect(response.body).to.have.property('id');
            newPartnershipId = response.body.id;
            cy.log('Created Partnership ID:', newPartnershipId);

            const editPartnershipData = {
                id: newPartnershipId,
                name: faker.name.firstName(),
                description: 'This is Edited Automation Partnerships',
                apartment: 'B-304',
                address: '47 W 13th St',
                city: 'New York',
                state: 'NY',
                zip: '10011',
                country: 'USA'
            };
            cy.api({
                method: "PUT",
                url: "/partnerships",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: editPartnershipData
            }).then((response) => {
                expect(response.status).to.eq(200);
                firstApiResponse = response.body;

                cy.api({
                    method: "GET",
                    url: `/partnerships/${newPartnershipId}`,
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                }).then((res) => {
                    response = res;
                    response.status = 200;
                    var jsonData = response.body;
                    secondApiResponse = response.body;
                    expect(secondApiResponse).to.deep.equal(firstApiResponse);
                    expect(response.duration).to.be.lessThan(5000);
                });
            });
        });
    });
});