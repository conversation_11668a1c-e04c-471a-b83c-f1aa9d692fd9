import faker from 'faker';
import { getCreatedEntityIdFromHeader } from '../../../fixtures/apihelperFunctions';
describe('Test Scenario: Verify All Test Cases for Create New Partnership API', () => {

    let createPartnershipResponse
    let createPartnershipData;

    it('Test Case01: Verify that Create Partnership API should successfully create a new Partnership', { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            createPartnershipData = {
                name: faker.name.firstName(),
                description: 'This is Automation Partnership',
                apartment: 'B-304',
                address: '47 W 13th St',
                city: 'New York',
                state: 'NY',
                zip: '10011',
                country: 'USA'
            };

            cy.api({
                method: "POST",
                url: "/partnerships",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: createPartnershipData
            }).then((response) => {
                createPartnershipResponse = response;
                expect(createPartnershipResponse.status).to.eq(201);
                expect(createPartnershipResponse.duration).to.be.lessThan(5000);
                const entityId = getCreatedEntityIdFromHeader(response.headers);
                expect(response.body.id).to.eq(entityId);
            });
        });
    });
});