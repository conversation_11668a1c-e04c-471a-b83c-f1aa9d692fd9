import { createAndGetLastPartnershipId, getInvestorId } from '../../../fixtures/apihelperFunctions';

describe('Test Scenario: Verify all test cases for Add Investors To Partnership', () => {

    let activeId = null;
    let token;

    before(() => {
        cy.login().then((authToken) => {
            token = authToken;
        });
    });

    it('Test Case01: Adds an Investor to a New Partnership and Verifies the Response', { tags: ["@PR", "@Smoke"] }, () => {
        cy.fetchInvestor(token).then((investorId) => {
            cy.api({
                method: "GET",
                url: `/investors/${investorId}/accounts`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(200);
                for (let account of response.body) {
                    if (account.isActive) {
                        activeId = account.id;
                        break;
                    }
                }

                const addInvestorData = {
                    account: activeId,
                    percentage: 50
                };
                createAndGetLastPartnershipId(token).then((newPartnershipId) => {
                    cy.api({
                        method: "POST",
                        url: `/partnerships/${newPartnershipId}/investors`,
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: addInvestorData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(204);
                    });

                    cy.api({
                        method: "GET",
                        url: `/partnerships/${newPartnershipId}/investors`,
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        failOnStatusCode: false
                    }).then((response) => {
                        response.status = 200;
                        expect(response.duration).to.be.lessThan(500);
                    });
                });
            });
        });
    });

    it('Test Case02: Total percentage of ownership cannot be greater than 100 - For One Investor', { tags: ["@PR", "@Smoke"] }, () => {
        cy.fetchInvestor(token).then((investorId) => {
            cy.api({
                method: "GET",
                url: `/investors/${investorId}/accounts`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(200);
                for (let account of response.body) {
                    if (account.isActive) {
                        activeId = account.id;
                        break;
                    }
                }

                const addInvestorData = {
                    account: activeId,
                    percentage: 100
                };
                createAndGetLastPartnershipId(token).then((newPartnershipId) => {
                    cy.api({
                        method: "POST",
                        url: `/partnerships/${newPartnershipId}/investors`,
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: addInvestorData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(400);
                        expect(response.body).to.have.property('status', 400);
                        expect(response.body.errors).to.have.property('Percentage');
                        expect(response.body.errors.Percentage).to.include('Percentage must be greater than 0 and less than 100');
                    });
                });
            });
        });
    });

    it('Test Case03: Verify that Percentage Owned Field is required to Add Partners to Partnership', { tags: ["@PR", "@Smoke"] }, () => {
        cy.fetchInvestor(token).then((investorId) => {
            cy.api({
                method: "GET",
                url: `/investors/${investorId}/accounts`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(200);
                for (let account of response.body) {
                    if (account.isActive) {
                        activeId = account.id;
                        break;
                    }
                }

                const addInvestorData = {
                    account: activeId,
                    //percentage: 100
                };
                createAndGetLastPartnershipId(token).then((newPartnershipId) => {
                    cy.api({
                        method: "POST",
                        url: `/partnerships/${newPartnershipId}/investors`,
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: addInvestorData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(400);
                    });
                });
            });
        });
    });

    it('Test Case04: Verify that Account Field is required to Add Partners to Partnership', { tags: ["@PR", "@Smoke"] }, () => {
        cy.fetchInvestor(token).then((investorId) => {
            cy.api({
                method: "GET",
                url: `/investors/${investorId}/accounts`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(200);
                for (let account of response.body) {
                    if (account.isActive) {
                        activeId = account.id;
                        break;
                    }
                }

                const addInvestorData = {
                    //account: activeId,
                    percentage: 50
                };
                createAndGetLastPartnershipId(token).then((newPartnershipId) => {
                    cy.api({
                        method: "POST",
                        url: `/partnerships/${newPartnershipId}/investors`,
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: addInvestorData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(400);
                    });
                });
            });
        });
    });

    it('Test Case05: Verify the Investor API Response should return 401 if JWT token is missing or invalid ', { tags: ["@PR", "@Smoke"] }, () => {
        cy.fetchInvestor(token).then((investorId) => {
            cy.api({
                method: "GET",
                url: `/investors/${investorId}/accounts`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(200);
                for (let account of response.body) {
                    if (account.isActive) {
                        activeId = account.id;
                        break;
                    }
                }

                const expiredToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ppG-K1k-FL1sfym4r-57fZfVMySX6SlcmVlITbCjMMI";
                const addInvestorData = {
                    account: activeId,
                    percentage: 50
                };
                createAndGetLastPartnershipId(token).then((newPartnershipId) => {
                    cy.api({
                        method: "POST",
                        url: `/partnerships/${newPartnershipId}/investors`,
                        headers: {
                            'Authorization': `Bearer ${expiredToken}`
                        },
                        body: addInvestorData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(401);
                    });
                });
            });
        });
    });

    it('Test Case06: Verify that using a valid token (Non-Admin) results in a 403 Forbidden response', { tags: ["@PR", "@Smoke"] }, () => {
        cy.fetchInvestor(token).then((investorId) => {
            cy.api({
                method: "GET",
                url: `/investors/${investorId}/accounts`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(200);
                for (let account of response.body) {
                    if (account.isActive) {
                        activeId = account.id;
                        break;
                    }
                }

                const addInvestorData = {
                    account: activeId,
                    percentage: 50
                };
                createAndGetLastPartnershipId(token).then((newPartnershipId) => {
                    cy.clientLogin().then((token) => {
                        cy.api({
                            method: "POST",
                            url: `/partnerships/${newPartnershipId}/investors`,
                            headers: {
                                'Authorization': `Bearer ${token}`
                            },
                            body: addInvestorData,
                            failOnStatusCode: false
                        }).then((response) => {
                            expect(response.status).to.eq(403);
                        });
                    });
                });
            });
        });
    });

    it('Test Case07: Verify the Add Partnership API Response should return 404 if id is not found', { tags: ["@PR", "@Smoke"] }, () => {
        cy.fetchInvestor(token).then((investorId) => {
            cy.api({
                method: "GET",
                url: `/investors/${investorId}/accounts`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(200);
                for (let account of response.body) {
                    if (account.isActive) {
                        activeId = account.id;
                        break;
                    }
                }

                const addInvestorData = {
                    account: activeId,
                    percentage: 50
                };
                createAndGetLastPartnershipId(token).then((newPartnershipId) => {
                    cy.api({
                        method: "POST",
                        url: `/partnerships/${newPartnershipId}/investors`,
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: addInvestorData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(204);
                    });

                    cy.api({
                        method: "GET",
                        url: "/partnerships/234561/investors",
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        failOnStatusCode: false
                    }).then((response) => {
                        response.status = 200;
                        expect(response.duration).to.be.lessThan(5000);
                    });
                });
            });
        });
    });

    it('Test Case08: Adds an same Investor multiple time to a New Partnership and Verifies the Response', { tags: ["@PR", "@Smoke"] }, () => {
        cy.fetchInvestor(token).then((investorId) => {
            cy.api({
                method: "GET",
                url: `/investors/${investorId}/accounts`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(200);
                for (let account of response.body) {
                    if (account.isActive) {
                        activeId = account.id;
                        break;
                    }
                }

                const addInvestorData = {
                    account: activeId,
                    percentage: 50
                };
                createAndGetLastPartnershipId(token).then((newPartnershipId) => {
                    cy.api({
                        method: "POST",
                        url: `/partnerships/${newPartnershipId}/investors`,
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: addInvestorData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(204);

                        cy.api({
                            method: "POST",
                            url: `/partnerships/${newPartnershipId}/investors`,
                            headers: {
                                'Authorization': `Bearer ${token}`
                            },
                            body: addInvestorData,
                            failOnStatusCode: false
                        }).then((response) => {
                            expect(response.status).to.eq(400);
                        });
                    });
                });
            });
        });
    });

    it('Test Case09: Adds an Investor to a New Partnership and Verifies the Response', { tags: ["@PR", "@Smoke"] }, () => {
        cy.fetchInvestor(token).then((investorId) => {
            cy.api({
                method: "GET",
                url: `/investors/${investorId}/accounts`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(200);
                for (let account of response.body) {
                    if (account.isActive) {
                        activeId = account.id;
                        break;
                    }
                }

                const addInvestorData = {
                    account: activeId,
                    percentage: 50
                };
                createAndGetLastPartnershipId(token).then((newPartnershipId) => {
                    cy.api({
                        method: "POST",
                        url: `/partnerships/${newPartnershipId}/investors`,
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'content-type': 'application/text'
                        },
                        body: addInvestorData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(415);
                    });
                });
            });
        });
    });

    it('Test Case10: Verify that API response and status code with empty body', { tags: ["@PR", "@Smoke"] }, () => {
        cy.fetchInvestor(token).then((investorId) => {
            cy.api({
                method: "GET",
                url: `/investors/${investorId}/accounts`,
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(200);
                for (let account of response.body) {
                    if (account.isActive) {
                        activeId = account.id;
                        break;
                    }
                }

                const addInvestorData = {

                };
                createAndGetLastPartnershipId(token).then((newPartnershipId) => {
                    cy.api({
                        method: "POST",
                        url: `/partnerships/${newPartnershipId}/investors`,
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: addInvestorData,
                        failOnStatusCode: false
                    }).then((response) => {
                        expect(response.status).to.eq(400);
                    });
                });
            });
        });
    });
});
