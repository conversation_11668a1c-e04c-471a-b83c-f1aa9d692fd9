import faker from 'faker';
describe('Test Scenario: Verify All Test Cases for Create New Partnership API', () => {
    let createPartnershipResponse
    let createPartnershipData;
    it('Test Case01: Verify that Create Partnership API should not create a new Partnership if the Name is blank', { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            createPartnershipData = {
                //name: faker.name.firstName(),
                description: 'This is Automation Partnership',
                apartment: 'B-304',
                address: '47 W 13th St',
                city: 'New York',
                state: 'NY',
                zip: '10011',
                country: 'USA'
            };
            cy.api({
                method: "POST",
                url: "/partnerships",
                headers: {
                    'Authorization': `Bear<PERSON> ${token}`
                },
                body: createPartnershipData,
                failOnStatusCode: false,
            }).then((response) => {
                createPartnershipResponse = response;
                expect(response.status).to.eq(400);
                expect(createPartnershipResponse.body).to.have.property('type', 'https://tools.ietf.org/html/rfc9110#section-15.5.1');
                expect(createPartnershipResponse.body).to.have.property('title', 'One or more validation errors occurred.');
                expect(createPartnershipResponse.body).to.have.property('status', 400);
                expect(createPartnershipResponse.body).to.have.property('traceId');
                expect(createPartnershipResponse.body.errors).to.have.property('Name');
                expect(createPartnershipResponse.body.errors.Name).to.include('The Name field is required.');
            });
        });
    });

    it('Test Case02: Verify that Create Partnership API should not create a new Partnership if the Address is blank', { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            createPartnershipData = {
                name: faker.name.firstName(),
                description: 'This is Automation Partnership',
                apartment: 'B-304',
                //address: '47 W 13th St',
                city: 'New York',
                state: 'NY',
                zip: '10011',
                country: 'USA'
            };
            cy.api({
                method: "POST",
                url: "/partnerships",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: createPartnershipData,
                failOnStatusCode: false,
            }).then((response) => {
                createPartnershipResponse = response;
                expect(response.status).to.eq(201);
                expect(response.body).to.not.be.null;
                // TODO: Verify properties of body
            });
        });
    });

    it('Test Case03: Verify that Create Partnership API should not create a new Partnership Body is blank', { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            createPartnershipData = {
            };
            cy.api({
                method: "POST",
                url: "/partnerships",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: createPartnershipData,
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.eq(400);
                expect(response.body).to.have.property('type', 'https://tools.ietf.org/html/rfc9110#section-15.5.1');
                expect(response.body).to.have.property('title', 'One or more validation errors occurred.');
                expect(response.body).to.have.property('status', 400);
                expect(response.body).to.have.property('traceId');
                expect(response.body.errors).to.have.property('Name');
                expect(response.body.errors.Name).to.include('The Name field is required.');
                expect(response.body.errors).to.have.property('Country');
                expect(response.body.errors.Country).to.include('The Country field is required.');
            });
        });
    });

    it("Test Case 04: Verify that using an expired token results in a 401 Unauthorized response", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            const expiredToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NH2bIjs3tgAm7ri6FTId8P4FdSDNqun-PgaLbJJ1GRI";

            createPartnershipData = {
                name: faker.name.firstName(),
                description: 'This is Automation Partnership',
                apartment: 'B-304',
                address: '47 W 13th St',
                city: 'New York',
                state: 'NY',
                zip: '10011',
                country: 'USA'
            };
            cy.api({
                method: "POST",
                url: "/partnerships",
                headers: {
                    'Authorization': `Bearer ${expiredToken}`
                },
                body: createPartnershipData,
                failOnStatusCode: false,
            }).then((response) => {
                createPartnershipResponse = response;
                expect(createPartnershipResponse.status).to.eq(401);
            });
        });
    });

    it("Test Case 05: Verify that using an empty token results in a 401 Unauthorized response", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            const expiredToken = "";

            createPartnershipData = {
                name: faker.name.firstName(),
                description: 'This is Automation Partnership',
                apartment: 'B-304',
                address: '47 W 13th St',
                city: 'New York',
                state: 'NY',
                zip: '10011',
                country: 'USA'
            };
            cy.api({
                method: "POST",
                url: "/partnerships",
                headers: {
                    'Authorization': `Bearer ${expiredToken}`
                },
                body: createPartnershipData,
                failOnStatusCode: false,
            }).then((response) => {
                createPartnershipResponse = response;
                expect(createPartnershipResponse.status).to.eq(401);
            });
        });
    });

    it("Test Case 06:  Verify that using a valid token (Non-Admin) results in a 403 Forbidden response", { tags: ["@PR", "@Smoke"] }, () => {
        cy.clientLogin().then((token) => {
            createPartnershipData = {
                name: faker.name.firstName(),
                description: 'This is Automation Partnership',
                apartment: 'B-304',
                address: '47 W 13th St',
                city: 'New York',
                state: 'NY',
                zip: '10011',
                country: 'USA'
            };
            cy.api({
                method: "POST",
                url: "/partnerships",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: createPartnershipData,
                failOnStatusCode: false,
            }).then((response) => {
                createPartnershipResponse = response;
                expect(createPartnershipResponse.status).to.eq(403);
            });
        });
    });
});