describe("Test Scenario: Verify Authorization Error Responses for the Get All Partnership API", () => {
    it("Test Case 01: Verify that using an expired token results in a 401 Unauthorized response", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            const expiredToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NH2bIjs3tgAm7ri6FTId8P4FdSDNqun-PgaLbJJ1GRI";
            cy.api({
                method: "GET",
                url: "/partnerships",
                headers: {
                    'Authorization': `Bearer ${expiredToken}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(401);
            });
        });
    });

    it("Test Case 02: Verify that using an empty token results in a 401 Unauthorized response", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            const expiredToken = "";
            cy.api({
                method: "GET",
                url: "/partnerships",
                headers: {
                    'Authorization': `Bearer ${expiredToken}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(401);
            });
        });
    });

    it("Test Case 03: Verify that using a valid token (Non-Admin) results in a 403 Forbidden response", { tags: ["@PR", "@Smoke"] }, () => {
        cy.clientLogin().then((token) => {
            cy.api({
                method: "GET",
                url: "/partnerships",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(403);
            });
        });
    });
});