
import { getAccountId } from '../../../fixtures/apihelperFunctions';
describe("Test Scenario: End to End flow to test Deposit To Account", () => {
    let accountId;

    before(() => {
        getAccountId().then((id) => {
            accountId = id;
        });
    });

    it("Test Case 01: Verify that Admin should successfully make a deposit with valid input data", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.getCurrentDate().then((formattedDate) => {
                const depositBody = {
                    "account": accountId,
                    "date": formattedDate,
                    "transactionType": 1,
                    "amount": 1500,
                    "description": "Test Deposit To Account Feature"
                };
                cy.api({
                    method: "POST",
                    url: "transactions/deposit",
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: depositBody
                }).then((response) => {
                    expect(response.status).to.eq(201);
                });
            });
        });
    });
});