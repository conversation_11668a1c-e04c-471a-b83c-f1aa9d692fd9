
import { getAccountId, getInitialTotalTransactionsCount } from '../../../fixtures/apihelperFunctions';
describe("Test Scenario: End to End flow to test Withdrawal From Account through WIRE Method", () => {
    let accountId;
    let response;

    before(() => {
        getAccountId().then((id) => {
            accountId = id;
            return cy.newlogin();
        }).then((token) => {
            cy.getCurrentDate().then((formattedDate) => {
                const withdrawalBody = {
                    "account": accountId,
                    "date": formattedDate,
                    "transactionType": 2,
                    "amount": 500,
                    "description": "Test Deposit To Account Feature",
                    "wireNumber": "********"

                };
                return cy.api({
                    method: "POST",
                    url: "transactions/withdraw/wire",
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: withdrawalBody
                });
            }).then((res) => {
                response = res;
            });
        });
    });

    it("Test Case 01: Verify that <PERSON><PERSON> should successfully make a Withdrawal Amount From Account - Check Status - WIRE Method", { tags: ["@PR", "@Smoke"] }, () => {
        expect(response.status).to.eq(201);

    });

    it("Test Case 02: Verify the Response Response time is less than 500ms  - WIRE Method", { tags: ["@PR", "@Smoke"] }, () => {
        expect(response.duration).to.be.lessThan(5000);
    });

    it("Test Case 03: Verify that Admin should not successfully make a Withdrawal Amount - WIRE Method", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            const withdrawalBody = {
            };
            cy.api({
                method: "POST",
                url: "transactions/withdraw/wire",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: withdrawalBody,
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(400);
            });
        });
    });

    it("Test Case 04: Verify that Admin should not successfully make a Withdrawal Amount without Account Field - WIRE Method", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            cy.getCurrentDate().then((formattedDate) => {
                const withdrawalBodyWithoutAccount = {
                    // "account": accountId,
                    "date": formattedDate,
                    "transactionType": 2,
                    "amount": 500,
                    "description": "Test Deposit To Account Feature",
                    "wireNumber": "********"
                };
                cy.api({
                    method: "POST",
                    url: "transactions/withdraw/wire",
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: withdrawalBodyWithoutAccount,
                    failOnStatusCode: false
                }).then((response) => {
                    expect(response.status).to.eq(400);
                    expect(response.body.type).to.eq('https://tools.ietf.org/html/rfc9110#section-15.5.1');
                    expect(response.body.title).to.eq('One or more validation errors occurred.');

                    const errors = response.body.errors;
                    expect(errors).to.have.property('Account').that.includes('Account is not valid.');
                });
            });
        });
    });

    it("Test Case 05: Verify that Admin should not successfully make a Withdrawal Amount without Date Field - WIRE Method", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            const withdrawalBodyWithoutDate = {
                "account": accountId,
                //"date": new Date().toISOString(),
                "transactionType": 2,
                "amount": 500,
                "description": "Test Deposit To Account Feature",
                "wireNumber": "********"
            };
            cy.api({
                method: "POST",
                url: "transactions/withdraw/wire",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: withdrawalBodyWithoutDate,
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(400);
                expect(response.body.type).to.eq('https://tools.ietf.org/html/rfc9110#section-15.5.1');
                expect(response.body.title).to.eq('One or more validation errors occurred.');

                const errors = response.body.errors;
                expect(response.body.errors.Date[0]).to.eq('Date can not be for a period that has already been processed.');
            });
        });
    });

    it("Test Case 06: Verify that Admin should not successfully make a Withdrawal Amount without Amount Field - WIRE Method", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            cy.getCurrentDate().then((formattedDate) => {
                const withdrawalBodyWithoutAmount = {
                    "account": accountId,
                    "date": formattedDate,
                    "transactionType": 2,
                    //"amount": 500,
                    "description": "Test Deposit To Account Feature",
                    "wireNumber": "********"
                };
                cy.api({
                    method: "POST",
                    url: "transactions/withdraw/wire",
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: withdrawalBodyWithoutAmount,
                    failOnStatusCode: false
                }).then((response) => {
                    expect(response.status).to.eq(400);
                    expect(response.body.type).to.eq('https://tools.ietf.org/html/rfc9110#section-15.5.1');
                    expect(response.body.title).to.eq('One or more validation errors occurred.');

                    const errors = response.body.errors;
                    expect(response.body.errors.Amount[0]).to.eq('Amount should be more than 0.');
                });
            });
        });
    });

    it("Test Case 07: Verify that using an empty token results in a 401 Unauthorized response - WIRE Method", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            const emptyToken = "";
            const withdrawalBody = {
                "account": accountId,
                "date": new Date().toISOString(),
                "transactionType": 2,
                "amount": 500,
                "description": "Test Deposit To Account Feature",
                "wireNumber": "********"
            };
            cy.api({
                method: "POST",
                url: "transactions/withdraw/wire",
                headers: {
                    'Authorization': `Bearer ${emptyToken}`
                },
                body: withdrawalBody,
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(401);
            });
        });
    });

    it("Test Case 08: Verify that using an expired token results in a 401 Unauthorized response - WIRE Method", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            const expiredToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NH2bIjs3tgAm7ri6FTId8P4FdSDNqun-PgaLbJJ1GRI";
            const withdrawalBody = {
                "account": accountId,
                "date": new Date().toISOString(),
                "transactionType": 2,
                "amount": 500,
                "description": "Test Deposit To Account Feature",
                "wireNumber": "********"
            };
            cy.api({
                method: "POST",
                url: "transactions/withdraw/wire",
                headers: {
                    'Authorization': `Bearer ${expiredToken}`
                },
                body: withdrawalBody,
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(401);
            });
        });
    });

    it("Test Case 09: Verify that using a valid token (Non-Admin) results in a 403 Forbidden response - WIRE Method", { tags: ["@PR", "@Smoke"] }, () => {
        cy.clientLogin().then((token) => {
            const withdrawalBody = {
                "account": accountId,
                "date": new Date().toISOString(),
                "transactionType": 2,
                "amount": 500,
                "description": "Test Deposit To Account Feature",
                "wireNumber": "********"
            };
            cy.api({
                method: "POST",
                url: "transactions/withdraw/wire",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: withdrawalBody,
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(403);
            });
        });
    });

    it("Test Case 10: Verify that Admin should not successfully make a Withdrawal Amount with different Method - WIRE Method", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            const withdrawalBodyWithoutAccount = {
                "account": accountId,
                "date": new Date().toISOString(),
                "transactionType": 2,
                "amount": 500,
                "description": "Test Deposit To Account Feature",
                "wireNumber": "********"
            };
            cy.api({
                method: "GET",
                url: "transactions/withdraw/wire",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: withdrawalBodyWithoutAccount,
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(405);
            });
        });
    });

    it("Test Case 11: Verify that Admin should not successfully make a Withdrawal Amount with different Media Type - WIRE Method", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            const withdrawalBodyWithoutAccount = {
                "account": accountId,
                "date": new Date().toISOString(),
                "transactionType": 2,
                "amount": 500,
                "description": "Test Deposit To Account Feature",
                "wireNumber": "********"
            };
            cy.api({
                method: "POST",
                url: "transactions/withdraw/wire",
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'text/plain',
                },
                body: withdrawalBodyWithoutAccount,
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(415);
            });
        });
    });
});