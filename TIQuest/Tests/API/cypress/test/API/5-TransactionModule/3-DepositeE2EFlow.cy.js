
import { getAccountId } from '../../../fixtures/apihelperFunctions';
describe("Test Scenario: End to End flow to test Deposit To Account", () => {
    let accountId;
    let initialTransactionCount;

    before(() => {
        getAccountId().then((id) => {
            accountId = id;
        });
    });


    it("Test Case 01: Verify that Admin should successfully make a deposit with valid input data", { tags: ["@PR", "@Smoke"] }, () => {

        cy.login().then((token) => {
            cy.api({
                method: "GET",
                url: "/transactions?StartDate=01-01-2023&EndDate=01-01-2027",
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                expect(response.status).to.eq(200);
            });
        });
    });
});