

import { getAccountId } from '../../../fixtures/apihelperFunctions';
describe("Test Scenario: End to End flow to test Deposit To Account", () => {
    let accountId;

    before(() => {
        getAccountId().then((id) => {
            accountId = id;
        });
    });

    it("Test Case 01: Verify that deposit in account with a blank body results in a 400 status code", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            const depositBody = {
            };
            cy.api({
                method: "POST",
                url: "transactions/deposit",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: depositBody,
                failOnStatusCode: false,
            }).then((response) => {
                expect(response.status).to.eq(400);
                expect(response.body.type).to.eq('https://tools.ietf.org/html/rfc9110#section-15.5.1');
                expect(response.body.title).to.eq('One or more validation errors occurred.');

                const errors = response.body.errors;
                expect(errors).to.have.property('Date').that.includes('Date can not be for a period that has already been processed.');
                expect(errors).to.have.property('Amount').that.includes('Amount should be more than 0.');
                expect(errors).to.have.property('Account').that.includes('Account is not valid.');
                expect(errors).to.have.property('TransactionType').that.includes('TransactionType is not valid.');
            });
        });
    });

    it("Test Case 02:  Verify that deposit in account with different method(GET) results in a 400 status code", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.getCurrentDate().then((formattedDate) => {
                const depositBody = {
                    "account": accountId,
                    "date": formattedDate,
                    "transactionType": 1,
                    "amount": 500,
                    "description": "Test Deposit To Account Feature"
                };
                cy.api({
                    method: "GET",
                    url: "transactions/deposit",
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: depositBody,
                    failOnStatusCode: false,
                }).then((response) => {
                    expect(response.status).to.eq(400);
                    expect(response.body.errors).to.have.property('id');
                    expect(response.body.errors.id).to.include('The value \'deposit\' is not valid.');
                });
            });
        });
    });

    it("Test Case 03:  Verify that deposit in account with invalid transactionType results in a 400 status code", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.getCurrentDate().then((formattedDate) => {
                const depositBody = {
                    "account": accountId,
                    "date": formattedDate,
                    "transactionType": -1,
                    "amount": 500,
                    "description": "Test Deposit To Account Feature"
                };
                cy.api({
                    method: "POST",
                    url: "transactions/deposit",
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: depositBody,
                    failOnStatusCode: false,
                }).then((response) => {
                    expect(response.status).to.eq(400);
                });
            });
        });
    });

    it("Test Case 04:  Verify that deposit in account with not found transactionType results in a 400 status code", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.getCurrentDate().then((formattedDate) => {
                const depositBody = {
                    "account": accountId,
                    "date": formattedDate,
                    "transactionType": 0,
                    "amount": 500,
                    "description": "Test Deposit To Account Feature"
                };
                cy.api({
                    method: "POST",
                    url: "transactions/deposit",
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: depositBody,
                    failOnStatusCode: false,
                }).then((response) => {
                    expect(response.status).to.eq(400);
                });
            });
        });
    });

    it("Test Case 05:  Verify that deposit in account with negative amount results in a 400 status code", { tags: ["@PR", "@Smoke"] }, () => {
        cy.login().then((token) => {
            cy.getCurrentDate().then((formattedDate) => {
                const depositBody = {
                    "account": accountId,
                    "date": formattedDate,
                    "transactionType": 1,
                    "amount": -500,
                    "description": "Test Deposit To Account Feature"
                };
                cy.api({
                    method: "POST",
                    url: "transactions/deposit",
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: depositBody,
                    failOnStatusCode: false,
                }).then((response) => {
                    expect(response.status).to.eq(400);
                    expect(response.body.type).to.eq('https://tools.ietf.org/html/rfc9110#section-15.5.1');
                    expect(response.body.title).to.eq('One or more validation errors occurred.');

                    const errors = response.body.errors;
                    expect(errors).to.have.property('Amount').that.includes('Amount should be more than 0.');
                });
            });
        });
    });

    it("Test Case 06: Verify that the endpoint identifies and rejects invalid tokens, returning an appropriate error status code of 401.", { tags: ['@PR', '@Smoke'] }, () => {
        cy.login().then((token) => {
            cy.getCurrentDate().then((formattedDate) => {
                const depositBody = {
                    "account": accountId,
                    "date": formattedDate,
                    "transactionType": 1,
                    "amount": -500,
                    "description": "Test Deposit To Account Feature"
                };
                cy.api({
                    method: "POST",
                    url: "transactions/deposit",
                    headers: {
                        "Content-Type": "application/json",
                        'Authorization': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Lre_7V73x2mj5ZcfsSBj2EUVJ_iuYacDck_M_DRqukA'
                    },
                    failOnStatusCode: false,
                }).then((response) => {
                    expect(response.status).to.eq(401);
                });
            });
        });
    });
});