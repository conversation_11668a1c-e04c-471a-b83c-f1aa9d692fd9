describe("Test Scenario: Verify all the Scenarios for the getAllTransaction API", () => {
    it("Test Case 01: Verify that Admin should have status code 200 and Content-Type application/json", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            cy.api({
                method: "GET",
                url: "/transactions?StartDate=01-01-2024&EndDate=01-01-2027",
                headers: {
                    'Authorization': `Bear<PERSON> ${token}`
                }
            }).then((response) => {
                expect(response.status).to.eq(200);
                expect(response.headers['content-type']).to.include('application/json');
            });
        });
    });

    // it("Test Case 02: Verify that <PERSON><PERSON> should have an array of transactions with specific fields", { tags: ["@PR", "@Smoke"] }, () => {
    //     cy.newlogin().then((token) => {
    //         cy.api({
    //             method: "GET",
    //             url: "/transactions?StartDate=01-01-2023&EndDate=01-01-2027",
    //             headers: {
    //                 'Authorization': `<PERSON><PERSON> ${token}`
    //             }
    //         }).then((response) => {
    //             expect(response.body).to.be.an('array').and.to.not.be.empty;
    //             const firstFiftyTransactions = response.body.slice(0, 10);
    //             firstFiftyTransactions.forEach((transaction) => {
    //                 response.body.forEach((transaction) => {
    //                     expect(transaction).to.include.all.keys('id', 'investor', 'accountNumber', 'name', 'balance', 'transactions');
    //                 });
    //             });
    //         });
    //     });
    // });

    // it("Test Case 03: Verify that Admin should have valid sub-transaction type and status", { tags: ["@PR", "@Smoke"] }, () => {
    //     cy.newlogin().then((token) => {
    //         cy.api({
    //             method: "GET",
    //             url: "/transactions?StartDate=01-01-2023&EndDate=01-01-2027",
    //             headers: {
    //                 'Authorization': `Bearer ${token}`
    //             }
    //         }).then((response) => {
    //             expect(response.body).to.be.an('array').and.to.not.be.empty;
    //             // Get only the first 50 transactions
    //             const firstFiftyTransactions = response.body.slice(0, 10);
    //             firstFiftyTransactions.forEach((transaction) => {
    //                 transaction.transactions.forEach((subTransaction) => {
    //                     expect(subTransaction.status).to.match(/^(Outstanding|Reconciled|Void)$/);
    //                 });
    //             });
    //         });
    //     });
    // });

    it("Test Case 04: Verify that using an expired token results in a 401 Unauthorized response", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            const expiredToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NH2bIjs3tgAm7ri6FTId8P4FdSDNqun-PgaLbJJ1GRI";
            cy.api({
                method: "GET",
                url: "/transactions?StartDate=01-01-2023&EndDate=01-01-2027",
                headers: {
                    'Authorization': `Bearer ${expiredToken}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(401);
            });
        });
    });

    it("Test Case 05: Verify that using an empty token results in a 401 Unauthorized response", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            const expiredToken = "";
            cy.api({
                method: "GET",
                url: "/transactions?StartDate=01-01-2023&EndDate=01-01-2027",
                headers: {
                    'Authorization': `Bearer ${expiredToken}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(401);
            });
        });
    });

    it("Test Case 06: Verify that Admin should have status code 200 and Content-Type application/json", { tags: ["@PR", "@Smoke"] }, () => {
        cy.clientLogin().then((token) => {
            cy.api({
                method: "GET",
                url: "/transactions?StartDate=01-01-2023&EndDate=01-01-2027",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(200);
                expect(response.headers['content-type']).to.include('application/json');
            });
        });
    });

    it("Test Case 07: Verify that Auditor should have status code 200 and Content-Type application/json", { tags: ["@PR", "@Smoke"] }, () => {
        cy.auditorLogin().then((token) => {
            cy.api({
                method: "GET",
                url: "/transactions?StartDate=01-01-2023&EndDate=01-01-2027",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(200);
                expect(response.headers['content-type']).to.include('application/json');
            });
        });
    });
})