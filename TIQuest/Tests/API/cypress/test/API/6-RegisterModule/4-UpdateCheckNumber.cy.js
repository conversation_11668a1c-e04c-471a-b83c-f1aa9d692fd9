describe("Test Scenario: Verify all test cases for the Update Check API to check number for all unprinted checks", () => {
    it("Test Case 01: Verify the Register Check API should check number for unprinted checks", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            cy.api({
                method: "POST",
                url: "/registers/check",
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                failOnStatusCode: false,
                body: "1"
            }).then((response) => {
                expect(response.status).to.eq(204);

                cy.api({
                    method: "GET",
                    url: "/registers/check",
                    headers: {
                        'Authorization': `Bear<PERSON> ${token}`
                    }
                }).then((response) => {
                    expect(response.status).to.eq(200);
                });
            });
        });
    });

    it("Test Case 02: Verify that Next check number must be greater than 0", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            cy.api({
                method: "POST",
                url: "/registers/check",
                headers: {
                    'Authorization': `Bear<PERSON> ${token}`,
                    'Content-Type': 'application/json'
                },
                failOnStatusCode: false,
                body: "0"
            }).then((response) => {
                expect(response.status).to.eq(400);
                expect(response.body.errors).to.have.property('nextCheckNumber');
                expect(response.body.errors.nextCheckNumber[0]).to.eq('Next check number must be greater than 0.');
            });
        });
    });

    it("Test Case 03: Verify that Next check number must not be empty", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            cy.api({
                method: "POST",
                url: "/registers/check",
                headers: {
                    'Authorization': `Bearer ${token}`,
                },
                failOnStatusCode: false,
                body: "1"
            }).then((response) => {
                expect(response.status).to.eq(415);
            });
        });
    });

    it("Test Case 04: Verify the Register Check API should return status 403 For Client User", { tags: ["@PR", "@Smoke"] }, () => {
        cy.clientLogin().then((token) => {
            cy.api({
                method: "POST",
                url: "/registers/check",
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                failOnStatusCode: false,
                body: "1"
            }).then((response) => {
                expect(response.status).to.eq(403);
            });
        });
    });

    it("Test Case 05: Verify the Register Check API should return status 403 For Auditor User", { tags: ["@PR", "@Smoke"] }, () => {
        cy.auditorLogin().then((token) => {
            cy.api({
                method: "POST",
                url: "/registers/check",
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                failOnStatusCode: false,
                body: "1"
            }).then((response) => {
                expect(response.status).to.eq(403);
            });
        });
    });

    it("Test Case 06: Verify that using an expired token results in a 401 Unauthorized response", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            const expiredToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NH2bIjs3tgAm7ri6FTId8P4FdSDNqun-PgaLbJJ1GRI";
            cy.api({
                method: "POST",
                url: "/registers/check",
                headers: {
                    'Authorization': `Bearer ${expiredToken}`,
                    'Content-Type': 'application/json'
                },
                failOnStatusCode: false,
                body: "1"
            }).then((response) => {
                expect(response.status).to.eq(401);
            });
        });
    });

    it("Test Case 06: Verify response time is less than 500ms", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            const startTime = new Date().getTime(); // Record the start time
            cy.api({
                method: "POST",
                url: "/registers/check",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                const endTime = new Date().getTime();
                const duration = endTime - startTime;
                expect(duration).to.be.lessThan(5000);
            });
        });
    });
});