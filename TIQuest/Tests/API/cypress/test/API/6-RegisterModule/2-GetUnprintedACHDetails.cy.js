describe("Test Scenario: Verify all test cases for the Register ACH API", () => {
    it("Test Case 01: Verify the Register ACH API should return status 200 For Admin User", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            cy.api({
                method: "GET",
                url: "/registers/ach",
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                expect(response.status).to.eq(200);
            });
        });
    });

    it("Test Case 02: Verify the Register ACH API should return status 403 For Client User", { tags: ["@PR", "@Smoke"] }, () => {
        cy.clientLogin().then((token) => {
            cy.api({
                method: "GET",
                url: "/registers/ach",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(403);
            });
        });
    });

    it("Test Case 03: Verify the Register ACH API should return status 403 For Auditor User", { tags: ["@PR", "@Smoke"] }, () => {
        cy.auditorLogin().then((token) => {
            cy.api({
                method: "GET",
                url: "/registers/ach",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(403);
            });
        });
    });

    it("Test Case 04: Verify the Register ACH API should return an array of objects", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            cy.api({
                method: "GET",
                url: "/registers/ach",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
            }).then((response) => {
                if (response.status === 200) {
                    expect(response.body).to.be.an('array').that.is.not.empty;
                    response.body.forEach((entry) => {
                        expect(entry).to.be.an('object');
                    });
                }
            });
        });
    });

    it("Test Case 05: Verify each object in the response has required properties", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            cy.api({
                method: "GET",
                url: "/registers/ach",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(200);
                response.body.forEach((entry) => {
                    expect(entry).to.have.property('id');
                    expect(entry).to.have.property('date');
                    expect(entry).to.have.property('amount');
                    expect(entry).to.have.property('details');
                });
            });
        });
    });

    it("Test Case 06: Verify response time is less than 500ms", { tags: ["@PR", "@Smoke"] }, () => {
        cy.auditorLogin().then((token) => {
            const startTime = new Date().getTime(); // Record the start time
            cy.api({
                method: "GET",
                url: "/registers/ach",
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                failOnStatusCode: false
            }).then((response) => {
                const endTime = new Date().getTime();
                const duration = endTime - startTime;
                expect(duration).to.be.lessThan(5000);
            });
        });
    });

    it("Test Case 07: Verify the Register ACH API should return status 401 if JWT Token is missing", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            const expiredToken = "";
            cy.api({
                method: "GET",
                url: "/registers/ach",
                headers: {
                    'Authorization': `Bearer ${expiredToken}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(401);
            });
        });
    });

    it("Test Case 08: Verify that using an expired token results in a 401 Unauthorized response", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            const expiredToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NH2bIjs3tgAm7ri6FTId8P4FdSDNqun-PgaLbJJ1GRI";
            cy.api({
                method: "GET",
                url: "/registers/ach",
                headers: {
                    'Authorization': `Bearer ${expiredToken}`
                },
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(401);
            });
        });
    });
});