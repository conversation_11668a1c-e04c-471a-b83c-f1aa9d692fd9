describe("Test Scenario: Verify all test cases for the Register ACH API", () => {
    it("Test Case 01: Verify the Register ACH API should return status 200 For Admin User", { tags: ["@PR", "@Smoke"] }, () => {
        cy.newlogin().then((token) => {
            cy.api({
                method: "GET",
                url: "/registers/ach",
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then((response) => {
                expect(response.status).to.eq(200);

                const firstId = response.body[0].id;
                cy.wrap(firstId).as('firstId');

                cy.get('@firstId').then(id => {
                    cy.api({
                        method: "POST",
                        url: "/reports/ach",
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        // body: JSON.stringify([firstId])
                        body: [firstId]
                    }).then(res => {
                        expect(res.status).to.eq(200);

                        cy.api({
                            method: "GET",
                            url: "/registers/ach",
                            headers: {
                                'Authorization': `Bearer ${token}`
                            }
                        }).then((newResponse) => {
                            expect(newResponse.status).to.eq(200);

                            // Verify that firstId is not present in the new response
                            const ids = newResponse.body.map(item => item.id);
                            expect(ids).to.not.include(firstId);
                        });
                    });
                });
            });
        });
    });
});