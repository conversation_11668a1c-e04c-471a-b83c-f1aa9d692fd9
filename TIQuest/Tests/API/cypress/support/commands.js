// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... }

Cypress.Commands.add("login", () => {
    cy.fixture("loginCredentials.json").then((credentials) => {
        const base64Password = btoa(credentials.password);
        return cy.api({
            method: "POST",
            url: "/auth/login",
            body: {
                email: credentials.email,
                password: base64Password,
            },
        }).then((response) => {
            expect(response.status).to.equal(200);
            expect(response.body).to.have.property("token");
            return response.body.token;
        });
    });
});

Cypress.Commands.add("newlogin", () => {
    cy.fixture("newloginCredentials.json").then((credentials) => {
        const base64Password = btoa(credentials.password);
        return cy.api({
            method: "POST",
            url: "/auth/login",
            body: {
                email: credentials.email,
                password: base64Password,
            },
        }).then((response) => {
            expect(response.status).to.equal(200);
            expect(response.body).to.have.property("token");
            return response.body.token;
        });
    });
});

// commands.js
Cypress.Commands.add("fetchInvestor", (token) => {
    return cy.api({
      method: "GET",
      url: "/investors",
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }).then((response) => {
      expect(response.status).to.eq(200);
      const investorId = response.body[0].id;
      return investorId;
    });
  });

  Cypress.Commands.add("clientLogin", () => {
    cy.fixture("clientLoginCredentials.json").then((credentials) => {
        const base64Password = btoa(credentials.password);
        return cy.api({
            method: "POST",
            url: "/auth/login",
            body: {
                email: credentials.email,
                password: base64Password,
            },
        }).then((response) => {
            expect(response.status).to.equal(200);
            expect(response.body).to.have.property("token");
            return response.body.token;
        });
    });
});

Cypress.Commands.add("auditorLogin", () => {
    cy.fixture("auditorLoginCredentials.json").then((credentials) => {
        const base64Password = btoa(credentials.password);
        return cy.api({
            method: "POST",
            url: "/auth/login",
            body: {
                email: credentials.email,
                password: base64Password,
            },
        }).then((response) => {
            expect(response.status).to.equal(200);
            expect(response.body).to.have.property("token");
            return response.body.token;
        });
    });
});

Cypress.Commands.add('generateDateAndSuffix', () => {
    const now = new Date();
    const startDate = now.toISOString();
    const uniqueSuffix = Date.now();
    return { startDate, uniqueSuffix };
});

Cypress.Commands.add('getCurrentDate', () => {
    const today = new Date();
    return today.toISOString().split('T')[0]; // Returns the date part of the ISO string
});

  
  