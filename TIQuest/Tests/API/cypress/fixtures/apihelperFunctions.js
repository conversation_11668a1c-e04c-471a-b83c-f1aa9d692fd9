import faker from 'faker';

export const createInvestor = (token) => {
    return cy.login().then((token) => {
        const createInvestorData = {
            firstName: faker.name.firstName(),
            lastName: faker.name.lastName(),
            companyName: faker.company.companyName(),
            taxNumber: faker.phone.phoneNumberFormat(),
            type: 'Company',
            email: faker.internet.email(),
            officePhone: faker.phone.phoneNumberFormat(),
            homePhone: faker.phone.phoneNumberFormat(),
            mobile: faker.phone.phoneNumberFormat(),
            fax: faker.phone.phoneNumberFormat(),
            apartment: 'B-304',
            address: '47 W 13th St',
            city: 'New York',
            state: 'NY',
            zip: '10011',
            country: 'USA',
            note: 'This is Automation Investor',
            isActive: true
        };

        return cy.api({
            method: "POST",
            url: "/investors",
            headers: {
                'Authorization': `Bear<PERSON> ${token}`
            },
            body: createInvestorData
        }).then((response) => {
            expect(response.status).to.eq(201);
            return response.body.id;
        });
    });
};

export const createIndividualInvestor = (token) => {
    return cy.login().then((token) => {
        const createInvestorData = {
            firstName: faker.name.firstName(),
            lastName: faker.name.lastName(),
            taxNumber: faker.phone.phoneNumberFormat(),
            type: 'Individual',
            email: faker.internet.email(),
            officePhone: faker.phone.phoneNumberFormat(),
            homePhone: faker.phone.phoneNumberFormat(),
            mobile: faker.phone.phoneNumberFormat(),
            fax: faker.phone.phoneNumberFormat(),
            apartment: 'B-304',
            address: '47 W 13th St',
            city: 'New York',
            state: 'NY',
            zip: '10011',
            country: 'USA',
            note: 'This is Automation Investor',
            isActive: true
        };

        return cy.api({
            method: "POST",
            url: "/investors",
            headers: {
                'Authorization': `Bearer ${token}`
            },
            body: createInvestorData
        }).then((response) => {
            expect(response.status).to.eq(201);
            return response.body.id;
        });
    });
};

export const createAccount = (token, investorId) => {
    const accountData = {
        "investor": investorId,
        "name": "Automation_ScriptAccount_Pensioners savings bank account",
        "accountType": "Investment",
        "startDate": "2023-12-13T10:21:59.797+00:00",
        "endDate": "2025-12-13T10:21:59.797+00:00",
        "rate": 2.5,
        "interestType": "ACH",
        "report1099Name": "Test Report"
    };
    return cy.api({
        method: "POST",
        url: "/accounts",
        headers: {
            'Authorization': `Bearer ${token}`
        },
        body: accountData
    }).then((accountCreationResponse) => {
        expect(accountCreationResponse.status).to.eq(201);
        const accountId = getCreatedEntityIdFromHeader(accountCreationResponse.headers);

        return cy.api({
            method: "GET",
            url: `/accounts/${accountId}`,
            headers: {
                'Authorization': `Bearer ${token}`
            }
        }).then((accountDetailsResponse) => {
            return {
                accountId: accountId,
                accountDetails: accountDetailsResponse.body
            };
        });
    });
};

export const getAccountId = () => {
    return cy.login().then((token) => {
        return cy.api({
            method: "GET",
            url: "/accounts",
            headers: {
                'Authorization': `Bearer ${token}`
            }
        }).then((response) => {
            expect(response.status).to.eq(200);
            return response.body[2].id;
        });
    });
};

export const getInvestorId = () => {
    return cy.login().then((token) => {
        return cy.api({
            method: "GET",
            url: "/investors",
            headers: {
                'Authorization': `Bearer ${token}`
            }
        }).then((response) => {
            expect(response.status).to.eq(200);
            return response.body[0].id;
        });
    });
};

export function validateNullableProperties(value, expectedType) {
    return value === null || typeof value === expectedType;
}

export const createAndGetLastPartnershipId = (token) => {
    return cy.login().then((token) => {

        const createPartnershipData = {
            name: faker.name.firstName(),
            description: 'This is Automation Partnership',
            apartment: 'B-304',
            address: '47 W 13th St',
            city: 'New York',
            state: 'NY',
            zip: '10011',
            country: 'USA'
        };

        return cy.api({
            method: "POST",
            url: "/partnerships",
            headers: {
                'Authorization': `Bearer ${token}`
            },
            body: createPartnershipData
        }).then((response) => {
            expect(response.status).to.eq(201);
            return response.body.id;
        });
    });
};

export const getCreatedEntityIdFromHeader = (headers) => {
    const locationHeader = headers['location'];
    if (locationHeader) {
        const entityId = locationHeader.split('/').pop();
        return parseInt(entityId);
    } else {
        throw new Error("Location header is missing in the response.");
    }
};

export const getInitialTotalTransactionsCount = () => {
    return cy.newlogin().then((token) => {
        return cy.api({
            method: "GET",
            url: "/transactions?startDate=2023-01-01&endDate=2027-01-01",
            headers: {
                'Authorization': `Bearer ${token}`
            }
        }).then((response) => {
            expect(response.status).to.eq(200);
            let initialtotalCount = 0;
            response.body.forEach(account => {
                initialtotalCount += account.transactions.length;
            });
            return initialtotalCount;
        });
    });
};

