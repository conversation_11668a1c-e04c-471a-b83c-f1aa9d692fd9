﻿namespace TIQuest.Api
{
    internal class Constants
    {
        internal class RoleConstants
        {
            internal const string PasswordResetRole = "PasswordReset";
            internal const string AdminRole = "Administrator";
            internal const string ClientRole = "Client";
            internal const string AuditorRole = "Auditor";
        }

        internal class DataConstants
        {
            internal const string DepositCode = "I-DEP";
            internal const string WithdrawalCode = "I-WDL";
            internal const string TransferCode = "XFER";
            internal const string ManagementFeeCode = "FEE";
            internal const string InvestmentInterestCode = "I-INT";
            internal const string LoanInterestCode = "L-INT";
            internal const string LoanPaymentCode = "L-PMT";
            internal const string LoanAdvanceCode = "L-ADV";
            internal const string VoidAdjustmentCode = "VADJ";
            internal const string LoanTransferCode = "LXFER";

            internal const int CashAccountId = 1;
            internal const int SystemInvestorId = 1;
            internal const string InvestmentAccountType = "Investment";
            internal const string LoanAccountType = "Loan";
            internal const string OutstandingStatus = "Outstanding";
            internal const string ReconciledStatus = "Reconciled";
            internal const string VoidStatus = "Void";
            internal const string IndividualInvestorType = "Individual";
            internal const string CompanyInvestorType = "Company";
            internal const string NextCheckNumberKey = "NextCheckNumber";
            internal static DateTime SystemMinimumDate = new DateTime(2000, 1, 1, 0, 0, 0);
            internal const string AccrualInterestType = "Accrual";
            internal const string CheckInterestType = "Check";
            internal const string AchInterestType = "Ach";
            internal const string CompanyEmailKey = "CompanyEmail";
            internal const string QuoteKey = "Quote";
            internal const string InvestmentMessageKey = "InvestmentMessage";

            internal const string DateFormat = "MM/dd/yyyy";

			   internal const string BankTransactionTypeCredit = "Credit";
			   internal const string BankTransactionTypeDebit = "Debit";
		}
	}
}
