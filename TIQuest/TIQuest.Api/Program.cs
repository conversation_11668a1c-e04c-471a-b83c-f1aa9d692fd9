using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Azure;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Reflection;
using System.Security.Claims;
using System.Text;
using TIQuest.Api;
using TIQuest.Api.Entities;
using TIQuest.Api.Extensions;
using TIQuest.Api.Middlewares;
using TIQuest.Api.Options;
using Azure.Monitor.OpenTelemetry.AspNetCore;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(option =>
{
    option.SwaggerDoc("v1", new OpenApiInfo { Title = "TIQuest API", Version = "v1" });
    option.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        In = ParameterLocation.Header,
        Description = "Please enter a valid token",
        Name = "Authorization",
        Type = SecuritySchemeType.Http,
        BearerFormat = "JWT",
        Scheme = "Bearer"
    });
    option.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // using System.Reflection;
    var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    option.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename));

});

builder.Services.RegisterServices();

builder.Services.AddMemoryCache();

//builder.Services.AddTransient<GlobalExceptionMiddleware>();
builder.Services.AddTransient<TokenValidationMiddleware>();

var azureStorageConnectionString = builder.Configuration.GetValue<string>("Azure:Storage:ConnectionString");

if (string.IsNullOrEmpty(azureStorageConnectionString))
{
    throw new ApplicationException("Azure Storage connection string is missing or empty.");
}

builder.Services.AddAzureClients(clientBuilder =>
{
    // Register clients for each service
    try
    {
        clientBuilder.AddBlobServiceClient(azureStorageConnectionString);
        clientBuilder.AddQueueServiceClient(azureStorageConnectionString);
    }
    catch (Exception ex)
    {
        throw new ApplicationException("Error occurred while registering Azure Storage clients.", ex);
    }
});

builder.Services.AddAutoMapper(typeof(Program).Assembly);

builder.Services.AddPooledDbContextFactory<DataContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("TIQuest")));

builder.Services.AddOptions<JwtConfigOptions>()
    .Bind(builder.Configuration.GetSection(JwtConfigOptions.JwtConfig))
    .ValidateDataAnnotations()
    .ValidateOnStart();

builder.Services.AddOptions<EmailSettings>()
    .Bind(builder.Configuration.GetSection(EmailSettings.EmailConfig));

builder.Services.AddOptions<ApplicationConfig>()
    .Bind(builder.Configuration.GetSection(ApplicationConfig.ApplicationConstants));


var secret = builder.Configuration.GetValue<string>("JWT:Secret");
var issuer = builder.Configuration.GetValue<string>("JWT:Issuer");
var audience = builder.Configuration.GetValue<string>("JWT:Audience");
builder.Services
    .AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters.ValidIssuer = issuer;
        options.TokenValidationParameters.ValidAudience = audience;
        options.TokenValidationParameters.IssuerSigningKey = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(secret!.ToString()));
        options.TokenValidationParameters.ClockSkew = TimeSpan.Zero;
    });

builder.Services.AddAuthorization(options =>
{
    options.DefaultPolicy = new AuthorizationPolicyBuilder()
                                    .RequireClaim(ClaimTypes.NameIdentifier)
                                    .RequireRole(Constants.RoleConstants.AdminRole, Constants.RoleConstants.ClientRole, Constants.RoleConstants.AuditorRole)
                                    .Build();
});

builder.Services.AddOpenTelemetry().UseAzureMonitor();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

//// allow exceptions to be thrown in development for ease of debugging
//if (!app.Environment.IsDevelopment())
//{
//    app.UseMiddleware<GlobalExceptionMiddleware>();
//}

app.UseHttpsRedirection();

app.UseAuthorization();

app.UseMiddleware<TokenValidationMiddleware>();

app.MapControllers();

app.Run();