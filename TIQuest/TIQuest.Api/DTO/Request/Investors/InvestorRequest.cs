﻿using System.ComponentModel.DataAnnotations;

namespace TIQuest.Api.DTO.Request.Investors
{
    public abstract class InvestorRequest
    {
        [MaxLength(80)]
        public string? FirstName { get; set; }
        [MaxLength(80)]
        public string? LastName { get; set; }
        [MaxLength(200)]
        public string? CompanyName { get; set; }
        [MaxLength(320)]
        public string? Email { get; set; }
        [MaxLength(30)]
        [Phone]
        public string? OfficePhone { get; set; }
        [MaxLength(30)]
        [Phone]
        public string? HomePhone { get; set; }
        [MaxLength(30)]
        [Phone]
        public string? Mobile { get; set; }
        [MaxLength(30)]
        public string? Fax { get; set; }
        [MaxLength(100)]
        public string? Apartment { get; set; }
        [MaxLength(100)]
        public string? Address { get; set; }
        [MaxLength(100)]
        public string? City { get; set; }
        [MaxLength(100)]
        public string? State { get; set; }
        [MaxLength(30)]
        public string? Zip { get; set; }
        [MaxLength(100)]
        [Required]
        public string Country { get; set; }
        [MaxLength(400)]
        public string? Note { get; set; }
        [MaxLength(30)]
        public string? TaxNumber { get; set; }
    }
}
