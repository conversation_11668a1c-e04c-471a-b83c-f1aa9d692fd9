﻿using System.ComponentModel.DataAnnotations;
using TIQuestAttributes = TIQuest.Api.Attributes;

namespace TIQuest.Api.DTO.Request
{
    public class AccountRequest
    {
        [Required]
        public int Investor { get; set; }
        [Required]
        [MaxLength(100)]
        public string Name { get; set; }
        [MaxLength(320)]
        public string? Email { get; set; }
        [MaxLength(250)]
        public string? Report1099Name { get; set; }
        [Required]
        [TIQuestAttributes.AllowedValues(new string[] { Constants.DataConstants.InvestmentAccountType, Constants.DataConstants.LoanAccountType })]
        public string AccountType { get; set; }
        [Required]
        public DateTimeOffset StartDate { get; set; }
        public DateTimeOffset? EndDate { get; set; }
        public decimal? Rate { get; set; }
        [TIQuestAttributes.AllowedValues(new string[] { Constants.DataConstants.AccrualInterestType, Constants.DataConstants.AchInterestType, Constants.DataConstants.CheckInterestType })]
        public string? InterestType { get; set; }
        public bool? SendEmail { get; set; }
        public bool? SendMail { get; set; }
        public string? TaxNumber { get; set; }
    }
}
