﻿using System.ComponentModel.DataAnnotations;

namespace TIQuest.Api.DTO.Request
{
    /// <summary>
    /// DTO for the user request
    /// </summary>
    public class UserRequest
    {
        [Required]
        [MaxLength(80)]
        public string FirstName { get; set; }
        [Required]
        [<PERSON><PERSON>ength(80)]
        public string LastName { get; set; }
        [Required]
        [EmailAddress]
        [MaxLength(200)]
        public string Email { get; set; }
        public List<int>? Accounts { get; set; }
        public bool IsActive { get; set; }
        public List<UserStatements>? Statements { get; set; }
    }
    /// <summary>
    /// DTO for User Statements
    /// </summary>
    public class UserStatements
    {
        [Required]
        public int Account { get; set; }
        public bool Email { get; set; }
        public bool Mail { get; set; }
    }
}
