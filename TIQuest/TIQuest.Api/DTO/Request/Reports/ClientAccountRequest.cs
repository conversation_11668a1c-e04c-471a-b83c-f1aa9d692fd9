﻿using System.ComponentModel.DataAnnotations;

namespace TIQuest.Api.DTO.Request.Reports
{
    public class ClientAccountRequest
    {
        [Required]
        public RequestFilter Client { get; set; }
        [Required]
        public RequestFilter Account { get; set; }
        public string? AccountType { get; set; }
        [Required]
        [Range(1, 12)]
        public int Month { get; set; }
        [Required]
        public int Year { get; set; }
        [Required]
        public bool Email { get; set; }
    }

    public class RequestFilter
    {
        [Required]
        public List<int> Filter { get; set; }
        [Required]
        public bool IsActive { get; set; }
    }
}
