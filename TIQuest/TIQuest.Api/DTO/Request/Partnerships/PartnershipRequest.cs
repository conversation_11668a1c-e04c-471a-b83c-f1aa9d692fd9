﻿using System.ComponentModel.DataAnnotations;

namespace TIQuest.Api.DTO.Request.Partnerships
{
    public class PartnershipRequest
    {
        [Required]
        [MaxLength(200)]
        public string Name { get; set; }
        [MaxLength(2000)]
        public string? Description { get; set; }
        [MaxLength(100)]
        public string? Apartment { get; set; }
        [MaxLength(100)]
        public string? Address { get; set; }
        [MaxLength(100)]
        public string? City { get; set; }
        [MaxLength(100)]
        public string? State { get; set; }
        [MaxLength(30)]
        public string? Zip { get; set; }
        [MaxLength(100)]
        public string Country { get; set; }
    }
}
