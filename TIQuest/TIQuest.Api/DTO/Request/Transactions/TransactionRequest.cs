﻿using System.ComponentModel.DataAnnotations;

namespace TIQuest.Api.DTO.Request.Transactions
{
    public class TransactionRequest
    {
        [Required]
        public int Account { get; init; }
        [Required]
        public DateOnly Date { get; init; }
        [Required]
        public int TransactionType { get; init; }
        [Required]
        public decimal Amount { get; init; }
        [MaxLength(80)]
        public string? Description { get; init; }
		  public string? BankTransactionType { get; init; }
	}
}
