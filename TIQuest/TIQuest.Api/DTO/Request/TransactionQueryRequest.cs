﻿namespace TIQuest.Api.DTO.Request
{
    /// <summary>
    /// Transaction request model
    /// </summary>
    /// <param name="StartDate">Start Date (optional when DistributionId is provided)</param>
    /// <param name="EndDate">End Date (optional when DistributionId is provided)</param>
    /// <param name="ExcludeTransactionCodes">Transaction Codes to Exclude</param>
    /// <param name="DistributionId">Optional Distribution ID to filter transactions</param>
    public record TransactionQueryRequest(DateOnly? StartDate, DateOnly? EndDate, string? ExcludeTransactionCodes, int? DistributionId = null);
}
