﻿using System.ComponentModel.DataAnnotations;

namespace TIQuest.Api.DTO.Request
{
    public class MonthEndSummaryRequest
    {
        [Required]
        [Range(1, 12)]
        public int Month { get; set; }
        [Required]
        public int Year { get; set; }
        [Required]
        public string Description { get; set; }
        [Required]
        public decimal? InvestmentRate { get; set; }
        [Required]
        public decimal? ManagementRate { get; set; }
    }
}
