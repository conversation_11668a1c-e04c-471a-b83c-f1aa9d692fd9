﻿using CsvHelper.Configuration.Attributes;
using System.Globalization;

namespace TIQuest.Api.DTO
{
    public class AchCsvResponse
    {
        [DateTimeStyles(DateTimeStyles.AssumeUniversal)]
        [Format("MM/dd/yyyy")]
        public DateTime Date { get; set; }
        public string Client { get; set; }
        public string AccountNumber { get; set; }
        public decimal Amount { get; set; }
        public string Details { get; set; }
    }
}
