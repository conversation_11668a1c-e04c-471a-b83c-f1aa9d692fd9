﻿using TIQuest.Api.DTO.Request;

namespace TIQuest.Api.DTO.Response
{
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
    public class UserResponse
    {
        public int? Id { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public string Role { get; set; }
        public bool IsActive { get; set; }
        public List<int>? Accounts { get; set; }
        public List<UserStatements>? Statements { get; set; }
    }
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
}
