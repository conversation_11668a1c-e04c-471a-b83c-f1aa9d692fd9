﻿namespace TIQuest.Api.DTO.Response
{
    public class MonthEndSummaryResponse
    {
        public InterestSummaryResponse Investment { get; init; } = new();
        public InterestSummaryResponse FixedLoan { get; init; } = new();
        public InterestSummaryResponse VariableLoan { get; init; } = new();
        public ManagementFeeSummary ManagementFee { get; init; } = new();
    }

    public class InterestSummaryResponse
    {
        private decimal? balance;
        private decimal? rate;
        private decimal? due;

        public decimal? Balance { get => balance is null ? null : decimal.Round(balance.Value, 5); internal set { balance = value; } }
        public decimal? Rate { get => rate is null ? null : decimal.Round(rate.Value, 5); internal set { rate = value; } }
        public decimal? Due { get => due is null ? null : decimal.Round(due.Value, 5); internal set { due = value; } }
    }

    public class ManagementFeeSummary
    {
        private decimal? fee;
        private decimal? rate;
        private decimal? offset;
        private decimal? total;

        public decimal? Fee { get => fee is null ? null : decimal.Round(fee.Value, 5); internal set { fee = value; } }
        public decimal? Rate { get => rate is null ? null : decimal.Round(rate.Value, 5); internal set { rate = value; } }
        public decimal? Offset { get => offset is null ? null : decimal.Round(offset.Value, 5); internal set { offset = value; } }
        public decimal? Total { get => total is null ? null : decimal.Round(total.Value, 5); internal set { total = value; } }
    }

}
