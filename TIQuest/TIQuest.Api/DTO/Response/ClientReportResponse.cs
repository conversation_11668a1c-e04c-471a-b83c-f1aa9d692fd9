﻿namespace TIQuest.Api.DTO.Response
{
    public class ClientReportResponse
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? CompanyName { get; set; }
        public string? TaxNumber { get; set; }
        public string? AccountName { get; set; }
        public string? AccountNumber { get; set; }
        public string? UserEmail { get; set; }
        public string? ClientEmail { get; set; }
        public string? AccountEmail { get; set; }
        public string? Type { get; set; }
        public string? AccountType { get; set; }
        public bool? IsActive { get; set; }
        public string? Report1099Name { get; set; }
        public string? PhoneNumber { get; set; }
    }
}
