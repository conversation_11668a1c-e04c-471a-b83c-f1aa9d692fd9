﻿namespace TIQuest.Api.DTO.Response
{
    public class TransactionDetailResponse
    {
        public int? Id { get; set; }
        public int? Account { get; set; }
        public decimal? Amount { get; set; }
        public DateOnly? Date { get; set; }
        public string? Description { get; set; }
        public string? Status { get; set; }
        public string? Code { get; set; }
        public CheckRegisterResponse? CheckDetails { get; set; }
        public string? WireNumber { get; set; }
        public string? AchDetails { get; set; }
        public int? ToAccount { get; set; }
        public string? BankTransactionType { get; set; }

	 }

    public class CheckRegisterResponse
    {
        public int? Number { get; set; }
        public string? Payee { get; set; }
        public string Address { get; set; }
        public string? Apartment { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? Zip { get; set; }
        public string Country { get; set; }
        public string? Memo { get; set; }
    }
}
