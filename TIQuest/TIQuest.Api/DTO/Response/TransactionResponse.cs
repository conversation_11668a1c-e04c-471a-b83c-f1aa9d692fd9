﻿namespace TIQuest.Api.DTO.Response
{
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
    public class TransactionAccountResponse
    {
        public int? Id { get; set; }
        public TransactionInvestorResponse? Investor { get; set; }
        public string? AccountNumber { get; set; }
        public string? Name { get; set; }
        public decimal? Balance { get; set; }
        public IEnumerable<TransactionResponse>? Transactions { get; set; }
    }

    public class TransactionResponse
    {
        public int? Id { get; set; }
        public decimal? Amount { get; set; }
        public DateOnly? Date { get; set; }
        public string? Description { get; set; }
        public string? Status { get; set; }
        public string? Code { get; set; }
        public int? CheckNumber { get; set; }
        public string? WireNumber { get; set; }
        public string? AchDetails { get; set; }
        public DateTime? CreatedOn { get; set; }
        public string? BankTransactionType { get; set; }

	 }

    public class TransactionInvestorResponse
    {
        public int? Id { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? CompanyName { get; set; }
        public string? Type { get; set; }
    }
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
}
