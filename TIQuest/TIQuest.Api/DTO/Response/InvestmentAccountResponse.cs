﻿namespace TIQuest.Api.DTO.Response
{
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
    public class InvestmentAccountResponse
    {
        public int? Id { get; set; }
        public int? Investor { get; set; }
        public string? InvestorName { get; set; }
        public string? Name { get; set; }
        public string? Email { get; set; }
        public string? AccountNumber { get; set; }
        public string? Report1099Name { get; set; }
        public decimal? Rate { get; set; }
        public decimal? Balance { get; set; }
        public DateTimeOffset? StartDate { get; set; }
        public DateTimeOffset? EndDate { get; set; }
        public string? AccountType { get; set; }
        public string? InterestType { get; set; }
        public bool? IsActive { get; set; }
        public bool? SendEmail { get; set; }
        public bool? SendMail { get; set; }
        public string? TaxNumber { get; set; }
    }
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
}
