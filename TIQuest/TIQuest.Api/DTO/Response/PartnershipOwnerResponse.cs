﻿namespace TIQuest.Api.DTO.Response
{
    public class PartnershipOwnerResponse
    {
        public int? Id { get; set; }
        public int? Partnership { get; set; }
        public PartnershipAccountResponse? Account { get; set; }
        public PartnershipInvestorResponse? Investor { get; set; }
        public decimal? Percentage { get; set; }
    }

    public class PartnershipAccountResponse
    {
        public int? Id { get; set; }
        public string? Name { get; set; }
        public bool? IsActive { get; set; }
        public string AccountNumber { get; set; }
    }

    public class PartnershipInvestorResponse
    {
        public int? Id { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? CompanyName { get; set; }
    }
}
