﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using TIQuest.Api.Interfaces.Managers;
using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Extensions
{
    internal static class ClaimsManagerExtensions
    {
        internal static IClaimsManager GetDefaultClaims(this IClaimsManager claimsManager, int identifier)
        {
            return claimsManager.AddClaim(JwtRegisteredClaimNames.Sub, identifier.ToString())
                         .AddClaim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
                         .AddClaim(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer);
        }

        internal static IClaimsManager GetDefaultUserClaims(this IClaimsManager claimsManager, User user)
        {
            claimsManager.GetDefaultClaims(user.Id)
                         .AddClaim(ClaimTypes.Email, user.Email)
                         .AddClaim(ClaimTypes.Role, user.Role);

            return claimsManager;
        }

        internal static IClaimsManager AddRoleClaim(this IClaimsManager claimsManager, string role)
        {
            return claimsManager.AddClaim(ClaimTypes.Role, role);
        }
    }
}
