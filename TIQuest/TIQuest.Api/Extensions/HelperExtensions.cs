﻿using Humanizer;
using System.Globalization;
using System.Text;
using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Extensions
{
    internal static class HelperExtensions
    {
        internal static bool IsActive(this InvestmentAccount account)
            => account.EndDate is null || account.EndDate > DateTimeOffset.UtcNow;

        internal static string ConvertToWords(this decimal amount)
        {
            var stringBuilder = new StringBuilder();
            var integerPart = (int)decimal.Truncate(amount);
            if (integerPart > 0)
            {
                stringBuilder.Append(integerPart.ToWords().Transform(To.TitleCase));
                if (integerPart > 1)
                {
                    stringBuilder.Append($" {"Dollars"}");
                }
                else
                {
                    stringBuilder.Append($" {"Dollar"}");
                }
            }
            if (!decimal.IsInteger(amount))
            {
                var fractionalPart = (int)((amount - integerPart) * 100);
                var fractionalPartText = fractionalPart.ToWords();
                if (fractionalPart > 0)
                {
                    stringBuilder.Append($" and {fractionalPartText}");
                    if (fractionalPart > 1)
                    {
                        stringBuilder.Append($" {"Cents"}");
                    }
                    else
                    {
                        stringBuilder.Append($" {"Cent"}");
                    }
                }
            }

            return stringBuilder.ToString();
        }

        internal static string FormatAsPercentage(this decimal percent) => Math.Round(percent * 100, 4).ToString() + "%";

        internal static string FormatAsCurrency(this decimal amount) => amount >= 0
                            ? amount.ToString("C", CultureInfo.GetCultureInfo("en-US"))
                            : $"({Math.Abs(amount).ToString("C", CultureInfo.GetCultureInfo("en-US"))})";

        internal static string FormatTaxID(this string? taxID, int format)
        {
            if (!string.IsNullOrWhiteSpace(taxID))
            {
                string newTaxID = CleanTaxID(taxID);
                if (newTaxID.Length == 9)
                {
                    switch (format)
                    {
                        case 1: return newTaxID.Substring(0, 3) + "-" + newTaxID.Substring(3, 2) + "-" + newTaxID.Substring(5, 4);
                        case 2: return newTaxID.Substring(0, 2) + "-" + newTaxID.Substring(2, 7);
                        default: return newTaxID.Substring(0, 3) + "-" + newTaxID.Substring(3, 2) + "-" + newTaxID.Substring(5, 4);
                    }
                }
                else
                {
                    return "";
                }
            }
            else
            {
                return "";
            }
        }

        private static string CleanTaxID(string taxID)
        {
            return taxID.Replace("-", "").Replace(" ", "");
        }
    }
}
