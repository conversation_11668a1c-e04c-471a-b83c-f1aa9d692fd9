﻿using TIQuest.Api.Factories;
using TIQuest.Api.Interfaces.DataProviders;
using TIQuest.Api.Interfaces.Factories;
using TIQuest.Api.Interfaces.Managers;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Managers;
using TIQuest.Api.Reports.Builders;
using TIQuest.Api.Reports.DataProviders;
using TIQuest.Api.Services;

namespace TIQuest.Api.Extensions
{
    internal static class ServiceCollectionExtensions
    {
        internal static IServiceCollection RegisterServices(this IServiceCollection services)
        {
            services.AddSingleton<IUserService, UserService>();
            services.AddSingleton<IInvestorService, InvestorService>();
            services.AddSingleton<IRoleService, RoleService>();
            services.AddSingleton<ITokenMailService, TokenMailService>();
            services.AddSingleton<ITransactionService, TransactionService>();
            services.AddSingleton<IHasher, Sha256Hasher>();
            services.AddSingleton<IEmailService, EmailService>();
            services.AddSingleton<IReportService, ReportService>();
            services.AddSingleton<IExportEngineFactory, ExportEngineFactory>();
            services.AddSingleton<IReportBuilderFactory, ReportBuilderFactory>();
            services.AddSingleton<IStatement1099DataProvider, Statement1099DataProvider>();
            services.AddSingleton<IForm1096DataProvider, Statement1096DataProvider>();
            services.AddSingleton<IInvestmentAccountService, InvestmentAccountService>();
            services.AddSingleton<ITransactionCodeService, TransactionCodeService>();
            services.AddSingleton<IMonthEndProcessService, MonthEndProcessService>();
            services.AddSingleton<IAzureStorageService, AzureStorageService>();
            services.AddSingleton<ITransactionManagerFactory, TransactionManagerFactory>();
            services.AddSingleton<ISettingService, SettingService>();
            services.AddSingleton<ITokenService, TokenService>();
            services.AddSingleton<IPartnershipService, PartnershipService>();
            services.AddSingleton<ITemplateGeneratorService, TemplateGeneratorService>();
            services.AddSingleton<IDistributionService, DistributionService>();
            services.AddSingleton<ITransactionOperationService, TransactionOperationService>();
            services.AddSingleton<ICheckRegisterService, CheckRegisterService>();
            services.AddSingleton<IAchRegisterService, AchRegisterService>();
            services.AddSingleton<ICheckDataProvider, CheckDataProvider>();
            services.AddSingleton<IClientAccountDataProvider, ClientAccountDataProvider>();
            services.AddSingleton<IAccountStatementDataProvider, AccountStatementDataProvider>();
            services.AddSingleton<ICsvManager, CsvManager>();
            services.AddSingleton<ICompanyReportService, CompanyReportService>();
            services.AddSingleton<ITransactionDataProvider, TransactionDataProvider>();
            services.AddSingleton<IDistributionTransactionDataProvider, DistributionTransactionDataProvider>();
            services.AddSingleton<IEmailLogService, EmailLogService>();
            services.AddSingleton<IAuditLogService, AuditLogService>();

            services.AddScoped<IClaimsManager, ClaimsManager>();

            services.AddTransient<CheckBuilder>();
            services.AddTransient<AccountStatementBuilder>();
            services.AddTransient<ClientAccountBuilder>();
            services.AddTransient<TransactionReportBuilder>();
            services.AddTransient<Form1096ReportBuilder>();
            services.AddTransient<DistributionTransactionReportBuilder>();


            return services;
        }
    }
}
