﻿using MimeKit;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Options;
using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Extensions
{
    internal static class EmailExtensions
    {
        internal static async Task<string> SendEmailAsync(this IEmailService emailService, EmailSettings emailSettings, User user, string subject,
            string body, bool isHtml, IEnumerable<(string Name, Stream Content)>? attachments = null)
        {
            return await emailService.SendAsync(new MailboxAddress(emailSettings.Name, emailSettings.EmailId),
                new List<MailboxAddress> { new MailboxAddress(user.FirstName, user.Email) }, null, null, subject: subject, body: body, isHtml, attachments);
        }
    }
}
