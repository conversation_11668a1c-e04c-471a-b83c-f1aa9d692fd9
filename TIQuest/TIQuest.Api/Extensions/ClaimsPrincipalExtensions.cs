﻿using System.Security.Claims;
using TIQuest.Api.Enums;

namespace TIQuest.Api.Extensions
{
    internal static class ClaimsPrincipalExtensions
    {
        internal static int? GetUserIdFromClaims(this ClaimsPrincipal claimsPrincipal)
        {
            Claim? userIdClaim = claimsPrincipal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier);
            if (int.TryParse(userIdClaim?.Value, out int userId))
            {
                return userId;
            }
            return null;
        }

        internal static UserRole GetUserRoleFromClaims(this ClaimsPrincipal claimsPrincipal)
        {
            Claim? userIdClaim = claimsPrincipal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Role);
            return userIdClaim?.Value.ToUserRoleEnum() ?? UserRole.None;
        }
    }
}
