﻿using AutoMapper;
using TIQuest.Api.DTO;
using TIQuest.Api.DTO.Request;
using TIQuest.Api.DTO.Request.Investors;
using TIQuest.Api.DTO.Request.Partnerships;
using TIQuest.Api.DTO.Request.Transactions;
using TIQuest.Api.DTO.Response;
using TIQuest.Api.Poco;
using TIQuest.Api.Poco.Entities;
using TIQuest.Api.Poco.Reports;

namespace TIQuest.Api.Mappers
{
    public class TiQuestMapper : Profile
    {
        public TiQuestMapper()
        {
            CreateRequestToEntitiesMaps();

            CreatePocoToResponseMaps();

            CreateEntityToPocoMaps();

            CreatePocoToEntityMaps();

            CreateMap<Entities.AchRegister, AchCsvResponse>()
                .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.Description))
                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => Math.Round(Math.Abs(src.Transaction.Amount), 2)))
                .ForMember(dest => dest.AccountNumber, opt => opt.MapFrom(src => src.Transaction.InvestmentAccount.AccountNumber))
                .ForMember(dest => dest.Client, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.Transaction.InvestmentAccount.AccountInvestor.CompanyName)
                                                                                 ? src.Transaction.InvestmentAccount.AccountInvestor.CompanyName
                                                                                 : $"{src.Transaction.InvestmentAccount.AccountInvestor.LastName} {src.Transaction.InvestmentAccount.AccountInvestor.FirstName}"));

        }

        private void CreatePocoToEntityMaps()
        {
            CreateMap<EmailLog, Entities.EmailLog>()
                .ForMember(dest => dest.Date, opt => opt.MapFrom(src => new DateTime(src.Date, TimeOnly.MinValue)));
        }

        private void CreateEntityToPocoMaps()
        {
            CreateMap<Entities.UserRole, UserRole>();

            CreateMap<Entities.Investor, Investor>();

            CreateMap<Entities.TransactionCode, TransactionCode>();

            CreateMap<Entities.CheckRegister, CheckRegister>()
                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.Transaction.Amount))
                .ForMember(dest => dest.AccountNumber, opt => opt.MapFrom(src => src.Transaction.InvestmentAccount.AccountNumber))
                .ForMember(dest => dest.Client, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.Transaction.InvestmentAccount.AccountInvestor.CompanyName)
                                                                                 ? src.Transaction.InvestmentAccount.AccountInvestor.CompanyName
                                                                                 : $"{src.Transaction.InvestmentAccount.AccountInvestor.LastName} {src.Transaction.InvestmentAccount.AccountInvestor.FirstName}"));

            CreateMap<Entities.AchRegister, AchRegister>()
                .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.Description))
                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.Transaction.Amount))
                .ForMember(dest => dest.AccountNumber, opt => opt.MapFrom(src => src.Transaction.InvestmentAccount.AccountNumber))
                .ForMember(dest => dest.Client, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.Transaction.InvestmentAccount.AccountInvestor.CompanyName)
                                                                                 ? src.Transaction.InvestmentAccount.AccountInvestor.CompanyName
                                                                                 : $"{src.Transaction.InvestmentAccount.AccountInvestor.LastName} {src.Transaction.InvestmentAccount.AccountInvestor.FirstName}"));

            CreateMap<Entities.InvestmentAccount, InvestmentAccount>()
                .ForMember(dest => dest.Investor, opt => opt.MapFrom(src => src.AccountInvestor));

            CreateMap<Entities.Transaction, Transaction>()
                .ForMember(dest => dest.Date, opt => opt.MapFrom(src => DateOnly.FromDateTime(src.Date)))
                .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.TransactionCode.Description))
                .ForMember(dest => dest.Account, opt => opt.MapFrom(src => src.InvestmentAccount));

            CreateMap<Entities.TransactionStatus, TransactionStatus>();

            CreateMap<Entities.Partnership, Partnership>();

            CreateMap<Entities.PartnershipOwner, PartnershipOwner>();

            CreateMap<Entities.Distribution, Distribution>()
                .ForMember(dest => dest.Date, opt => opt.MapFrom(src => DateOnly.FromDateTime(src.Date)));

            CreateMap<Entities.MonthEndProcess, MonthEndProcess>();

            CreateMap<Entities.EmailLog, EmailLog>()
                .ForMember(dest => dest.Date, opt => opt.MapFrom(src => DateOnly.FromDateTime(src.Date)));
        }

        private void CreatePocoToResponseMaps()
        {
            CreateMap<User, UserResponse>();

            CreateMap<UserStatement, UserStatements>();

            CreateMap<TransactionAccountRecord, TransactionAccountResponse>();

            CreateMap<ClientDetailReport, ClientReportResponse>();

            CreateMap<OutOfBalanceReport, OutOfBalanceResponse>();

            CreateMap<CashAccountStatementReport, CashAccountStatementResponse>();

            CreateMap<TransactionStatementRecord, StatementDetailResponse>()
                .ForMember(dest => dest.Investor, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.Investor.CompanyName) ? src.Investor.CompanyName : $"{src.Investor.LastName} {src.Investor.FirstName}"))
                .ForMember(dest => dest.Account, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Debits, opt => opt.MapFrom(src => src.Debit))
                .ForMember(dest => dest.Credits, opt => opt.MapFrom(src => src.Credit));

            CreateMap<Transaction, TransactionResponse>()
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.TransactionStatus.Description))
                .ForMember(dest => dest.CheckNumber, opt => opt.MapFrom(src => src.CheckRegister != null ? src.CheckRegister.Number : (int?)null));

            CreateMap<Transaction, TransactionDetailResponse>()
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.TransactionStatus.Description))
                .ForMember(dest => dest.CheckDetails, opt => opt.MapFrom(src => src.CheckRegister))
                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => Math.Abs(src.Amount)))
                .ForMember(dest => dest.Account, opt => opt.MapFrom(src => src.Account.Id))
                .ForMember(dest => dest.ToAccount, opt => opt.MapFrom(src => src.ParentTransaction != null ? src.ParentTransaction.Account.Id : (int?)null));

            CreateMap<InvestmentAccount, InvestmentAccountResponse>()
                .ForMember(dest => dest.StartDate, opt => opt.MapFrom(src => new DateTimeOffset(src.StartDate, TimeSpan.Zero)))
                .ForMember(dest => dest.Investor, opt => opt.MapFrom(src => src.Investor.Id))
                .ForMember(dest => dest.InvestorName, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.Investor.CompanyName) ? src.Investor.CompanyName : $"{src.Investor.LastName} {src.Investor.FirstName}"))
                .ForMember(dest => dest.EndDate, opt => opt.MapFrom(src => src.EndDate.HasValue ? new DateTimeOffset(src.EndDate.Value, TimeSpan.Zero) : (DateTimeOffset?)null));

            CreateMap<Investor, TransactionInvestorResponse>();
            CreateMap<Investor, InvestorResponse>();

            CreateMap<CheckRegister, CheckRegisterResponse>();
            CreateMap<CheckRegister, CheckDetailResponse>()
                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => Math.Abs(src.Amount)));

            CreateMap<AchRegister, AchDetailResponse>()
                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => Math.Abs(src.Amount)));

            CreateMap<Partnership, PartnershipResponse>();
            CreateMap<Partnership, PartnershipDetailResponse>();

            CreateMap<PartnershipOwner, PartnershipOwnerResponse>()
                .ForMember(dest => dest.Partnership, opt => opt.MapFrom(src => src.PartnershipId));
            CreateMap<InvestmentAccount, PartnershipAccountResponse>();
            CreateMap<Investor, PartnershipInvestorResponse>();

            CreateMap<Distribution, DistributionResponse>();

            CreateMap<MonthEndProcess, MonthEndProcessResponse>();

            CreateMap<EmailLog, EmailLogResponse>();

            CreateMap<AuditLog, AuditLogResponse>();
        }

        private void CreateRequestToEntitiesMaps()
        {
            CreateMap<NewUserRequest, Entities.User>()
                .ForMember(dest => dest.UserRoleId, opt => opt.MapFrom(src => src.Role));

            CreateMap<UserRequest, Entities.User>();

            CreateMap<TransactionRequest, Entities.Transaction>()
                .ForMember(dest => dest.CodeId, opt => opt.MapFrom(src => src.TransactionType))
                .ForMember(dest => dest.Date, opt => opt.MapFrom(src => new DateTime(src.Date, TimeOnly.MinValue)))
                .ForMember(dest => dest.AccountId, opt => opt.MapFrom(src => src.Account))
                .ForMember(dest => dest.CreatedOn, opt => opt.MapFrom(src => DateTime.UtcNow))
                .ForMember(dest => dest.LastModifiedOn, opt => opt.MapFrom(src => DateTime.UtcNow));

            CreateMap<WireTransactionRequest, Entities.Transaction>()
                .ForMember(dest => dest.WireNumber, opt => opt.MapFrom(src => src.WireNumber))
                .ForMember(dest => dest.Date, opt => opt.MapFrom(src => new DateTime(src.Date, TimeOnly.MinValue)))
                .ForMember(dest => dest.CodeId, opt => opt.MapFrom(src => src.TransactionType))
                .ForMember(dest => dest.AccountId, opt => opt.MapFrom(src => src.Account))
                .ForMember(dest => dest.CreatedOn, opt => opt.MapFrom(src => DateTime.UtcNow))
                .ForMember(dest => dest.LastModifiedOn, opt => opt.MapFrom(src => DateTime.UtcNow));

            CreateMap<CheckWithdrawalRequest, Entities.CheckRegister>()
                .ForMember(dest => dest.Date, opt => opt.MapFrom(src => new DateTime(src.Date, TimeOnly.MinValue)));

            CreateMap<NewInvestorRequest, Entities.Investor>();

            CreateMap<EditInvestorRequest, Entities.Investor>();

            CreateMap<AccountRequest, Entities.InvestmentAccount>()
                .ForMember(dest => dest.InvestorId, opt => opt.MapFrom(src => src.Investor))
                .ForMember(dest => dest.StartDate, opt => opt.MapFrom(src => src.StartDate.UtcDateTime))
                .ForMember(dest => dest.EndDate, opt => opt.MapFrom(src => src.EndDate.HasValue ? src.EndDate.Value.UtcDateTime : (DateTime?)null));

            CreateMap<PartnershipRequest, Entities.Partnership>();

            CreateMap<EditPartnershipRequest, Entities.Partnership>();

            CreateMap<AddPartnershipInvestorRequest, Entities.PartnershipOwner>()
                .ForMember(dest => dest.AccountId, opt => opt.MapFrom(src => src.Account));

            CreateMap<NewDistributionRequest, Entities.Distribution>()
                .ForMember(dest => dest.Date, opt => opt.MapFrom(src => new DateTime(src.Date, TimeOnly.MinValue)))
                .ForMember(dest => dest.PartnershipId, opt => opt.MapFrom(src => src.Partnership))
                .ForMember(dest => dest.Partnership, opt => opt.Ignore());
        }
    }
}
