﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TIQuest.Api.Entities;
using TIQuest.Api.Enums;
using TIQuest.Api.Interfaces.Factories;
using TIQuest.Api.Interfaces.Managers;
using TIQuest.Api.Managers.Transactions;

namespace TIQuest.Api.Factories
{
    internal class TransactionManagerFactory : ITransactionManagerFactory
    {
        private readonly IDbContextFactory<DataContext> _dbContext;
        private readonly IMapper _mapper;

        public TransactionManagerFactory(IDbContextFactory<DataContext> dbContext, IMapper mapper)
        {
            _dbContext = dbContext;
            _mapper = mapper;
        }

        public ITransactionManager GetTransactionManager(TransactionType transactionType)
        {
            return transactionType switch
            {
                TransactionType.Deposit => new DepositManager(_dbContext, _mapper),
                TransactionType.Wire => new WireWithdrawalManager(_dbContext, _mapper),
                TransactionType.ACH => new AchWithdrawalManager(_dbContext, _mapper),
                TransactionType.Check => new CheckWithdrawalManager(_dbContext, _mapper),
                TransactionType.Transfer => new TransferManager(_dbContext, _mapper),
                TransactionType.LoanPayment => new LoanPaymentManager(_dbContext, _mapper),
                TransactionType.LoanAdvance => new LoanAdvanceManager(_dbContext, _mapper),
                _ => throw new NotImplementedException(),
            };
        }
    }
}
