﻿using TIQuest.Api.Enums;
using TIQuest.Api.Exports;
using TIQuest.Api.Interfaces.Factories;

namespace TIQuest.Api.Factories
{
    internal class ExportEngineFactory : IExportEngineFactory
    {
        public IExportEngine GetExportEngine(ExportFormat exportFormat)
        {
            return exportFormat switch
            {
                ExportFormat.Pdf => new PdfExportEngine(),
                ExportFormat.Excel => new ExcelExportEngine(),
                _ => throw new NotSupportedException(),
            };
        }
    }
}
