﻿using TIQuest.Api.Enums;
using TIQuest.Api.Interfaces.Factories;
using TIQuest.Api.Interfaces.Reports;
using TIQuest.Api.Reports.Builders;

namespace TIQuest.Api.Factories
{
    internal class ReportBuilderFactory : IReportBuilderFactory
    {
        private readonly IServiceProvider _serviceProvider;

        public ReportBuilderFactory(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }
        public IReportBuilder GetReportBuilder(ReportType reportType)
        {
            return reportType switch
            {
                ReportType.Check => _serviceProvider.GetRequiredService<CheckBuilder>(),
                ReportType.ClientAccount => _serviceProvider.GetRequiredService<ClientAccountBuilder>(),
                ReportType.AccountStatement => _serviceProvider.GetRequiredService<AccountStatementBuilder>(),
                ReportType.Transactions => _serviceProvider.GetRequiredService<TransactionReportBuilder>(),
                ReportType.Form1096 => _serviceProvider.GetRequiredService<Form1096ReportBuilder>(),
                ReportType.DistributionTransaction => _serviceProvider.GetRequiredService<DistributionTransactionReportBuilder>(),
                _ => throw new NotImplementedException()
            };
        }
    }
}
