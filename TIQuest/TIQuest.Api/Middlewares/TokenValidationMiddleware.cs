﻿using Microsoft.Extensions.Caching.Memory;

namespace TIQuest.Api.Middlewares
{
    internal class TokenValidationMiddleware : IMiddleware
    {
        private readonly IMemoryCache _cache;
        public TokenValidationMiddleware(IMemoryCache cache)
        {
            _cache = cache;
        }
        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            string token = context.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
            // token is in cache, indicating it is blacklisted
            if (_cache.TryGetValue(token, out string? _))
            {
                var response = context.Response;
                response.StatusCode = StatusCodes.Status401Unauthorized;
                return;
            }

            await next(context);
        }
    }
}
