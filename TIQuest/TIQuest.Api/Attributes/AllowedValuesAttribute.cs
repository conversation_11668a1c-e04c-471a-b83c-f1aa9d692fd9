﻿using System.ComponentModel.DataAnnotations;

namespace TIQuest.Api.Attributes
{
    //TODO : Check if this can be removed.
    // .Net 8 has added this but that is not case insensitive.
    public class AllowedValuesAttribute : ValidationAttribute
    {
        private readonly string[] _allowedValues;
        private readonly bool _isCaseSensitive;

        public AllowedValuesAttribute(string[] allowedValues) : this(allowedValues, false)
        {
        }

        public AllowedValuesAttribute(string[] allowedValues, bool isCaseSensitive)
        {
            _allowedValues = allowedValues;
            _isCaseSensitive = isCaseSensitive;
        }

        protected override ValidationResult IsValid(object? value, ValidationContext validationContext)
        {
            if (value is null)
            {
                return ValidationResult.Success;
            }

            if (_isCaseSensitive)
            {
                if (_allowedValues.Contains(value.ToString()))
                {
                    return ValidationResult.Success;
                }
            }
            else
            {
                if (_allowedValues.Contains(value.ToString(), StringComparer.OrdinalIgnoreCase))
                {
                    return ValidationResult.Success;
                }
            }

            return new ValidationResult($"{value} is not a valid value for {validationContext.MemberName}.");
        }
    }
}
