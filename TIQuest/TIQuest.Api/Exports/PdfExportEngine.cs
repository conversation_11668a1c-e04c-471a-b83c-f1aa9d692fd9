﻿using GrapeCity.ActiveReports;
using GrapeCity.ActiveReports.Export.Pdf.Section;

namespace TIQuest.Api.Exports
{
    internal class PdfExportEngine : IExportEngine
    {
        public Stream Export(SectionReport report)
        {
            using (PdfExport PdfExport = new())
            {
                Stream memStream = new MemoryStream();
                PdfExport.Export(report.Document, memStream);
                memStream.Position = 0;
                return memStream;
            }
        }
    }
}
