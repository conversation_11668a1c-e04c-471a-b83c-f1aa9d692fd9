﻿using GrapeCity.ActiveReports;
using GrapeCity.ActiveReports.Export.Excel.Section;

namespace TIQuest.Api.Exports
{
    internal class ExcelExportEngine : IExportEngine
    {
        public Stream Export(SectionReport report)
        {
            using (XlsExport excelExport = new()
            {
                RemoveVerticalSpace = true,
                MinColumnWidth = 0.125F,
                UseCellMerging = true
            })
            {
                Stream memStream = new MemoryStream();
                excelExport.Export(report.Document, memStream);
                memStream.Position = 0;
                return memStream;
            }
        }
    }
}
