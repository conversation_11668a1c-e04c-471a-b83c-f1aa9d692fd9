﻿using TIQuest.Api.DTO.Request.Transactions;
using TIQuest.Api.Enums;
using TIQuest.Api.Poco;
using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Interfaces.Services
{
	/// <summary>
	/// Interface for transaction service
	/// </summary>
	public interface ITransactionService
	{
		/// <summary>
		/// Find a transaction by id
		/// </summary>
		/// <param name="id">transaction Id</param>
		/// <param name="cancellationToken">Cancellation Token</param>
		/// <returns>Matching transaction record</returns>
		Task<Transaction?> FindByIdAsync(int id, CancellationToken cancellationToken = default);

		/// <summary>
		/// Get a transaction by id
		/// </summary>
		/// <param name="id">transaction Id</param>
		/// <param name="cancellationToken">Cancellation Token</param>
		/// <returns>Matching transaction record</returns>
		Task<Transaction?> GetByIdAsync(int id, CancellationToken cancellationToken = default);

		/// <summary>
		/// Get all transactions for the given user for the given period
		/// </summary>
		/// <param name="userId">Id of the user</param>
		/// <param name="isClient"></param>
		/// <param name="startDate">Start date for the period</param>
		/// <param name="endDate">End date for the period</param>
		/// <param name="excludeTransactionCodes">Transaction codes to exclude</param>
		/// <param name="cancellationToken">Cancellation token</param>
		/// <returns>List of transactions</returns>
		Task<IEnumerable<TransactionAccountRecord>> GetUserTransactionsAsync(int userId, bool isClient, DateOnly startDate, DateOnly endDate, string? excludeTransactionCodes, CancellationToken cancellationToken = default);

		/// <summary>
		/// Get transactions related to a specific distribution (excluding cash account transactions)
		/// </summary>
		/// <param name="userId">Id of the user</param>
		/// <param name="isClient">Whether the user is a client</param>
		/// <param name="distributionId">Distribution ID to filter transactions</param>
		/// <param name="startDate">Start date for the period (optional)</param>
		/// <param name="endDate">End date for the period (optional)</param>
		/// <param name="excludeTransactionCodes">Transaction codes to exclude</param>
		/// <param name="cancellationToken">Cancellation token</param>
		/// <returns>List of distribution transactions</returns>
		Task<IEnumerable<TransactionAccountRecord>> GetDistributionTransactionsAsync(int userId, bool isClient, int distributionId, DateOnly? startDate, DateOnly? endDate, string? excludeTransactionCodes, CancellationToken cancellationToken = default);

		/// <summary>
		/// Record a new transaction
		/// </summary>
		/// <param name="request">Transaction</param>
		/// <param name="modifierId">Modifying user id</param>
		/// <param name="transactionType">Type of transaction</param>
		/// <param name="cancellationToken">Cancellation token</param>
		Task<int> RecordTransactionAsync(TransactionRequest request, int modifierId, TransactionType transactionType, CancellationToken cancellationToken = default);

		/// <summary>
		/// Void a transaction
		/// </summary>
		/// <param name="transactionId">Id of transaction</param>
		/// <param name="modifierId">Modifying user id</param>
		/// <param name="cancellationToken">Cancellation token</param>
		Task VoidTransactionAsync(int transactionId, int modifierId, CancellationToken cancellationToken = default);
		/// <summary>
		/// Reconcile a transaction
		/// </summary>
		/// <param name="transactionId">Id of transaction</param>
		/// <param name="modifierId">Modifying user id</param>
		/// <param name="cancellationToken">Cancellation token</param>
		Task ReconcileTransactionAsync(int transactionId, int modifierId, CancellationToken cancellationToken = default);
		/// <summary>
		/// Unreconcile a transaction
		/// </summary>
		/// <param name="transactionId">Id of transaction</param>
		/// <param name="modifierId">Modifying user id</param>
		/// <param name="cancellationToken">Cancellation token</param>
		Task UnreconcileTransactionAsync(int transactionId, int modifierId, CancellationToken cancellationToken = default);
		/// <summary>
		/// Checks if a transaction is a distribution transaction
		/// </summary>
		/// <param name="transactionId">Transaction Id</param>
		/// <param name="cancellationToken">Cancellation token</param>
		/// <returns>Boolean value to indicate if a transaction is part of distributions</returns>
		Task<bool> IsDistributionTransactionAsync(int transactionId, CancellationToken cancellationToken = default);
		/// <summary>
		/// Checks if a transaction is part of month end process
		/// </summary>
		/// <param name="transactionId"></param>
		/// <param name="cancellationToken"></param>
		/// <returns></returns>
		Task<bool> IsMonthEndTransactionAsync(int transactionId, CancellationToken cancellationToken = default);

		/// <summary>
		/// Update an existing transaction
		/// </summary>
		/// <param name="transactionId"></param>
		/// <param name="request">Update transaction request</param>
		/// <param name="lastModifiedBy"></param>
		/// <param name="cancellationToken">Cancellation token</param>
		/// <returns>Id of the updated transaction</returns>
		Task<int> UpdateTransactionAsync(int transactionId, UpdateTransactionRequest request, int lastModifiedBy, CancellationToken cancellationToken = default);
	}
}
