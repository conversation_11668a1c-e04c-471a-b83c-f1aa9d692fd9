﻿using TIQuest.Api.Entities;
using TIQuest.Api.Poco.Transactions;

namespace TIQuest.Api.Interfaces.Services
{
    //TODO : Use this interface to encapsulate individual operations
    // Sort of unit of work pattern
    public interface ITransactionOperationService
    {
        Task<(Transaction First, Transaction Second)> CreateDepositAsync(DataContext ctx, DepositTransaction transaction, CancellationToken cancellationToken = default);

        Task<(Transaction First, Transaction Second)> CreateSystemCashWithdrawalAsync(DataContext ctx, DepositTransaction transaction, CancellationToken cancellationToken = default);

        Task<(Transaction First, Transaction Second, AchRegister Ach)> CreateAchWithdrawalAsync(DataContext ctx, AchTransaction transaction, CancellationToken cancellationToken = default);

        Task<(Transaction First, Transaction Second, CheckRegister Check)> CreateCheckWithdrawalAsync(DataContext ctx, CheckTransaction transaction, CancellationToken cancellationToken = default);

        Task<(Transaction VoidedTransaction, Transaction VoidedChildTransaction)> VoidAsync(DataContext ctx, Transaction parentTransaction, Transaction childTransaction, int voidStatusId, int modifierId, CancellationToken cancellationToken = default);
    }
}
