﻿using MimeKit;

namespace TIQuest.Api.Interfaces.Services
{
    /// <summary>
    /// Represents a service for sending email messages.
    /// </summary>
    public interface IEmailService
    {
        /// <summary>
        /// Sends an email asynchronously.
        /// </summary>
        /// <param name="from">The sender's email address.</param>
        /// <param name="to">A list of recipient email addresses.</param>
        /// <param name="cc">A list of CC (carbon copy) email addresses.</param>
        /// <param name="bcc">A list of BCC (blind carbon copy) email addresses.</param>
        /// <param name="subject">The subject of the email.</param>
        /// <param name="body">The body content of the email.</param>
        /// <param name="isHtml">Specifies whether the email body is in HTML format (default is false).</param>
        /// <param name="attachments"></param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public Task<string> SendAsync(MailboxAddress from, IList<MailboxAddress> to, IList<MailboxAddress>? cc, IList<MailboxAddress>? bcc,
                                string subject, string body, bool isHtml = false, IEnumerable<(string Name, Stream Content)>? attachments = null);
    }
}
