﻿using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Interfaces.Services
{
    /// <summary>
    /// Interface for template generators
    /// </summary>
    public interface ITemplateGeneratorService
    {
        /// <summary>
        /// Get the change password template
        /// </summary>
        /// <param name="appUser">Instance of <see cref="User"/></param>
        /// <param name="token">Token to add to the template</param>
        /// <returns>string representation of template HTML</returns>
        string GetChangePasswordTemplate(User appUser, string token);
        /// <summary>
        /// Get the reset password template
        /// </summary>
        /// <param name="appUser">Instance of <see cref="User"/></param>
        /// <param name="token">Token to add to the template</param>
        /// <returns>string representation of template HTML</returns>
        string GetResetPasswordTemplate(User appUser, string token);
        /// <summary>
        /// Get the create user template
        /// </summary>
        /// <param name="appUser">Instance of <see cref="User"/></param>
        /// <param name="token">Token to add to the template</param>
        /// <returns>string representation of template HTML</returns>
        string GetCreateUserTemplate(User appUser, string token);
    }
}
