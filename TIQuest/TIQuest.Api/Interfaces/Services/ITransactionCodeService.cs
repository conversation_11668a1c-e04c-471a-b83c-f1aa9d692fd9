﻿using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Interfaces.Services
{
    /// <summary>
    /// 
    /// </summary>
    public interface ITransactionCodeService
    {
        /// <summary>
        /// Find Transaction code by Id
        /// </summary>
        /// <param name="id">Id to find with</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Matching Transaction code entity or null if no match found</returns>
        Task<TransactionCode?> FindByIdAsync(int id, CancellationToken cancellationToken = default);
    }
}
