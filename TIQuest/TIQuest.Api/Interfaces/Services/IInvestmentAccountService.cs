﻿using TIQuest.Api.DTO.Request;
using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Interfaces.Services
{
    /// <summary>
    /// 
    /// </summary>
    public interface IInvestmentAccountService
    {
        /// <summary>
        /// Get all investment accounts
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of investment accounts</returns>
        Task<IEnumerable<InvestmentAccount>> GetInvestmentAccountsAsync(int userId, CancellationToken cancellationToken = default);
        /// <summary>
        /// Find Account by Id
        /// </summary>
        /// <param name="id">Id to find with</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Matching Account entity or null if no match found</returns>
        Task<InvestmentAccount?> FindByIdAsync(int id, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get investment accounts for an investor
        /// </summary>
        /// <param name="investorId">Investor Id</param>
        /// <param name="userId"></param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of accounts for investor</returns>
        Task<IEnumerable<InvestmentAccount>> GetAccountsByInvestorAsync(int investorId, int userId, CancellationToken cancellationToken = default);
        /// <summary>
        /// Create a new investment account
        /// </summary>
        /// <param name="account">Instance of <see cref="AccountRequest"/></param>
        /// <param name="modifierId">Id of the modifier</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        /// <returns>Created account entity</returns>
        Task<InvestmentAccount> CreateAsync(AccountRequest account, int modifierId, CancellationToken cancellationToken = default);
        /// <summary>
        /// Update an existing investment account
        /// </summary>
        /// <param name="account">Instance of <see cref="EditAccountRequest"/></param>
        /// <param name="modifierId">Id of the modifier</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        /// <returns>Updated account entity</returns>
        Task<InvestmentAccount> UpdateAsync(EditAccountRequest account, int modifierId, CancellationToken cancellationToken = default);
    }
}
