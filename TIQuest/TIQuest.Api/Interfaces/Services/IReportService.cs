﻿using TIQuest.Api.DTO.Request.Reports;
using TIQuest.Api.Enums;
using TIQuest.Api.Poco;
using TIQuest.Api.Poco.Reports;
using TIQuest.Api.Reports.Filters;

namespace TIQuest.Api.Interfaces.Services
{
    /// <summary>
    /// Interface for reporting service
    /// </summary>
    public interface IReportService
    {
        /// <summary>
        /// Get Statement 1099 file for provided filters and format.
        /// </summary>
        /// <param name="filter">Filter parameters</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        /// <returns>Data Stream</returns>
        public Task<Stream> GetStatement1099Async(Statement1099Filter filter, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get Form 1096 file for provided filters and format.
        /// </summary>
        /// <param name="filter">Filter parameters</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        /// <returns>Data Stream</returns>
        public Task<Stream> GetForm1096Async(Form1096Filter filter, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get Client account statement for provided filters
        /// </summary>
        /// <param name="filter">Filter parameters</param>
        /// <param name="userRole">Filter parameters</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        /// <returns>Data Stream</returns>
        public Task<Stream?> GetClientAccountStatementAsync(ClientAccountFilter filter, TIQuest.Api.Enums.UserRole userRole, CancellationToken cancellationToken = default);

        public Task<IEnumerable<TransactionStatementRecord>> GetStatementDetailsAsync(int month, int year, string accountType, CancellationToken cancellationToken = default);

        public Task<Stream?> GetStatementReportAsync(int month, int year, string accountType, CancellationToken cancellationToken = default);

        public Task<IEnumerable<ClientDetailReport>> GetClientReportDetailsAsync(CancellationToken cancellationToken = default);
        public Task<Stream> GetClientReportStatementAsync(CancellationToken cancellationToken = default);

        public Task<Stream> GetTransactionStatementAsync(int UserId, DateOnly startDate, DateOnly endDate, ExportFormat format, CancellationToken cancellationToken = default);

        public Task<OutOfBalanceReport> GetOutOfBalance(CancellationToken cancellationToken = default);

		  public Task<CashAccountStatementReport> GetCashAccountStatement(CashAccountStatementRequest request, CancellationToken cancellationToken = default);

		/// <summary>
		/// Get distribution transactions as PDF stream
		/// </summary>
		/// <param name="distributionId">Distribution ID</param>
		/// <param name="cancellationToken">Cancellation token</param>
		/// <returns>PDF stream</returns>
		Task<Stream> GetDistributionTransactionsPdfAsync(int distributionId, CancellationToken cancellationToken = default);
	}
}
