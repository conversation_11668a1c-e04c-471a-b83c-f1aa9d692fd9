﻿using TIQuest.Api.DTO.Request.Partnerships;
using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Interfaces.Services
{
    public interface IPartnershipService
    {
        /// <summary>
        /// Get all partnerships
        /// </summary>
        /// <param name="cancellationToken">Cancellation Token</param>
        /// <returns>List of partnerships</returns>
        Task<IEnumerable<Partnership>> GetAllAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Get all partnerships
        /// </summary>
        /// <param name="id">Id of partnership</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        /// <returns>Partnership if exists, else null</returns>
        Task<Partnership?> GetByIdAsync(int id, CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a partnership
        /// </summary>
        /// <param name="request">Input request</param>
        /// <param name="modifierId">Modifying user id</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Created entity</returns>
        Task<Partnership> CreateAsync(PartnershipRequest request, int modifierId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Edit a partnership
        /// </summary>
        /// <param name="request">Input request</param>
        /// <param name="modifierId">Modifying user id</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Created entity</returns>
        Task<Partnership> UpdateAsync(EditPartnershipRequest request, int modifierId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get associated accounts of a partnership
        /// </summary>
        /// <param name="partnershipId">Partnership id</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        /// <returns>List of associated partnership owners</returns>
        Task<IEnumerable<PartnershipOwner>> GetPartnershipInvestorsAsync(int partnershipId, CancellationToken cancellationToken = default);
        /// <summary>
        /// Get owner of a partnership
        /// </summary>
        /// <param name="id">Parnership owner id</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        /// <returns>Partnership owner entity or null if not found</returns>
        Task<PartnershipOwner?> GetPartnershipInvestorAsync(int id, CancellationToken cancellationToken = default);
        /// <summary>
        /// Add an investor to a partnership
        /// </summary>
        /// <param name="request">Input request</param>
        /// <param name="partnershipId">Partnership id</param>
        /// <param name="modifierId">Modifying user id</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        Task AddInvestorAsync(AddPartnershipInvestorRequest request, int partnershipId, int modifierId, CancellationToken cancellationToken = default);
        /// <summary>
        /// Edit an investor in a partnership
        /// </summary>
        /// <param name="request">Input request</param>
        /// <param name="modifierId">Modifying user id</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        Task EditInvestorAsync(EditPartnershipInvestorRequest request, int modifierId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Remove an investor from a partnership
        /// </summary>
        /// <param name="partnershipId">Partnership id</param>
        /// <param name="accountId">Account id</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        Task RemoveInvestorAsync(int partnershipId, int accountId, CancellationToken cancellationToken = default);
    }
}
