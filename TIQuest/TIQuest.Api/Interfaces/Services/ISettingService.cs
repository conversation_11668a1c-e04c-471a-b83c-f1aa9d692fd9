﻿using TIQuest.Api.DTO.Request;
using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Interfaces.Services
{
    /// <summary>
    /// 
    /// </summary>
    public interface ISettingService
    {
        /// <summary>
        /// Get all settings
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of all settings</returns>
        Task<IEnumerable<Setting>> GetSettingsAsync(CancellationToken cancellationToken = default);
        /// <summary>
        /// Get setting value by key
        /// </summary>
        /// <param name="key">Key to find</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Value of setting</returns>
        Task<Setting?> GetSettingAsync(string key, CancellationToken cancellationToken = default);
        /// <summary>
        /// Update setting value
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task UpdateSettingAsync(EditSettingRequest request, CancellationToken cancellationToken = default);
    }
}
