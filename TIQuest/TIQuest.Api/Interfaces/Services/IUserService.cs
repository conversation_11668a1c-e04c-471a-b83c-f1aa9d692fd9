﻿using TIQuest.Api.DTO.Request;
using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Interfaces.Services
{
    /// <summary>
    /// Interface for user service
    /// </summary>
    public interface IUserService
    {
        /// <summary>
        /// Retrieve all users
        /// </summary>
        /// <param name="includeInactive">Include inactive users</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of users in system</returns>
        Task<IEnumerable<User>> GetAllAsync(bool includeInactive, CancellationToken cancellationToken = default);
        /// <summary>
        /// Find user by email id
        /// </summary>
        /// <param name="email">Email to find with</param>
        /// <param name="includeInactive">Include inactive users</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Matching <see cref="User"/> object if found. Returns null if no match is found</returns>
        Task<User?> FindByEmailAsync(string email, bool includeInactive, CancellationToken cancellationToken = default);
        /// <summary>
        /// Find user by Id
        /// </summary>
        /// <param name="id">User Id to search</param>
        /// <param name="includeInactive">Include inactive users</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Matching <see cref="User"/> object if found. Returns null if no match is found</returns>
        Task<User?> FindByIdAsync(int id, bool includeInactive, CancellationToken cancellationToken = default);
        /// <summary>
        /// Updates the password for the user
        /// </summary>
        /// <param name="id">User id</param>
        /// <param name="password">Password string</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        Task UpdatePasswordAsync(int id, string password, CancellationToken cancellationToken = default);
        /// <summary>
        /// Create a user
        /// </summary>
        /// <param name="user">User entity to create</param>
        /// <param name="modifierId">Modifier Id</param>
        /// <param name="userRole">User role</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Created user entity</returns>
        Task<User> CreateUserAsync(NewUserRequest user, int modifierId, Enums.UserRole userRole, CancellationToken cancellationToken = default);

        /// <summary>
        /// Update a user
        /// </summary>
        /// <param name="user">User entity to create</param>
        /// <param name="modifierId">Modifier Id</param>
        /// <param name="userRole">User role</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Created user entity</returns>
        Task<User> UpdateUserAsync(EditUserRequest user, int modifierId, Enums.UserRole userRole, CancellationToken cancellationToken = default);
    }
}
