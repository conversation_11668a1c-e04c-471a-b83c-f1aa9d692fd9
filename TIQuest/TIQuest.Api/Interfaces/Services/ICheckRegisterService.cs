﻿using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Interfaces.Services
{
    public interface ICheckRegisterService
    {
        /// <summary>
        /// Get all check register details
        /// </summary>
        /// <param name="cancellationToken">Cancellation Token</param>
        /// <returns>List of Check Registers</returns>
        Task<IEnumerable<CheckRegister>> GetAllAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Get printed checks for provided ids.
        /// </summary>
        /// <param name="ids">Ids to find</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        /// <returns>Data Stream</returns>
        public Task<Stream> GetChecksAsync(List<int> ids, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates the check number for all the unprinted checks.
        /// </summary>
        /// <param name="nextCheckNumber">Next check number</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task UpdateCheckNumberAsync(int nextCheckNumber, CancellationToken cancellationToken = default);
    }
}
