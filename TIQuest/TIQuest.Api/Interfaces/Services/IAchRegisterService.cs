﻿using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Interfaces.Services
{
    public interface IAchRegisterService
    {
        /// <summary>
        /// Get all ach register details
        /// </summary>
        /// <param name="cancellationToken">Cancellation Token</param>
        /// <returns>List of Ach Registers</returns>
        Task<IEnumerable<AchRegister>> GetAllAsync(CancellationToken cancellationToken = default);
        /// <summary>
        /// Get ach report for the provided ids
        /// </summary>
        /// <param name="ids">Ids to print</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        /// <returns>Data stream</returns>
        Task<Stream> GetAchReportAsync(IEnumerable<int> ids, CancellationToken cancellationToken = default);
    }
}
