﻿using System.Security.Claims;
using TIQuest.Api.Enums;
using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Interfaces.Services
{
    /// <summary>
    /// 
    /// </summary>
    public interface ITokenMailService
    {
        /// <summary>
        /// Create a token from claims provided and email it to the user.
        /// </summary>
        /// <param name="claims">Claims of the user</param>
        /// <param name="templateType">Type of template</param>
        /// <param name="user">User entity</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        Task SendTokenAsync(ICollection<Claim> claims, TemplateType templateType, User user, CancellationToken cancellationToken = default);
    }
}
