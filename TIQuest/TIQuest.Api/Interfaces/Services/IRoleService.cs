﻿using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Interfaces.Services
{
    /// <summary>
    /// Interface for user role service
    /// </summary>
    public interface IRoleService
    {
        /// <summary>
        /// Find role by Id
        /// </summary>
        /// <param name="id">Id to find with</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Matching user role entity or null if no match found</returns>
        Task<UserRole?> FindByIdAsync(int id, CancellationToken cancellationToken = default);
    }
}
