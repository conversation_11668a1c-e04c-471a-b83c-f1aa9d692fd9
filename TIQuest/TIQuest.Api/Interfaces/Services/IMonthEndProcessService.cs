﻿using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Interfaces.Services
{
    /// <summary>
    /// 
    /// </summary>
    public interface IMonthEndProcessService
    {
        /// <summary>
        /// Get all processed months
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of all processed months</returns>
        Task<IEnumerable<MonthEndProcess>> GetAllAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Get latest processed month
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Latest processed month</returns>
        Task<MonthEndProcess> GetLatestMonthEndProcessAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<MonthEndProcess?> GetByIdAsync(int id, CancellationToken cancellationToken = default);
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<DateTime> GetLastProcessedMonthAsync(CancellationToken cancellationToken = default);
        /// <summary>
        /// Get the summary of balance and dues for the investments
        /// </summary>
        /// <param name="monthStartDate">Start date of the month</param>
        /// <param name="monthEndDate">End date of the month</param>
        /// <param name="interestRate">Rate of interest</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Tuple of Average balance of all accounts, and total interest due on that</returns>
        Task<(decimal AverageBalance, decimal InterestDue)> GetInvestmentInterestSummaryAsync(DateTime monthStartDate, DateTime monthEndDate, decimal interestRate, CancellationToken cancellationToken = default);
        /// <summary>
        /// Run month end process
        /// </summary>
        /// <param name="monthStartDate">Start date of the month</param>
        /// <param name="monthEndDate">End date of the month</param>
        /// <param name="interestRate">Rate of interest</param>
        /// <param name="description">Description</param>
        /// <param name="managementRate"></param>
        /// <param name="modifierId"></param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task RunMonthEndProcessAsync(DateTime monthStartDate, DateTime monthEndDate, decimal interestRate, string description, decimal managementRate, int modifierId, CancellationToken cancellationToken = default);
        /// <summary>
        /// Delete the month end process
        /// </summary>
        /// <param name="processId">Id of process</param>
        /// <param name="modifierId">Modifying user id</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task DeleteMonthEndProcessAsync(int processId, int modifierId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the summary of balance and dues for fixed loans
        /// </summary>
        /// <param name="monthStartDate">Start date of the month</param>
        /// <param name="monthEndDate">End date of the month</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Tuple of Average balance of all accounts, and total interest due on that</returns>
        Task<(decimal AverageBalance, decimal InterestDue)> GetFixedLoanInterestSummaryAsync(DateTime monthStartDate, DateTime monthEndDate, CancellationToken cancellationToken = default);
        /// <summary>
        /// Get the summary of balance and dues for variable loans
        /// </summary>
        /// <param name="monthStartDate">Start date of the month</param>
        /// <param name="monthEndDate">End date of the month</param>
        /// <param name="interestDue">Interest due for variable loans</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Tuple of Average balance of all accounts, and total interest due on that and the calculated rate</returns>
        Task<(decimal AverageBalance, decimal ApplicableDue, decimal ApplicableRate)> GetVariableLoanInterestSummaryAsync(DateTime monthStartDate, DateTime monthEndDate, decimal interestDue, CancellationToken cancellationToken = default);
    }
}
