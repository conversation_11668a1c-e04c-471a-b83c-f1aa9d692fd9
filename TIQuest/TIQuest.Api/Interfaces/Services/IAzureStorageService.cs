﻿using Azure.Storage.Queues.Models;

namespace TIQuest.Api.Interfaces.Services
{
    /// <summary>
    /// Represents a service for interacting with Azure Storage services.
    /// </summary>
    public interface IAzureStorageService
    {
        /// <summary>
        /// Enqueues an email message asynchronously for sending.
        /// </summary>
        /// <param name="message">The message of the email message to enqueue.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains a <see cref="SendReceipt"/> indicating the status of the sending process.</returns>
        public Task<string> EnqueueEmailMessageAsync(string message);
    }
}
