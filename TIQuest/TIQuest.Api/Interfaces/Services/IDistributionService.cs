﻿using TIQuest.Api.DTO.Request;
using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Interfaces.Services
{
    /// <summary>
    /// 
    /// </summary>
    public interface IDistributionService
    {
        /// <summary>
        /// Get distributions for a partnership
        /// </summary>
        /// <param name="partnershipId">partnership id</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>All distributions for partnership</returns>
        Task<IEnumerable<Distribution>> GetPartnershipDistributionsAsync(int partnershipId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get distributions for a partnership
        /// </summary>
        /// <param name="distributionId">partnership id</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>All distributions for partnership</returns>
        Task<Distribution?> GetDistributionAsync(int distributionId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a new distribution
        /// </summary>
        /// <param name="request">Instance of <see cref="NewDistributionRequest"/></param>
        /// <param name="modifierId">Modifier id</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns></returns>
        Task CreateDistributionAsync(NewDistributionRequest request, int modifierId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete a distribution
        /// </summary>
        /// <param name="distributionId">Distribution Id</param>
        /// <param name="modifierId">Modifier id</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task DeleteDistributionAsync(int distributionId, int modifierId, CancellationToken cancellationToken = default);
    }
}
