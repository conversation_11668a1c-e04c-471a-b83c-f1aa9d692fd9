﻿using TIQuest.Api.DTO.Request.Investors;
using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Interfaces.Services
{
    /// <summary>
    /// Inteface for InvestorService
    /// </summary>
    public interface IInvestorService
    {
        /// <summary>
        /// Get a list of all investors
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Client entities</returns>
        Task<IEnumerable<Investor>> GetAllInvestorsAsync(CancellationToken cancellationToken = default);
        /// <summary>
        /// Create a new investor entity
        /// </summary>
        /// <param name="investor">Instance of <see cref="NewInvestorRequest"/></param>
        /// <param name="modifierId">Modifying user's id</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        /// <returns>Created investor entity</returns>
        Task<Investor> CreateAsync(NewInvestorRequest investor, int modifierId, CancellationToken cancellationToken = default);
        /// <summary>
        /// Find investor by tax number
        /// </summary>
        /// <param name="taxNumber">Tax number to find with</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Matching Investor entity or null if no match found</returns>
        Task<Investor?> FindByTaxNumberAsync(string taxNumber, CancellationToken cancellationToken = default);
        /// <summary>
        /// Find investor by id
        /// </summary>
        /// <param name="id">Id to search by</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Matching Investor entity or null if no match found</returns>
        Task<Investor?> FindByIdAsync(int id, CancellationToken cancellationToken = default);
        /// <summary>
        /// Update an existing investor entity
        /// </summary>
        /// <param name="user">Instance of <see cref="EditInvestorRequest"/></param>
        /// <param name="modifierId">Modifying user's id</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        /// <returns>Updated investor entity</returns>
        Task<Investor> UpdateAsync(EditInvestorRequest user, int modifierId, CancellationToken cancellationToken = default);
    }
}
