﻿
namespace TIQuest.Api.Interfaces.Managers
{
    /// <summary>
    /// Interface for Hash mechanism
    /// </summary>
    public interface IHasher
    {
        /// <summary>
        /// Creates a hash for the provided plain text
        /// </summary>
        /// <param name="plainText">Plain text input</param>
        /// <returns>Hashed string</returns>
        /// <exception cref="ArgumentException">Thrown if <paramref name="plainText"/> is null or empty</exception>"
        string CreateHash(string plainText);
        /// <summary>
        /// Compares the provided text with the provided hash to verify if same
        /// </summary>
        /// <param name="plainText">Plain text input</param>
        /// <param name="hashToCompare">Hast to compare with</param>
        /// <returns>Value indicating success.</returns>
        /// <exception cref="ArgumentException">Thrown if either of the parameters are null or empty</exception>"
        bool CompareHash(string plainText, string hashToCompare);
    }
}
