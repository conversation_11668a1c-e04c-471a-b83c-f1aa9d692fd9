﻿using TIQuest.Api.Poco;
using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Interfaces.DataProviders
{
    public interface ITransactionDataProvider
    {
        Task<IEnumerable<Transaction>> GetTransactionsAsync(int userId, DateTime startDate, DateTime endDate, bool includeVoid, string? excludeTransactionCodes, CancellationToken cancellationToken = default);

        Task<IEnumerable<TransactionRecordWithBalance>> GetTransactionWithBalancesAsync(int userId, DateTime startDate, DateTime endDate, bool includeVoid, CancellationToken cancellationToken = default);
    }
}
