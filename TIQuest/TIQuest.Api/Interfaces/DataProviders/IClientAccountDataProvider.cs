﻿using TIQuest.Api.Entities;
using TIQuest.Api.Poco.Reports;
using TIQuest.Api.Reports.Filters;

namespace TIQuest.Api.Interfaces.DataProviders
{
    public interface IClientAccountDataProvider
    {
        Task<CompanyReport> GetCompanyReportAsync(CancellationToken cancellationToken = default);
        Task<ICollection<InvestmentAccount>> GetApplicableInvestmentAccountsAsync(ClientAccountFilter filter, CancellationToken cancellationToken = default);
        Task<ICollection<ClientAccountReport>> GetClientsAccountReportAsync(ClientAccountFilter filter, CancellationToken cancellationToken = default);
        Task<IEnumerable<Transaction>> GetTransactionsAsync(IEnumerable<int> accounts, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    }
}
