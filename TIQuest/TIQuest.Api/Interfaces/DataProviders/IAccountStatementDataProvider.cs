﻿using TIQuest.Api.Poco;
using TIQuest.Api.Poco.Reports;
using TIQuest.Api.Reports.Filters;

namespace TIQuest.Api.Interfaces.DataProviders
{
    public interface IAccountStatementDataProvider
    {
        Task<CompanyReport> GetCompanyReportAsync(CancellationToken cancellationToken = default);

        Task<IEnumerable<TransactionStatementRecord>> GetTransactionStatementsAsync(StatementFilter filter, CancellationToken cancellationToken = default);
    }
}
