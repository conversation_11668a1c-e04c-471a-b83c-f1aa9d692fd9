﻿using GrapeCity.ActiveReports;

namespace TIQuest.Api.Interfaces.Reports
{
    public interface IReportBuilder
    {
        /// <summary>
        /// Build report based on provided filter
        /// </summary>
        /// <typeparam name="TFilter">Filter of type <typeparamref name="TFilter"/></typeparam>
        /// <param name="filter">Instance of type <typeparamref name="TFilter"/></param>
        /// <param name="cancellationToken">Cancellation Token</param>
        /// <returns>Built Section report</returns>
        Task<SectionReport> BuildAsync<TFilter>(TFilter filter, CancellationToken cancellationToken = default);
        /// <summary>
        /// Preload Data for the reports
        /// </summary>
        /// <typeparam name="TFilter">Filter of type <typeparamref name="TFilter"/></typeparam>
        /// <param name="filter">Instance of type <typeparamref name="TFilter"/></param>
        /// <param name="cancellationToken">Cancellation Token</param>
        Task PreloadDataAsync<TFilter>(TFilter filter, CancellationToken cancellationToken = default);
    }
}
