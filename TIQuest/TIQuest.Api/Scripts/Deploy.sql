﻿IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
Go

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231109130440_Initial')
BEGIN
    CREATE TABLE [UserRole] (
        [Id] int NOT NULL IDENTITY,
        [Description] varchar(80) NOT NULL,
        CONSTRAINT [PK_UserRole] PRIMARY KEY ([Id])
    );
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231109130440_Initial')
BEGIN
    CREATE TABLE [User] (
        [Id] int NOT NULL IDENTITY,
        [UserRole] int NOT NULL,
        [FirstName] nvarchar(80) NOT NULL,
        [LastName] nvarchar(80) NOT NULL,
        [Email] nvarchar(200) NOT NULL,
        [PasswordHash] nvarchar(64) NULL,
        [OfficePhone] varchar(30) NULL,
        [HomePhone] varchar(30) NULL,
        [Mobile] varchar(30) NULL,
        [IsActive] bit NOT NULL,
        [LastModifiedBy] int NOT NULL,
        [LastModifiedOn] datetime2 NOT NULL,
        [CreatedBy] int NOT NULL,
        [CreatedOn] datetime2 NOT NULL,
        CONSTRAINT [PK_User] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_User_UserRole_UserRole] FOREIGN KEY ([UserRole]) REFERENCES [UserRole] ([Id]),
        CONSTRAINT [FK_User_User_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [User] ([Id]),
        CONSTRAINT [FK_User_User_LastModifiedBy] FOREIGN KEY ([LastModifiedBy]) REFERENCES [User] ([Id])
    );
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231109130440_Initial')
BEGIN
    CREATE INDEX [IX_User_CreatedBy] ON [User] ([CreatedBy]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231109130440_Initial')
BEGIN
    CREATE INDEX [IX_User_LastModifiedBy] ON [User] ([LastModifiedBy]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231109130440_Initial')
BEGIN
    CREATE INDEX [IX_User_UserRole] ON [User] ([UserRole]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231109130440_Initial')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20231109130440_Initial', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231120123948_Create_Investor')
BEGIN
    CREATE TABLE [Investor] (
        [Id] int NOT NULL IDENTITY,
        [FirstName] nvarchar(80) NOT NULL,
        [LastName] nvarchar(80) NOT NULL,
        [CompanyName] nvarchar(200) NULL,
        [TaxNumber] nvarchar(30) NOT NULL,
        [IsActive] bit NOT NULL,
        [LastModifiedBy] int NOT NULL,
        [LastModifiedOn] datetime2 NOT NULL,
        [CreatedBy] int NOT NULL,
        [CreatedOn] datetime2 NOT NULL,
        CONSTRAINT [PK_Investor] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_Investor_User_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [User] ([Id]),
        CONSTRAINT [FK_Investor_User_LastModifiedBy] FOREIGN KEY ([LastModifiedBy]) REFERENCES [User] ([Id])
    );
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231120123948_Create_Investor')
BEGIN
    CREATE INDEX [IX_Investor_CreatedBy] ON [Investor] ([CreatedBy]);
END;


IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231120123948_Create_Investor')
BEGIN
    CREATE INDEX [IX_Investor_LastModifiedBy] ON [Investor] ([LastModifiedBy]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231120123948_Create_Investor')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20231120123948_Create_Investor', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231122105035_Create_Investment_Account')
BEGIN
    CREATE TABLE [InvestmentAccount] (
        [Id] int NOT NULL IDENTITY,
        [Investor] int NOT NULL,
        [Name] nvarchar(100) NOT NULL,
        [TaxNumber] nvarchar(30) NOT NULL,
        [AccountNumber] nvarchar(20) NOT NULL,
        [Rate] decimal(7,5) NOT NULL,
        [StartDate] datetime2 NOT NULL,
        [EndDate] datetime2 NULL,
        [Balance] decimal(15,5) NOT NULL,
        [LastModifiedBy] int NOT NULL,
        [LastModifiedOn] datetime2 NOT NULL,
        [CreatedBy] int NOT NULL,
        [CreatedOn] datetime2 NOT NULL,
        CONSTRAINT [PK_InvestmentAccount] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_InvestmentAccount_Investor_Investor] FOREIGN KEY ([Investor]) REFERENCES [Investor] ([Id]),
        CONSTRAINT [FK_InvestmentAccount_User_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [User] ([Id]),
        CONSTRAINT [FK_InvestmentAccount_User_LastModifiedBy] FOREIGN KEY ([LastModifiedBy]) REFERENCES [User] ([Id])
    );
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231122105035_Create_Investment_Account')
BEGIN
    CREATE INDEX [IX_InvestmentAccount_CreatedBy] ON [InvestmentAccount] ([CreatedBy]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231122105035_Create_Investment_Account')
BEGIN
    CREATE INDEX [IX_InvestmentAccount_Investor] ON [InvestmentAccount] ([Investor]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231122105035_Create_Investment_Account')
BEGIN
    CREATE INDEX [IX_InvestmentAccount_LastModifiedBy] ON [InvestmentAccount] ([LastModifiedBy]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231122105035_Create_Investment_Account')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20231122105035_Create_Investment_Account', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231124105121_Create_User')
BEGIN
    CREATE TABLE [UserAccountPermission] (
        [User] int NOT NULL,
        [Account] int NOT NULL,
        CONSTRAINT [FK_UserAccountPermission_InvestmentAccount_Account] FOREIGN KEY ([Account]) REFERENCES [InvestmentAccount] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_UserAccountPermission_User_User] FOREIGN KEY ([User]) REFERENCES [User] ([Id]) ON DELETE CASCADE
    );
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231124105121_Create_User')
BEGIN
    CREATE INDEX [IX_UserAccountPermission_Account] ON [UserAccountPermission] ([Account]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231124105121_Create_User')
BEGIN
    CREATE INDEX [IX_UserAccountPermission_User] ON [UserAccountPermission] ([User]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231124105121_Create_User')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20231124105121_Create_User', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129093008_Transaction')
BEGIN
    CREATE PARTITION FUNCTION YearPartitionFunction (datetime2) 
    AS RANGE RIGHT FOR VALUES ('********', '********', '********', '********', '********', '********', '********', '********', '********', '********', '********',
    '********', '********', '********', '********', '********', '********', '********', '********', '********', '********', '********', '********', '********', '********', '********',
    '********', '********', '********', '********', '********', '********', '********', '********', '********', '********', '********', '********', '********', '20390101', '20400101',
    '20410101', '20420101', '********', '********', '********', '********', '********', '********', '********', '********');
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129093008_Transaction')
BEGIN
    CREATE PARTITION SCHEME YearPartitionScheme AS PARTITION YearPartitionFunction ALL TO ([Primary]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129093008_Transaction')
BEGIN
    CREATE TABLE [Transaction] (
        [Id] int NOT NULL IDENTITY,
        [Account] int NOT NULL,
        [Date] datetime2 NOT NULL,
        [Amount] money NOT NULL,
        [Description] varchar(80) NULL,
        [Parent] int NULL,
        [WireNumber] varchar(50) NULL,
        [LastModifiedBy] int NOT NULL,
        [LastModifiedOn] datetime2 NOT NULL,
        [CreatedBy] int NOT NULL,
        [CreatedOn] datetime2 NOT NULL,
        CONSTRAINT [FK_Transaction_InvestmentAccount_Account] FOREIGN KEY ([Account]) REFERENCES [InvestmentAccount] ([Id]),
        CONSTRAINT [FK_Transaction_User_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [User] ([Id]),
        CONSTRAINT [FK_Transaction_User_LastModifiedBy] FOREIGN KEY ([LastModifiedBy]) REFERENCES [User] ([Id])
    )ON YearPartitionScheme([Date]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129093008_Transaction')
BEGIN
    ALTER TABLE [dbo].[Transaction] ADD CONSTRAINT [PK_Transaction] PRIMARY KEY NONCLUSTERED ( [Id] ASC ) ON [PRIMARY]
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129093008_Transaction')
BEGIN
    ALTER TABLE [dbo].[Transaction] ADD CONSTRAINT [FK_Transaction_Transaction_Parent] FOREIGN KEY ([Parent]) REFERENCES [Transaction] ([Id]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129093008_Transaction')
BEGIN
    CREATE CLUSTERED INDEX [CIX_Transaction_Date] ON [Transaction] ([Date]) ON YearPartitionScheme([Date]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129093008_Transaction')
BEGIN
    CREATE INDEX [IX_Transaction_Account] ON [Transaction] ([Account]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129093008_Transaction')
BEGIN
    CREATE INDEX [IX_Transaction_CreatedBy] ON [Transaction] ([CreatedBy]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129093008_Transaction')
BEGIN
    CREATE INDEX [IX_Transaction_LastModifiedBy] ON [Transaction] ([LastModifiedBy]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129093008_Transaction')
BEGIN
    CREATE INDEX [IX_Transaction_Parent] ON [Transaction] ([Parent]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129093008_Transaction')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20231129093008_Transaction', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129094050_TransactionCodes')
BEGIN
    ALTER TABLE [Transaction] ADD [Code] int NOT NULL DEFAULT 0;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129094050_TransactionCodes')
BEGIN
    ALTER TABLE [Transaction] ADD [Status] int NOT NULL DEFAULT 0;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129094050_TransactionCodes')
BEGIN
    CREATE TABLE [TransactionCode] (
        [Id] int NOT NULL IDENTITY,
        [Code] varchar(6) NOT NULL,
        [Description] varchar(50) NOT NULL,
        [IsActive] bit NOT NULL,
        [IsDebit] bit NOT NULL,
        [IsTaxable] bit NOT NULL,
        [IsSystem] bit NOT NULL,
        CONSTRAINT [PK_TransactionCode] PRIMARY KEY ([Id])
    );
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129094050_TransactionCodes')
BEGIN
    CREATE TABLE [TransactionStatus] (
        [Id] int NOT NULL IDENTITY,
        [Description] varchar(50) NOT NULL,
        CONSTRAINT [PK_TransactionStatus] PRIMARY KEY ([Id])
    );
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129094050_TransactionCodes')
BEGIN
    CREATE INDEX [IX_Transaction_Code] ON [Transaction] ([Code]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129094050_TransactionCodes')
BEGIN
    CREATE INDEX [IX_Transaction_Status] ON [Transaction] ([Status]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129094050_TransactionCodes')
BEGIN
    ALTER TABLE [Transaction] ADD CONSTRAINT [FK_Transaction_TransactionCode_Code] FOREIGN KEY ([Code]) REFERENCES [TransactionCode] ([Id]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129094050_TransactionCodes')
BEGIN
    ALTER TABLE [Transaction] ADD CONSTRAINT [FK_Transaction_TransactionStatus_Status] FOREIGN KEY ([Status]) REFERENCES [TransactionStatus] ([Id]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231129094050_TransactionCodes')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20231129094050_TransactionCodes', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231201053212_MonthEndProcess')
BEGIN
    ALTER TABLE [UserAccountPermission] DROP CONSTRAINT [FK_UserAccountPermission_InvestmentAccount_Account];
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231201053212_MonthEndProcess')
BEGIN
    ALTER TABLE [UserAccountPermission] DROP CONSTRAINT [FK_UserAccountPermission_User_User];
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231201053212_MonthEndProcess')
BEGIN
    CREATE TABLE [MonthEndProcess] (
        [Id] int NOT NULL IDENTITY,
        [Year] int NOT NULL,
        [Month] int NOT NULL,
        [Description] nvarchar(max) NOT NULL,
        [InvestmentRate] decimal(7,5) NOT NULL,
        [LoanRate] decimal(7,5) NOT NULL,
        [ManagementRate] decimal(7,5) NOT NULL,
        [ManagementFee] money NOT NULL,
        CONSTRAINT [PK_MonthEndProcess] PRIMARY KEY ([Id])
    );
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231201053212_MonthEndProcess')
BEGIN
    ALTER TABLE [UserAccountPermission] ADD CONSTRAINT [FK_UserAccountPermission_InvestmentAccount_Account] FOREIGN KEY ([Account]) REFERENCES [InvestmentAccount] ([Id]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231201053212_MonthEndProcess')
BEGIN
    ALTER TABLE [UserAccountPermission] ADD CONSTRAINT [FK_UserAccountPermission_User_User] FOREIGN KEY ([User]) REFERENCES [User] ([Id]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231201053212_MonthEndProcess')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20231201053212_MonthEndProcess', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231204093811_AchRegister')
BEGIN
    CREATE TABLE [AchRegister] (
        [Id] int NOT NULL IDENTITY,
        [Transaction] int NOT NULL,
        [Date] datetime2 NOT NULL,
        [Description] varchar(200) NULL,
        [IsPrinted] bit NOT NULL,
        CONSTRAINT [PK_AchRegister] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_AchRegister_Transaction_Transaction] FOREIGN KEY ([Transaction]) REFERENCES [Transaction] ([Id])
    );
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231204093811_AchRegister')
BEGIN
    CREATE INDEX [IX_AchRegister_Transaction] ON [AchRegister] ([Transaction]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231204093811_AchRegister')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20231204093811_AchRegister', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231204111518_CheckRegister')
BEGIN
    CREATE TABLE [CheckRegister] (
        [Id] int NOT NULL IDENTITY,
        [Transaction] int NOT NULL,
        [Number] int NOT NULL,
        [Date] datetime2 NOT NULL,
        [Payee] varchar(100) NOT NULL,
        [Memo] varchar(256) NULL,
        [IsPrinted] bit NOT NULL,
        [Apartment] nvarchar(100) NULL,
        [Address] nvarchar(100) NOT NULL,
        [City] nvarchar(100) NULL,
        [State] nvarchar(100) NULL,
        [Zip] nvarchar(30) NULL,
        [Country] nvarchar(100) NOT NULL,
        CONSTRAINT [PK_CheckRegister] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_CheckRegister_Transaction_Transaction] FOREIGN KEY ([Transaction]) REFERENCES [Transaction] ([Id])
    );
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231204111518_CheckRegister')
BEGIN
    CREATE TABLE [Setting] (
        [Id] int NOT NULL IDENTITY,
        [Key] varchar(100) NOT NULL,
        [Value] varchar(256) NOT NULL,
        CONSTRAINT [PK_Setting] PRIMARY KEY ([Id])
    );
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231204111518_CheckRegister')
BEGIN
    CREATE INDEX [IX_CheckRegister_Transaction] ON [CheckRegister] ([Transaction]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231204111518_CheckRegister')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20231204111518_CheckRegister', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231207083937_InvestorDetails')
BEGIN
    ALTER TABLE [Investor] ADD [Address] nvarchar(100) NOT NULL DEFAULT N'';
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231207083937_InvestorDetails')
BEGIN
    ALTER TABLE [Investor] ADD [Apartment] nvarchar(100) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231207083937_InvestorDetails')
BEGIN
    ALTER TABLE [Investor] ADD [City] nvarchar(100) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231207083937_InvestorDetails')
BEGIN
    ALTER TABLE [Investor] ADD [Country] nvarchar(100) NOT NULL DEFAULT N'';
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231207083937_InvestorDetails')
BEGIN
    ALTER TABLE [Investor] ADD [Email] nvarchar(200) NOT NULL DEFAULT N'';
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231207083937_InvestorDetails')
BEGIN
    ALTER TABLE [Investor] ADD [HomePhone] varchar(30) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231207083937_InvestorDetails')
BEGIN
    ALTER TABLE [Investor] ADD [Mobile] varchar(30) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231207083937_InvestorDetails')
BEGIN
    ALTER TABLE [Investor] ADD [Note] varchar(400) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231207083937_InvestorDetails')
BEGIN
    ALTER TABLE [Investor] ADD [OfficePhone] varchar(30) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231207083937_InvestorDetails')
BEGIN
    ALTER TABLE [Investor] ADD [State] nvarchar(100) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231207083937_InvestorDetails')
BEGIN
    ALTER TABLE [Investor] ADD [Type] varchar(30) NOT NULL DEFAULT '';
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231207083937_InvestorDetails')
BEGIN
    ALTER TABLE [Investor] ADD [Zip] nvarchar(30) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231207083937_InvestorDetails')
BEGIN
    CREATE UNIQUE INDEX [IX_Investor_TaxNumber] ON [Investor] ([TaxNumber]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231207083937_InvestorDetails')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20231207083937_InvestorDetails', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231211112610_InvestmentAccountDetails')
BEGIN
    DECLARE @var0 sysname;
    SELECT @var0 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[InvestmentAccount]') AND [c].[name] = N'TaxNumber');
    IF @var0 IS NOT NULL EXEC(N'ALTER TABLE [InvestmentAccount] DROP CONSTRAINT [' + @var0 + '];');
    ALTER TABLE [InvestmentAccount] DROP COLUMN [TaxNumber];
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231211112610_InvestmentAccountDetails')
BEGIN
    ALTER TABLE [Investor] ADD [Fax] varchar(30) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231211112610_InvestmentAccountDetails')
BEGIN
    DECLARE @var1 sysname;
    SELECT @var1 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[InvestmentAccount]') AND [c].[name] = N'Rate');
    IF @var1 IS NOT NULL EXEC(N'ALTER TABLE [InvestmentAccount] DROP CONSTRAINT [' + @var1 + '];');
    ALTER TABLE [InvestmentAccount] ALTER COLUMN [Rate] decimal(7,5) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231211112610_InvestmentAccountDetails')
BEGIN
    ALTER TABLE [InvestmentAccount] ADD [AccountType] varchar(30) NOT NULL DEFAULT '';
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231211112610_InvestmentAccountDetails')
BEGIN
    ALTER TABLE [InvestmentAccount] ADD [InterestType] varchar(20) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231211112610_InvestmentAccountDetails')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20231211112610_InvestmentAccountDetails', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231212115433_UserStatements')
BEGIN
    CREATE TABLE [UserAccountStatement] (
        [User] int NOT NULL,
        [Account] int NOT NULL,
        [Email] bit NOT NULL,
        [Mail] bit NOT NULL,
        CONSTRAINT [FK_UserAccountStatement_InvestmentAccount_Account] FOREIGN KEY ([Account]) REFERENCES [InvestmentAccount] ([Id]),
        CONSTRAINT [FK_UserAccountStatement_User_User] FOREIGN KEY ([User]) REFERENCES [User] ([Id])
    );
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231212115433_UserStatements')
BEGIN
    CREATE INDEX [IX_UserAccountStatement_Account] ON [UserAccountStatement] ([Account]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231212115433_UserStatements')
BEGIN
    CREATE INDEX [IX_UserAccountStatement_User] ON [UserAccountStatement] ([User]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231212115433_UserStatements')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20231212115433_UserStatements', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231213115610_EditAccount')
BEGIN
    ALTER TABLE [InvestmentAccount] ADD [Report1099Name] varchar(250) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231213115610_EditAccount')
BEGIN
    CREATE UNIQUE INDEX [IX_InvestmentAccount_AccountNumber] ON [InvestmentAccount] ([AccountNumber]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20231213115610_EditAccount')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20231213115610_EditAccount', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240105101553_RemoveUserContantFields')
BEGIN
    DECLARE @var0 sysname;
    SELECT @var0 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[User]') AND [c].[name] = N'HomePhone');
    IF @var0 IS NOT NULL EXEC(N'ALTER TABLE [User] DROP CONSTRAINT [' + @var0 + '];');
    ALTER TABLE [User] DROP COLUMN [HomePhone];
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240105101553_RemoveUserContantFields')
BEGIN
    DECLARE @var1 sysname;
    SELECT @var1 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[User]') AND [c].[name] = N'Mobile');
    IF @var1 IS NOT NULL EXEC(N'ALTER TABLE [User] DROP CONSTRAINT [' + @var1 + '];');
    ALTER TABLE [User] DROP COLUMN [Mobile];
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240105101553_RemoveUserContantFields')
BEGIN
    DECLARE @var2 sysname;
    SELECT @var2 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[User]') AND [c].[name] = N'OfficePhone');
    IF @var2 IS NOT NULL EXEC(N'ALTER TABLE [User] DROP CONSTRAINT [' + @var2 + '];');
    ALTER TABLE [User] DROP COLUMN [OfficePhone];
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240105101553_RemoveUserContantFields')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240105101553_RemoveUserContantFields', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240110130746_UpdateInvestorEntity')
BEGIN
    DECLARE @var0 sysname;
    SELECT @var0 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[Investor]') AND [c].[name] = N'Email');
    IF @var0 IS NOT NULL EXEC(N'ALTER TABLE [Investor] DROP CONSTRAINT [' + @var0 + '];');
    ALTER TABLE [Investor] DROP COLUMN [Email];
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240110130746_UpdateInvestorEntity')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240110130746_UpdateInvestorEntity', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240112095421_SettingUpgrade')
BEGIN
    DECLARE @var0 sysname;
    SELECT @var0 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[Setting]') AND [c].[name] = N'Value');
    IF @var0 IS NOT NULL EXEC(N'ALTER TABLE [Setting] DROP CONSTRAINT [' + @var0 + '];');
    ALTER TABLE [Setting] ALTER COLUMN [Value] varchar(256) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240112095421_SettingUpgrade')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240112095421_SettingUpgrade', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240116091912_Partnership')
BEGIN
    CREATE TABLE [Partnership] (
        [Id] int NOT NULL IDENTITY,
        [Name] varchar(200) NOT NULL,
        [Description] varchar(2000) NULL,
        [Apartment] nvarchar(100) NULL,
        [Address] nvarchar(100) NOT NULL,
        [City] nvarchar(100) NULL,
        [State] nvarchar(100) NULL,
        [Zip] nvarchar(30) NULL,
        [Country] nvarchar(100) NOT NULL,
        [IsActive] bit NOT NULL,
        [LastModifiedBy] int NOT NULL,
        [LastModifiedOn] datetime2 NOT NULL,
        [CreatedBy] int NOT NULL,
        [CreatedOn] datetime2 NOT NULL,
        CONSTRAINT [PK_Partnership] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_Partnership_User_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [User] ([Id]),
        CONSTRAINT [FK_Partnership_User_LastModifiedBy] FOREIGN KEY ([LastModifiedBy]) REFERENCES [User] ([Id])
    );
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240116091912_Partnership')
BEGIN
    CREATE INDEX [IX_Partnership_CreatedBy] ON [Partnership] ([CreatedBy]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240116091912_Partnership')
BEGIN
    CREATE INDEX [IX_Partnership_LastModifiedBy] ON [Partnership] ([LastModifiedBy]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240116091912_Partnership')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116091912_Partnership', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240117082634_PartnershipOwner')
BEGIN
    CREATE TABLE [PartnershipOwner] (
        [Id] int NOT NULL IDENTITY,
        [Account] int NOT NULL,
        [Partnership] int NOT NULL,
        [Percentage] decimal(7,5) NOT NULL,
        [LastModifiedBy] int NOT NULL,
        [LastModifiedOn] datetime2 NOT NULL,
        [CreatedBy] int NOT NULL,
        [CreatedOn] datetime2 NOT NULL,
        CONSTRAINT [PK_PartnershipOwner] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_PartnershipOwner_InvestmentAccount_Account] FOREIGN KEY ([Account]) REFERENCES [InvestmentAccount] ([Id]),
        CONSTRAINT [FK_PartnershipOwner_Partnership_Partnership] FOREIGN KEY ([Partnership]) REFERENCES [Partnership] ([Id]),
        CONSTRAINT [FK_PartnershipOwner_User_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [User] ([Id]),
        CONSTRAINT [FK_PartnershipOwner_User_LastModifiedBy] FOREIGN KEY ([LastModifiedBy]) REFERENCES [User] ([Id])
    );
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240117082634_PartnershipOwner')
BEGIN
    CREATE UNIQUE INDEX [IX_PartnershipOwner_Account_Partnership] ON [PartnershipOwner] ([Account], [Partnership]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240117082634_PartnershipOwner')
BEGIN
    CREATE INDEX [IX_PartnershipOwner_CreatedBy] ON [PartnershipOwner] ([CreatedBy]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240117082634_PartnershipOwner')
BEGIN
    CREATE INDEX [IX_PartnershipOwner_LastModifiedBy] ON [PartnershipOwner] ([LastModifiedBy]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240117082634_PartnershipOwner')
BEGIN
    CREATE INDEX [IX_PartnershipOwner_Partnership] ON [PartnershipOwner] ([Partnership]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240117082634_PartnershipOwner')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240117082634_PartnershipOwner', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240117091953_UserUniqueEmail')
BEGIN
    CREATE UNIQUE INDEX [IX_User_Email] ON [User] ([Email]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240117091953_UserUniqueEmail')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240117091953_UserUniqueEmail', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240208121623_RemoveInvestorValidations')
BEGIN
    DECLARE @var0 sysname;
    SELECT @var0 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[Partnership]') AND [c].[name] = N'Address');
    IF @var0 IS NOT NULL EXEC(N'ALTER TABLE [Partnership] DROP CONSTRAINT [' + @var0 + '];');
    ALTER TABLE [Partnership] ALTER COLUMN [Address] nvarchar(100) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240208121623_RemoveInvestorValidations')
BEGIN
    DECLARE @var1 sysname;
    SELECT @var1 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[Investor]') AND [c].[name] = N'LastName');
    IF @var1 IS NOT NULL EXEC(N'ALTER TABLE [Investor] DROP CONSTRAINT [' + @var1 + '];');
    ALTER TABLE [Investor] ALTER COLUMN [LastName] nvarchar(80) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240208121623_RemoveInvestorValidations')
BEGIN
    DECLARE @var2 sysname;
    SELECT @var2 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[Investor]') AND [c].[name] = N'FirstName');
    IF @var2 IS NOT NULL EXEC(N'ALTER TABLE [Investor] DROP CONSTRAINT [' + @var2 + '];');
    ALTER TABLE [Investor] ALTER COLUMN [FirstName] nvarchar(80) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240208121623_RemoveInvestorValidations')
BEGIN
    DECLARE @var3 sysname;
    SELECT @var3 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[Investor]') AND [c].[name] = N'Address');
    IF @var3 IS NOT NULL EXEC(N'ALTER TABLE [Investor] DROP CONSTRAINT [' + @var3 + '];');
    ALTER TABLE [Investor] ALTER COLUMN [Address] nvarchar(100) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240208121623_RemoveInvestorValidations')
BEGIN
    DECLARE @var4 sysname;
    SELECT @var4 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[CheckRegister]') AND [c].[name] = N'Address');
    IF @var4 IS NOT NULL EXEC(N'ALTER TABLE [CheckRegister] DROP CONSTRAINT [' + @var4 + '];');
    ALTER TABLE [CheckRegister] ALTER COLUMN [Address] nvarchar(100) NULL;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240208121623_RemoveInvestorValidations')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240208121623_RemoveInvestorValidations', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240212101636_Distributions')
BEGIN
    CREATE TABLE [Distribution] (
        [Id] int NOT NULL IDENTITY,
        [Partnership] int NOT NULL,
        [Date] datetime2 NOT NULL,
        [Amount] money NOT NULL,
        CONSTRAINT [PK_Distribution] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_Distribution_Partnership_Partnership] FOREIGN KEY ([Partnership]) REFERENCES [Partnership] ([Id])
    );
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240212101636_Distributions')
BEGIN
    CREATE TABLE [DistributionTransaction] (
        [Distribution] int NOT NULL,
        [Transaction] int NOT NULL,
        CONSTRAINT [FK_DistributionTransaction_Distribution_Distribution] FOREIGN KEY ([Distribution]) REFERENCES [Distribution] ([Id]),
        CONSTRAINT [FK_DistributionTransaction_Transaction_Transaction] FOREIGN KEY ([Transaction]) REFERENCES [Transaction] ([Id])
    );
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240212101636_Distributions')
BEGIN
    CREATE INDEX [IX_Distribution_Partnership] ON [Distribution] ([Partnership]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240212101636_Distributions')
BEGIN
    CREATE INDEX [IX_DistributionTransaction_Distribution] ON [DistributionTransaction] ([Distribution]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240212101636_Distributions')
BEGIN
    CREATE INDEX [IX_DistributionTransaction_Transaction] ON [DistributionTransaction] ([Transaction]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240212101636_Distributions')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240212101636_Distributions', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240222123540_MonthEndTransaction')
BEGIN
    ALTER TABLE [InvestmentAccount] ADD [ClosureBalance] money NOT NULL DEFAULT 0.0;
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240222123540_MonthEndTransaction')
BEGIN
    CREATE TABLE [MonthEndProcessTransaction] (
        [MonthEndProcess] int NOT NULL,
        [Transaction] int NOT NULL,
        CONSTRAINT [FK_MonthEndProcessTransaction_MonthEndProcess_MonthEndProcess] FOREIGN KEY ([MonthEndProcess]) REFERENCES [MonthEndProcess] ([Id]),
        CONSTRAINT [FK_MonthEndProcessTransaction_Transaction_Transaction] FOREIGN KEY ([Transaction]) REFERENCES [Transaction] ([Id])
    );
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240222123540_MonthEndTransaction')
BEGIN
    CREATE INDEX [IX_MonthEndProcessTransaction_MonthEndProcess] ON [MonthEndProcessTransaction] ([MonthEndProcess]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240222123540_MonthEndTransaction')
BEGIN
    CREATE INDEX [IX_MonthEndProcessTransaction_Transaction] ON [MonthEndProcessTransaction] ([Transaction]);
END;

IF NOT EXISTS(SELECT * FROM [__EFMigrationsHistory] WHERE [MigrationId] = N'20240222123540_MonthEndTransaction')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240222123540_MonthEndTransaction', N'7.0.9');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240304080506_DeleteMonthEnd'
)
BEGIN
    DECLARE @var0 sysname;
    SELECT @var0 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[Transaction]') AND [c].[name] = N'Amount');
    IF @var0 IS NOT NULL EXEC(N'ALTER TABLE [Transaction] DROP CONSTRAINT [' + @var0 + '];');
    ALTER TABLE [Transaction] ALTER COLUMN [Amount] decimal(15,5) NOT NULL;
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240304080506_DeleteMonthEnd'
)
BEGIN
    DECLARE @var1 sysname;
    SELECT @var1 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[MonthEndProcess]') AND [c].[name] = N'ManagementFee');
    IF @var1 IS NOT NULL EXEC(N'ALTER TABLE [MonthEndProcess] DROP CONSTRAINT [' + @var1 + '];');
    ALTER TABLE [MonthEndProcess] ALTER COLUMN [ManagementFee] decimal(15,5) NOT NULL;
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240304080506_DeleteMonthEnd'
)
BEGIN
    DECLARE @var2 sysname;
    SELECT @var2 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[InvestmentAccount]') AND [c].[name] = N'ClosureBalance');
    IF @var2 IS NOT NULL EXEC(N'ALTER TABLE [InvestmentAccount] DROP CONSTRAINT [' + @var2 + '];');
    ALTER TABLE [InvestmentAccount] ALTER COLUMN [ClosureBalance] decimal(15,5) NOT NULL;
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240304080506_DeleteMonthEnd'
)
BEGIN
    DECLARE @var3 sysname;
    SELECT @var3 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[Distribution]') AND [c].[name] = N'Amount');
    IF @var3 IS NOT NULL EXEC(N'ALTER TABLE [Distribution] DROP CONSTRAINT [' + @var3 + '];');
    ALTER TABLE [Distribution] ALTER COLUMN [Amount] decimal(15,5) NOT NULL;
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240304080506_DeleteMonthEnd'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240304080506_DeleteMonthEnd', N'8.0.2');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240314123913_CheckNumberUpdate'
)
BEGIN
    DECLARE @var0 sysname;
    SELECT @var0 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[CheckRegister]') AND [c].[name] = N'Number');
    IF @var0 IS NOT NULL EXEC(N'ALTER TABLE [CheckRegister] DROP CONSTRAINT [' + @var0 + '];');
    ALTER TABLE [CheckRegister] ALTER COLUMN [Number] int NULL;
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240314123913_CheckNumberUpdate'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240314123913_CheckNumberUpdate', N'8.0.2');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240328103847_MonthEndBalances'
)
BEGIN
    CREATE TABLE [MonthEndBalance] (
        [MonthEndProcess] int NOT NULL,
        [Account] int NOT NULL,
        [StartingBalance] decimal(15,5) NOT NULL,
        [EndingBalance] decimal(15,5) NOT NULL,
        CONSTRAINT [FK_MonthEndBalance_InvestmentAccount_Account] FOREIGN KEY ([Account]) REFERENCES [InvestmentAccount] ([Id]),
        CONSTRAINT [FK_MonthEndBalance_MonthEndProcess_MonthEndProcess] FOREIGN KEY ([MonthEndProcess]) REFERENCES [MonthEndProcess] ([Id])
    );
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240328103847_MonthEndBalances'
)
BEGIN
    CREATE INDEX [IX_MonthEndBalance_Account] ON [MonthEndBalance] ([Account]);
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240328103847_MonthEndBalances'
)
BEGIN
    CREATE INDEX [IX_MonthEndBalance_MonthEndProcess] ON [MonthEndBalance] ([MonthEndProcess]);
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240328103847_MonthEndBalances'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240328103847_MonthEndBalances', N'8.0.2');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240409102221_RemoveClosureBalance'
)
BEGIN
    DECLARE @var0 sysname;
    SELECT @var0 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[InvestmentAccount]') AND [c].[name] = N'ClosureBalance');
    IF @var0 IS NOT NULL EXEC(N'ALTER TABLE [InvestmentAccount] DROP CONSTRAINT [' + @var0 + '];');
    ALTER TABLE [InvestmentAccount] DROP COLUMN [ClosureBalance];
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240409102221_RemoveClosureBalance'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240409102221_RemoveClosureBalance', N'8.0.2');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240422092010_OptionalTaxNumber'
)
BEGIN
    DROP INDEX [IX_Investor_TaxNumber] ON [Investor];
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240422092010_OptionalTaxNumber'
)
BEGIN
    DECLARE @var0 sysname;
    SELECT @var0 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[Investor]') AND [c].[name] = N'TaxNumber');
    IF @var0 IS NOT NULL EXEC(N'ALTER TABLE [Investor] DROP CONSTRAINT [' + @var0 + '];');
    ALTER TABLE [Investor] ALTER COLUMN [TaxNumber] nvarchar(30) NULL;
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240422092010_OptionalTaxNumber'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240422092010_OptionalTaxNumber', N'8.0.2');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240424093304_UserAssociationDate'
)
BEGIN
    ALTER TABLE [UserAccountStatement] ADD [AssociationDate] datetime2 NOT NULL DEFAULT '0001-01-01T00:00:00.0000000';
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240424093304_UserAssociationDate'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240424093304_UserAssociationDate', N'8.0.2');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240430091016_EmailLog'
)
BEGIN
    CREATE TABLE [EmailLog] (
        [Id] int NOT NULL IDENTITY,
        [Date] datetime2 NOT NULL,
        [Email] nvarchar(max) NOT NULL,
        [Subject] nvarchar(max) NOT NULL,
        [Status] nvarchar(max) NOT NULL,
        [Location] nvarchar(max) NOT NULL,
        CONSTRAINT [PK_EmailLog] PRIMARY KEY ([Id])
    );
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240430091016_EmailLog'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240430091016_EmailLog', N'8.0.2');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240503092202_AuditLogs'
)
BEGIN
    CREATE TABLE [AuditLog] (
        [Date] datetime2 NOT NULL,
        [Operation] nvarchar(max) NOT NULL,
        [Description] nvarchar(max) NOT NULL,
        [ModifiedBy] int NOT NULL,
        CONSTRAINT [FK_AuditLog_User_ModifiedBy] FOREIGN KEY ([ModifiedBy]) REFERENCES [User] ([Id])
    );
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240503092202_AuditLogs'
)
BEGIN
    CREATE INDEX [IX_AuditLog_ModifiedBy] ON [AuditLog] ([ModifiedBy]);
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240503092202_AuditLogs'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240503092202_AuditLogs', N'8.0.2');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240624140016_ClientStatementEmails'
)
BEGIN
    ALTER TABLE dbo.InvestmentAccount ADD Email nvarchar(320) NULL, SendEmail bit NOT NULL DEFAULT 0, SendMail bit NOT NULL DEFAULT 0;
    ALTER TABLE dbo.Investor ADD Email nvarchar(320) NULL;
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240624140016_ClientStatementEmails'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240624140016_ClientStatementEmails', N'8.0.2');
END;

COMMIT;
GO

BEGIN TRANSACTION;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230624130016_AddTaxNumberColumnToInvestmentAccountTable'
)
BEGIN
    ALTER TABLE [dbo].[InvestmentAccount] ADD [TaxNumber] NVARCHAR(30) NULL;
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230624130016_AddTaxNumberColumnToInvestmentAccountTable'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20230624130016_AddTaxNumberColumnToInvestmentAccountTable', N'8.0.2');
END;

COMMIT;
GO


BEGIN TRANSACTION;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20241126124930_AddAchDetailsColumnToTransactionTable'
)
BEGIN
    ALTER TABLE [dbo].[Transaction] ADD [AchDetails] NVARCHAR(200) NULL;
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20241126124930_AddAchDetailsColumnToTransactionTable'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20241126124930_AddAchDetailsColumnToTransactionTable', N'8.0.2');
END;

COMMIT;
GO


BEGIN TRANSACTION;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20241126189960_AddIsBankTransactionColumnToTransactionTable'
)
BEGIN
    ALTER TABLE [dbo].[Transaction] ADD [IsBankTransaction] BIT NULL DEFAULT 0;
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20241126189960_AddIsBankTransactionColumnToTransactionTable'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20241126189960_AddIsBankTransactionColumnToTransactionTable', N'8.0.2');
END;

COMMIT;
GO


BEGIN TRANSACTION;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20241126189560_DeleteIsBankTransactionColumnFromTransactionTable'
)
BEGIN
    ALTER TABLE [dbo].[Transaction] DROP COLUMN [IsBankTransaction];
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20241126189560_DeleteIsBankTransactionColumnFromTransactionTable'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20241126189560_DeleteIsBankTransactionColumnFromTransactionTable', N'8.0.2');
END;

COMMIT;
GO


BEGIN TRANSACTION;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20241126139960_AddBankTransactionTypeColumnToTransactionTable'
)
BEGIN
    ALTER TABLE [dbo].[Transaction] ADD [BankTransactionType] NVARCHAR(200) NULL;
END;

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20241126139960_AddBankTransactionTypeColumnToTransactionTable'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20241126139960_AddBankTransactionTypeColumnToTransactionTable', N'8.0.2');
END;

COMMIT;
GO