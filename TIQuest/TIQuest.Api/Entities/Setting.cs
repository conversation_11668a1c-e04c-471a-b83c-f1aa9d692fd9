﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(Setting))]
    public class Setting
    {
        public int Id { get; set; }
        [MaxLength(100)]
        [Column(TypeName = "varchar")]
        public string Key { get; set; }
        [MaxLength(256)]
        [Column(TypeName = "varchar")]
        public string? Value { get; set; }
    }
}
