﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(UserAccountStatement))]
    [Keyless]
    public class UserAccountStatement
    {
        [Column("User")]
        public int UserId { get; set; }
        [ForeignKey(nameof(UserId))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public User AssociatedUser { get; set; }
        [Column("Account")]
        public int AccountId { get; set; }
        [ForeignKey(nameof(AccountId))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public InvestmentAccount InvestmentAccount { get; set; }
        public bool Email { get; set; }
        public bool Mail { get; set; }
        public DateTime AssociationDate { get; set; }
    }
}
