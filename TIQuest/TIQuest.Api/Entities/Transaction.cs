﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
	[Table(nameof(Transaction))]
	public class Transaction : EntityModificationLog
	{
		[Key]
		public int Id { get; set; }
		[Column("Account")]
		public int AccountId { get; set; }
		[ForeignKey(nameof(AccountId))]
		[DeleteBehavior(DeleteBehavior.NoAction)]
		public InvestmentAccount InvestmentAccount { get; set; }
		public DateTime Date { get; set; }
		[Precision(15, 5)]
		public decimal Amount { get; set; }
		[MaxLength(80)]
		[Column(TypeName = "varchar")]
		public string? Description { get; set; }
		[Column("Status")]
		public int StatusId { get; set; }
		[ForeignKey(nameof(StatusId))]
		[DeleteBehavior(DeleteBehavior.NoAction)]
		public TransactionStatus TransactionStatus { get; set; }
		[Column("Code")]
		public int CodeId { get; set; }
		[ForeignKey(nameof(CodeId))]
		[DeleteBehavior(DeleteBehavior.NoAction)]
		public TransactionCode TransactionCode { get; set; }
		[Column("Parent")]
		public int? ParentId { get; set; }
		[ForeignKey(nameof(ParentId))]
		[DeleteBehavior(DeleteBehavior.NoAction)]
		public Transaction ParentTransaction { get; set; }
		[MaxLength(50)]
		[Column(TypeName = "varchar")]
		public string? WireNumber { get; set; }
		[MaxLength(200)]
		[Column(TypeName = "varchar")]
		public string? AchDetails { get; set; }
		public string? BankTransactionType { get; set; }
	}
}
