﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(AchRegister))]
    public class AchRegister
    {
        public int Id { get; set; }
        [Column("Transaction")]
        public int TransactionId { get; set; }
        [ForeignKey(nameof(TransactionId))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public Transaction Transaction { get; set; }
        public DateTime Date { get; set; }
        [MaxLength(200)]
        [Column(TypeName = "varchar")]
        public string? Description { get; set; }
        public bool IsPrinted { get; set; }
    }
}
