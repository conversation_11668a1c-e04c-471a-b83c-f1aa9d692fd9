﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(UserAccountPermission))]
    [Keyless]
    public class UserAccountPermission
    {
        [Column("User")]
        public int UserId { get; set; }
        [ForeignKey(nameof(UserId))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public User User { get; set; }
        [Column("Account")]
        public int InvestmentAccountId { get; set; }
        [ForeignKey(nameof(InvestmentAccountId))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public InvestmentAccount InvestmentAccount { get; set; }
    }
}
