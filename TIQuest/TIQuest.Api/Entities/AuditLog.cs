﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(AuditLog))]
    [Keyless]
    public class AuditLog
    {
        public DateTime Date { get; set; }

        public string Operation { get; set; }

        public string Description { get; set; }

        public int ModifiedBy { get; set; }
        [ForeignKey(nameof(ModifiedBy))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public User UserModifiedBy { get; set; }
    }
}
