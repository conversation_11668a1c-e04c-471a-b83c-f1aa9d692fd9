﻿using Microsoft.EntityFrameworkCore;

namespace TIQuest.Api.Entities
{
    public class DataContext : DbContext
    {
        public DataContext(DbContextOptions options) : base(options)
        {
        }

        public DbSet<UserRole> UserRoles { get; set; }

        public DbSet<User> Users { get; set; }

        public DbSet<Investor> Investors { get; set; }

        public virtual DbSet<InvestmentAccount> InvestmentAccounts { get; set; }

        public DbSet<UserAccountPermission> UserAccountPermissions { get; set; }

        public DbSet<UserRole> Roles { get; set; }

        public virtual DbSet<Transaction> Transactions { get; set; }

        public virtual DbSet<TransactionCode> TransactionCodes { get; set; }

        public DbSet<TransactionStatus> TransactionStatuses { get; set; }

        public DbSet<MonthEndProcess> MonthEndProcesses { get; set; }

        public virtual DbSet<AchRegister> AchRegisters { get; set; }

        public virtual DbSet<CheckRegister> CheckRegisters { get; set; }

        public virtual DbSet<Setting> Settings { get; set; }

        public DbSet<UserAccountStatement> UserAccountStatements { get; set; }

        public DbSet<Partnership> Partnerships { get; set; }

        public DbSet<PartnershipOwner> PartnershipOwners { get; set; }

        public DbSet<Distribution> Distributions { get; set; }

        public DbSet<DistributionTransaction> DistributionTransactions { get; set; }

        public DbSet<MonthEndProcessTransaction> MonthEndProcessTransactions { get; set; }

        public DbSet<MonthEndBalance> MonthEndBalances { get; set; }

        public DbSet<EmailLog> EmailLogs { get; set; }

        public DbSet<AuditLog> AuditLogs { get; set; }
    }
}
