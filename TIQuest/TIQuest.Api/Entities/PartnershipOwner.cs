﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(PartnershipOwner))]
    [Index(nameof(AccountId), nameof(PartnershipId), IsUnique = true)]
    public class PartnershipOwner : EntityModificationLog
    {
        [Key]
        public int Id { get; set; }
        [Column("Account")]
        public int AccountId { get; set; }
        [ForeignKey(nameof(AccountId))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public InvestmentAccount InvestmentAccount { get; set; }
        [Column("Partnership")]
        public int PartnershipId { get; set; }
        [ForeignKey(nameof(PartnershipId))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public Partnership Partnership { get; set; }
        [Precision(7, 5)]
        public decimal Percentage { get; set; }
    }
}
