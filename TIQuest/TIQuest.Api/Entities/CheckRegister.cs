﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(CheckRegister))]
    public class CheckRegister : EntityAddress
    {
        public int Id { get; set; }
        [Column("Transaction")]
        public int TransactionId { get; set; }
        [ForeignKey(nameof(TransactionId))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public Transaction Transaction { get; set; }
        public int? Number { get; set; }
        public DateTime Date { get; set; }
        [MaxLength(100)]
        [Column(TypeName = "varchar")]
        public string Payee { get; set; }
        [MaxLength(256)]
        [Column(TypeName = "varchar")]
        public string? Memo { get; set; }
        public bool IsPrinted { get; set; }
    }
}
