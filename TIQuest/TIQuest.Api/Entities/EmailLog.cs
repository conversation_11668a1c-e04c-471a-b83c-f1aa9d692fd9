﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(EmailLog))]
    public class EmailLog
    {
        [Key]
        public int Id { get; set; }
        public DateTime Date { get; set; }
        public string Email { get; set; }
        public string Subject { get; set; }
        public string Status { get; set; }
        public string Location { get; set; }
    }
}
