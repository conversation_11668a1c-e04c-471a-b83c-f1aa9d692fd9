﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(User))]
    [Index(nameof(Email), IsUnique = true)]
    public class User : EntityModificationLog
    {
        [Key]
        public int Id { get; set; }
        [Column("UserRole")]
        public int UserRoleId { get; set; }
        [ForeignKey(nameof(UserRoleId))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public UserRole UserRole { get; set; }
        [MaxLength(80)]
        public string FirstName { get; set; }
        [MaxLength(80)]
        public string LastName { get; set; }
        [MaxLength(200)]
        public string Email { get; set; }
        [MaxLength(64)]
        public string? PasswordHash { get; set; }
        public bool IsActive { get; set; }
    }
}
