﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(DistributionTransaction))]
    [Keyless]
    public class DistributionTransaction
    {
        [Column("Distribution")]
        public int DistributionId { get; set; }
        [ForeignKey(nameof(DistributionId))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public Distribution Distribution { get; set; }

        [Column("Transaction")]
        public int TransactionId { get; set; }
        [ForeignKey(nameof(TransactionId))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public Transaction Transaction { get; set; }
    }
}
