﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(Partnership))]
    public class Partnership : EntityModificationLog
    {
        [Key]
        public int Id { get; set; }
        [MaxLength(200)]
        [Column(TypeName = "varchar")]
        public string Name { get; set; }
        [MaxLength(2000)]
        [Column(TypeName = "varchar")]
        public string? Description { get; set; }
        [MaxLength(100)]
        public string? Apartment { get; set; }
        [MaxLength(100)]
        public string? Address { get; set; }
        [MaxLength(100)]
        public string? City { get; set; }
        [MaxLength(100)]
        public string? State { get; set; }
        [MaxLength(30)]
        public string? Zip { get; set; }
        [MaxLength(100)]
        public string Country { get; set; }
        public bool IsActive { get; set; }
    }
}
