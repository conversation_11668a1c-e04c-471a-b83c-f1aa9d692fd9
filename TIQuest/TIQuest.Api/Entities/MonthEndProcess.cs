﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(MonthEndProcess))]
    public class MonthEndProcess
    {
        [Key]
        public int Id { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }
        public string Description { get; set; }
        [Precision(7, 5)]
        public decimal InvestmentRate { get; set; }
        [Precision(7, 5)]
        public decimal LoanRate { get; set; }
        [Precision(7, 5)]
        public decimal ManagementRate { get; set; }
        [Precision(15, 5)]
        public decimal ManagementFee { get; set; }
    }
}
