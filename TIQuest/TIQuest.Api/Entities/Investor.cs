﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(Investor))]
    public class Investor : EntityAddress
    {
        [Key]
        public int Id { get; set; }
        [MaxLength(80)]
        public string? FirstName { get; set; }
        [MaxLength(80)]
        public string? LastName { get; set; }
        [MaxLength(200)]
        public string? CompanyName { get; set; }
        [MaxLength(320)]
        public string? Email { get; set; }
        [MaxLength(30)]
        public string? TaxNumber { get; set; }
        [MaxLength(30)]
        [Column(TypeName = "varchar")]
        public string Type { get; set; }
        [MaxLength(30)]
        [Column(TypeName = "varchar")]
        public string? OfficePhone { get; set; }
        [MaxLength(30)]
        [Column(TypeName = "varchar")]
        public string? HomePhone { get; set; }
        [MaxLength(30)]
        [Column(TypeName = "varchar")]
        public string? Mobile { get; set; }
        [MaxLength(30)]
        [Column(TypeName = "varchar")]
        public string? Fax { get; set; }
        [MaxLength(400)]
        [Column(TypeName = "varchar")]
        public string? Note { get; set; }
        public bool IsActive { get; set; }
        public int LastModifiedBy { get; set; }
        [ForeignKey(nameof(LastModifiedBy))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public User UserModifiedBy { get; set; }
        public DateTime LastModifiedOn { get; set; }
        public int CreatedBy { get; set; }
        [ForeignKey(nameof(CreatedBy))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public User UserCreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
    }
}
