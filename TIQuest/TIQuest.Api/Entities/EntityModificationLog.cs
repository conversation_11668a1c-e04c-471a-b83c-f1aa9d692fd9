﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
    public abstract class EntityModificationLog
    {
        public int LastModifiedBy { get; set; }
        [ForeignKey(nameof(LastModifiedBy))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public User UserModifiedBy { get; set; }
        public DateTime LastModifiedOn { get; set; }
        public int CreatedBy { get; set; }
        [ForeignKey(nameof(CreatedBy))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public User UserCreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
    }
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
}
