﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(TransactionCode))]
    public class TransactionCode
    {
        [Key]
        public int Id { get; set; }
        [MaxLength(6)]
        [Column(TypeName = "varchar")]
        public string Code { get; set; }
        [MaxLength(50)]
        [Column(TypeName = "varchar")]
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public bool IsDebit { get; set; }
        public bool IsTaxable { get; set; }
        public bool IsSystem { get; set; }
    }
}
