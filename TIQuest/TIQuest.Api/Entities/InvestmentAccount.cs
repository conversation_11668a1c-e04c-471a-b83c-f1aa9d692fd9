﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(InvestmentAccount))]
    [Index(nameof(AccountNumber), IsUnique = true)]
    public class InvestmentAccount : EntityModificationLog
    {
        [Key]
        public int Id { get; set; }
        [Column("Investor")]
        public int InvestorId { get; set; }
        [ForeignKey(nameof(InvestorId))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public Investor AccountInvestor { get; set; }
        [MaxLength(100)]
        public string Name { get; set; }
        [MaxLength(320)]
        [Column(TypeName = "varchar")]
        public string? Email { get; set; }
        [MaxLength(250)]
        [Column(TypeName = "varchar")]
        public string? Report1099Name { get; set; }
        [MaxLength(20)]
        public string AccountNumber { get; set; }
        [Precision(7, 5)]
        public decimal? Rate { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        [Precision(15, 5)]
        public decimal Balance { get; set; }
        [MaxLength(30)]
        [Column(TypeName = "varchar")]
        public string AccountType { get; set; }
        [MaxLength(20)]
        [Column(TypeName = "varchar")]
        public string? InterestType { get; set; }
        public Boolean SendEmail { get; set; }
        public Boolean SendMail { get; set; }
        public ICollection<Transaction> Transactions { get; set; } = new List<Transaction>();
        [MaxLength(30)]
        [Column(TypeName = "varchar")]
        public string? TaxNumber { get; set; }
    }
}
