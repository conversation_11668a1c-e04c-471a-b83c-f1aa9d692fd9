﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(MonthEndBalance))]
    [Keyless]
    public class MonthEndBalance
    {
        [Column("MonthEndProcess")]
        public int MonthEndProcessId { get; set; }
        [ForeignKey(nameof(MonthEndProcessId))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public MonthEndProcess MonthEndProcess { get; set; }
        [Column("Account")]
        public int AccountId { get; set; }
        [ForeignKey(nameof(AccountId))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public InvestmentAccount InvestmentAccount { get; set; }
        [Precision(15, 5)]
        public decimal StartingBalance { get; set; }
        [Precision(15, 5)]
        public decimal EndingBalance { get; set; }
    }
}
