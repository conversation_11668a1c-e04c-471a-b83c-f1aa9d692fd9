﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(MonthEndProcessTransaction))]
    [Keyless]
    public class MonthEndProcessTransaction
    {
        [Column("MonthEndProcess")]
        public int MonthEndProcessId { get; set; }
        [ForeignKey(nameof(MonthEndProcessId))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public MonthEndProcess MonthEndProcess { get; set; }

        [Column("Transaction")]
        public int TransactionId { get; set; }
        [ForeignKey(nameof(TransactionId))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public Transaction Transaction { get; set; }
    }
}
