﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TIQuest.Api.Entities
{
    [Table(nameof(Distribution))]
    public class Distribution
    {
        [Key]
        public int Id { get; set; }
        [Column("Partnership")]
        public int PartnershipId { get; set; }
        [ForeignKey(nameof(PartnershipId))]
        [DeleteBehavior(DeleteBehavior.NoAction)]
        public Partnership Partnership { get; set; }
        public DateTime Date { get; set; }
        [Precision(15, 5)]
        public decimal Amount { get; set; }
    }
}
