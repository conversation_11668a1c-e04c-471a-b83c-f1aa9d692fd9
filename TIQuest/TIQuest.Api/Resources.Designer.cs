﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace TIQuest.Api {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("TIQuest.Api.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;!DOCTYPE html&gt;
        ///&lt;html&gt;
        ///&lt;body&gt;
        ///    &lt;div style=&quot;color: #000000; font-family: Lato,sans-serif; margin-top: 20px; text-align: center; font-weight: bold; font-size: 30px;&quot;&gt;TI QUEST, LLC&lt;/div&gt;
        ///    &lt;br /&gt;
        ///    &lt;div style=&quot;background-color: #000000; height: 50px; color: white; font-family: Lato , sans-serif; padding: 35px 10px 10px; text-align: center; font-size: 20px;&quot;&gt;Please reset your password&lt;/div&gt;
        ///    &lt;div style=&quot;margin-left: 30px; font-size: 14px; font-family: Lato, sans-serif;&quot;&gt;
        ///        &lt;div style=&quot;fon [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string ChangePassword {
            get {
                return ResourceManager.GetString("ChangePassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;!DOCTYPE html&gt;
        ///&lt;html&gt;
        ///&lt;body&gt;
        ///    &lt;div style=&quot;color: #000000; font-family: Lato,sans-serif; margin-top: 20px; text-align: center; font-weight: bold; font-size: 30px;&quot;&gt;TI QUEST, LLC&lt;/div&gt;
        ///    &lt;br /&gt;
        ///    &lt;div style=&quot;background-color: #000000; height: 50px; color: white; font-family: Lato , sans-serif; padding: 35px 10px 10px; text-align: center; font-size: 20px;&quot;&gt;Please reset your password&lt;/div&gt;
        ///    &lt;div style=&quot;margin-left: 30px; font-size: 14px; font-family: Lato, sans-serif;&quot;&gt;
        ///        &lt;div style=&quot;fon [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string CreateNewUser {
            get {
                return ResourceManager.GetString("CreateNewUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;!DOCTYPE html&gt;
        ///&lt;html&gt;
        ///&lt;body&gt;
        ///    &lt;div style=&quot;color: #000000; font-family: Lato,sans-serif; margin-top: 20px; text-align: center; font-weight: bold; font-size: 30px;&quot;&gt;TI QUEST, LLC&lt;/div&gt;
        ///    &lt;br /&gt;
        ///    &lt;div style=&quot;background-color: #000000; height: 50px; color: white; font-family: Lato , sans-serif; padding: 35px 10px 10px; text-align: center; font-size: 20px;&quot;&gt;Please reset your password&lt;/div&gt;
        ///    &lt;div style=&quot;margin-left: 30px; font-size: 14px; font-family: Lato, sans-serif;&quot;&gt;
        ///        &lt;div style=&quot;fon [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string ResetPassword {
            get {
                return ResourceManager.GetString("ResetPassword", resourceCulture);
            }
        }
    }
}
