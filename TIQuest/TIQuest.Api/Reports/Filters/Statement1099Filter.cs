﻿namespace TIQuest.Api.Reports.Filters
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="Year"></param>
    /// <param name="ClientId"></param>
    /// <param name="AccountIds"></param>
    public record Statement1099Filter(int Year, int? ClientId, List<int>? AccountIds)
    {
        public Statement1099Filter(int year) : this(year, null, null) { }

        public Statement1099Filter(int year, int clientId) : this(year, clientId, null) { }
    }
}
