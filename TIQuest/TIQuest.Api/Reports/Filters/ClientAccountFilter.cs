﻿namespace TIQuest.Api.Reports.Filters
{
    public record struct ClientAccountFilter
    {
        public ClientAccountFilter(List<int> clients, bool onlyActiveClients, List<int> accounts, bool onlyActiveAccounts, int month, int year, bool email, string? accountType, int userId)
        {
            Clients = clients;
            OnlyActiveClients = onlyActiveClients;
            Accounts = accounts;
            OnlyActiveAccounts = onlyActiveAccounts;
            StartDate = new DateTime(year, month, 1);
            EndDate = StartDate.AddMonths(1);
            Email = email;
            AccountType = accountType;
            UserId = userId;
        }

        internal readonly List<int> Clients { get; }
        internal readonly bool OnlyActiveClients { get; }
        internal readonly List<int> Accounts { get; }
        internal readonly bool OnlyActiveAccounts { get; }
        internal readonly DateTime StartDate { get; }
        internal readonly DateTime EndDate { get; }
        internal readonly bool Email { get; }
        internal readonly int UserId { get; }
        internal readonly string? AccountType { get; }
    }
}
