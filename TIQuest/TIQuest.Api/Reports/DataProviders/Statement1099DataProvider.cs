﻿using Microsoft.EntityFrameworkCore;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.DataProviders;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Poco.Reports;
using TIQuest.Api.Reports.Filters;
using TIQuest.Api.Extensions;

namespace TIQuest.Api.Reports.DataProviders
{
    internal class Statement1099DataProvider : IStatement1099DataProvider
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;
        private readonly ICompanyReportService _companyReportService;

        public Statement1099DataProvider(IDbContextFactory<DataContext> dbContextFactory, ICompanyReportService companyReportService)
        {
            _contextFactory = dbContextFactory;
            _companyReportService = companyReportService;
        }

        public async Task<IEnumerable<Statement1099Row>> GetReportDataAsync(Statement1099Filter filter, CancellationToken cancellationToken = default)
        {
            var company = await _companyReportService.GetCompanyReportDataAsync(cancellationToken);

            return await GetStatement1099DataAsync(filter, company, cancellationToken);
        }

        private async Task<IEnumerable<Statement1099Row>> GetStatement1099DataAsync(Statement1099Filter filter, CompanyReport company, CancellationToken cancellationToken)
        {
            // first second of the selected year
            DateTime startDate = new DateTime(filter.Year, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            // last second of the selected year
            DateTime endDate = startDate.AddYears(1).AddTicks(-1);
            //DateTime endDate = startDate.AddYears(1).AddSeconds(-1);
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var voidStatus = await ctx.TransactionStatuses.FirstAsync(x => x.Description == Constants.DataConstants.VoidStatus, cancellationToken);
                var query = GetTransactions(ctx, startDate, endDate, voidStatus.Id);

                query = query.Where(x =>
                    !string.IsNullOrWhiteSpace(x.TaxID) &&
                    x.TaxID.Length >= 2 &&
                    EF.Functions.Like(x.TaxID.Substring(0, 2), "[0-9][0-9]"));

                if (filter.ClientId.HasValue)
                {
                    query = query.Where(x => x.ClientID == filter.ClientId.Value);
                }

                if (filter.AccountIds is not null && filter.AccountIds.Count > 0)
                {
                    query = query.Where(x => filter.AccountIds.Contains(x.AccountID));
                }
                var output = await query.ToListAsync(cancellationToken);
                if (output.Count > 0)
                {
                    var filteredRecords = output.GroupBy(x => x.AccountID)
                                            .Select(x =>
                                            {
                                                var first = x.First();
                                                return new Statement1099Row
                                                {
                                                    FullName = GetFullName(first),
                                                    FirstName = first.FirstName,
                                                    LastName = first.LastName,
                                                    ClientId = first.ClientID,
                                                    AccountName = first.AccountName,
                                                    ClientCompanyName = first.CompanyName,
                                                    Report1099Name = first.Report1099Name,
                                                    TaxID = first.TaxID?.FormatTaxID(1),
                                                    Address = first.Address,
                                                    City = first.City,
                                                    State = first.State,
                                                    PostalCode = first.PostalCode,
                                                    AccountNumber = first.AccountNumber,
                                                    Country = first.Country,
                                                    InterestTotal = x.Sum(x => x.Amount),
                                                    CompanyName = company.Name,
                                                    CompanyAddress = company.Address,
                                                    CompanyCityStateZip = company.CityStateZip,
                                                    PayerTaxID = company.TaxNumber?.FormatTaxID(1)
                                                };
                                            })
                                            .Where(x => x.InterestTotal > 0)
                                            .OrderBy(x => !string.IsNullOrWhiteSpace(x.FullName) ? x.FullName : x.LastName)
                                            .ThenBy(x => x.ClientId)
                                            .ThenBy(x => x.FirstName)
                                            .ThenBy(x => x.AccountName);

                    return filteredRecords;
                }
                return new List<Statement1099Row>();
            }
        }

        private static IQueryable<Statement1099Transactions> GetTransactions(DataContext ctx, DateTime startDate, DateTime endDate, int voidStatusId)
        {
            return from transaction in ctx.Transactions
                   join transactionCode in ctx.TransactionCodes on transaction.CodeId equals transactionCode.Id
                   join account in ctx.InvestmentAccounts on transaction.AccountId equals account.Id
                   join client in ctx.Investors on account.InvestorId equals client.Id
                   where
                       transaction.Date >= startDate
                       && transaction.Date <= endDate
                       && transaction.StatusId != voidStatusId
                       && (transactionCode.Code == Constants.DataConstants.InvestmentInterestCode || transactionCode.Code == Constants.DataConstants.LoanInterestCode)
                       && transaction.Amount > 0
                       && account.Id != Constants.DataConstants.CashAccountId
                       && account.AccountType == Constants.DataConstants.InvestmentAccountType
                       && account.CreatedOn <= endDate
                       && (account.EndDate == null || account.EndDate >= startDate)
                   select new Statement1099Transactions
                   {
                       TaxID = !string.IsNullOrWhiteSpace(account.TaxNumber) ? account.TaxNumber : client.TaxNumber,
                       Address = client.Address,
                       AccountNumber = account.AccountNumber,
                       CompanyName = client.CompanyName,
                       Report1099Name = account.Report1099Name,
                       City = client.City,
                       PostalCode = client.Zip,
                       State = client.State,
                       Country = client.Country,
                       Amount = transaction.Amount,
                       AccountID = transaction.AccountId,
                       FirstName = client.FirstName,
                       LastName = client.LastName,
                       ClientID = client.Id,
                       AccountName = account.Name
                   };
        }

        private static string? GetFullName(Statement1099Transactions first)
        {
            if (!string.IsNullOrWhiteSpace(first.TaxID))
            {
                return first.AccountName;
            }
            else if (!string.IsNullOrWhiteSpace(first.CompanyName))
            {
                return first.CompanyName;
            }
            else if (!string.IsNullOrWhiteSpace(first.FirstName) && !string.IsNullOrWhiteSpace(first.LastName))
            {
                return string.Format("{0}, {1}", first.LastName, first.FirstName);
            }
            else if (!string.IsNullOrWhiteSpace(first.LastName))
            {
                return first.LastName;
            }
            return first.FirstName;
        }
    }
}
