﻿using Microsoft.EntityFrameworkCore;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.DataProviders;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Poco.Reports;
using TIQuest.Api.Reports.Filters;

namespace TIQuest.Api.Reports.DataProviders
{
    internal class Statement1096DataProvider(IDbContextFactory<DataContext> dbContextFactory, ICompanyReportService companyReportService) : IForm1096DataProvider
    {
        private readonly IDbContextFactory<DataContext> _contextFactory = dbContextFactory;
        private readonly ICompanyReportService _companyReportService = companyReportService;

        public async Task<IEnumerable<Form1096Report>> GetReportDataAsync(Form1096Filter filter, CancellationToken cancellationToken = default)
        {
            var company = await _companyReportService.GetCompanyReportDataAsync(cancellationToken);

            return await GetStatement1096DataAsync(filter, company, cancellationToken);
        }

        private async Task<IEnumerable<Form1096Report>> GetStatement1096DataAsync(Form1096Filter filter, CompanyReport company, CancellationToken cancellationToken)
        {

            var amount = await GetForm1096DataAsync(filter.Year, cancellationToken);

            return
            [
                new()
                {
                    CompanyName = company.Name,
                    CompanyAddress = company.Address,
                    CompanyCityStateZip = company.CityStateZip,
                    ContactFullName = company.FirstName + " " + company.LastName,
                    FaxNumber = company.Fax,
                    PhoneNumber = company.OfficePhone,
                    Email = company.Email,
                    TaxID = company.TaxNumber,
                    TotalAmount = amount
                }
            ];
        }

        private async Task<decimal> GetForm1096DataAsync(int year, CancellationToken cancellationToken)
        {
            await using var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken);

            DateTime startDate = new(year, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            DateTime endDate = startDate.AddYears(1).AddTicks(-1);

            var query = from account in ctx.InvestmentAccounts
                        join client in ctx.Investors on account.InvestorId equals client.Id
                        where account.Id != 1 &&
                              account.StartDate < endDate &&
                              (account.EndDate == null || account.EndDate >= startDate || account.EndDate >= DateTime.UtcNow) &&
                              account.AccountType == "Investment"
                        select new
                        {
                            TaxNumber = !string.IsNullOrWhiteSpace(account.TaxNumber) ? account.TaxNumber : client.TaxNumber,
                            AccountId = account.Id
                        };

            var output = await query.ToListAsync(cancellationToken);

            var filteredOutput = output
                .Where(x => !string.IsNullOrWhiteSpace(x.TaxNumber) &&
                            x.TaxNumber.Length >= 2 &&
                            x.TaxNumber.Substring(0, 2).All(char.IsDigit))
                .OrderBy(x => x.AccountId);

            var accountIds = filteredOutput.Select(x => x.AccountId).ToList();

            var totalAmount = await ctx.Transactions
                .Where(t => accountIds.Contains(t.AccountId) &&
                            t.Date >= startDate && t.Date <= endDate &&
                            t.StatusId != 5 &&
                            (t.CodeId == 5 || t.CodeId == 6) &&
                            t.Amount > 0)
                .GroupBy(t => t.AccountId)
                .Select(g => g.Sum(t => t.Amount))
                .SumAsync(cancellationToken);

            return totalAmount;
        }
    }
}
