﻿using Microsoft.EntityFrameworkCore;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.DataProviders;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Poco.Reports;
using TIQuest.Api.Reports.Filters;

namespace TIQuest.Api.Reports.DataProviders
{
    internal class ClientAccountDataProvider : IClientAccountDataProvider
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;
        private readonly ICompanyReportService _companyReportService;

        public ClientAccountDataProvider(IDbContextFactory<DataContext> dbContextFactory, ICompanyReportService companyReportService)
        {
            _contextFactory = dbContextFactory;
            _companyReportService = companyReportService;
        }

        public async Task<ICollection<InvestmentAccount>> GetApplicableInvestmentAccountsAsync(ClientAccountFilter filter, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                return await GetAccountsQueryable(ctx, filter).ToListAsync(cancellationToken);
            }
        }

        public async Task<ICollection<ClientAccountReport>> GetClientsAccountReportAsync(ClientAccountFilter filter, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var accountsQueryable = GetAccountsQueryable(ctx, filter)
                                        .Select(x => new ClientAccountReport
                                        {
                                            AccountId = x.Id,
                                            AccountName = x.Name,
                                            AccountNumber = x.AccountNumber,
                                            ClientName = !string.IsNullOrWhiteSpace(x.AccountInvestor.CompanyName)
                                                            ? x.AccountInvestor.CompanyName
                                                            : $"{x.AccountInvestor.FirstName} {x.AccountInvestor.LastName}",
                                            ClientAddress = x.AccountInvestor.Address,
                                            ClientApartment = x.AccountInvestor.Apartment,
                                            ClientCity = x.AccountInvestor.City,
                                            ClientState = x.AccountInvestor.State,
                                            ClientZip = x.AccountInvestor.Zip,
                                            ClientCountry = x.AccountInvestor.Country
                                        });

                return await accountsQueryable.ToListAsync(cancellationToken);
            }
        }

        public Task<CompanyReport> GetCompanyReportAsync(CancellationToken cancellationToken = default)
        {
            return _companyReportService.GetCompanyReportDataAsync(cancellationToken);
        }

        public async Task<IEnumerable<Transaction>> GetTransactionsAsync(IEnumerable<int> accounts, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var transactions = await ctx.Transactions
                                            .Include(x => x.TransactionCode)
                                            .Include(x => x.InvestmentAccount)
                                            .Include(x => x.TransactionStatus)
                                            .Where(x => accounts.Contains(x.AccountId)
                                            && x.Date >= startDate && x.Date < endDate
                                            && x.TransactionStatus.Description != Constants.DataConstants.VoidStatus)
                                            .ToListAsync(cancellationToken);
                return transactions;
            }
        }

        private IQueryable<InvestmentAccount> GetAccountsQueryable(DataContext ctx, ClientAccountFilter filter)
        {
            var queryable = ctx.InvestmentAccounts
                                   .Include(x => x.AccountInvestor)
                                   .Where(x => x.InvestorId != Constants.DataConstants.SystemInvestorId);

            if (filter.Clients.Count > 0)
            {
                queryable = queryable.Where(x => filter.Clients.Contains(x.InvestorId));
            }

            if (filter.OnlyActiveClients)
            {
                queryable = queryable.Where(x => x.AccountInvestor.IsActive);
            }
            else
            {
                queryable = queryable.Where(x => !x.AccountInvestor.IsActive);
            }

            if (filter.Accounts.Count > 0)
            {
                queryable = queryable.Where(x => filter.Accounts.Contains(x.Id));

            }

            if (filter.OnlyActiveAccounts)
            {
                queryable = queryable.Where(x => x.StartDate <= filter.EndDate
                                                   && (x.EndDate == null || x.EndDate > DateTime.UtcNow));
            }
            else
            {
                queryable = queryable.Where(x => x.EndDate < DateTime.UtcNow);
            }
            if (!string.IsNullOrWhiteSpace(filter.AccountType))
            {
                queryable = queryable.Where(x => x.AccountType == filter.AccountType);
            }

            return queryable;
        }
    }
}
