﻿using Microsoft.EntityFrameworkCore;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.DataProviders;
using TIQuest.Api.Poco;

namespace TIQuest.Api.Reports.DataProviders
{
    internal class TransactionDataProvider : ITransactionDataProvider
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;
        private const int SYSTEM_ACCOUNT_ID = 3;

        public TransactionDataProvider(IDbContextFactory<DataContext> dbContextFactory)
        {
            _contextFactory = dbContextFactory;
        }
        public async Task<IEnumerable<Poco.Entities.Transaction>> GetTransactionsAsync(int userId, DateTime startDate, DateTime endDate, bool includeVoid, string? excludeTransactionCodes, CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var queryable = GetTransactionsQueryable(ctx, userId, startDate, endDate);
                if (!includeVoid)
                {
                    queryable = queryable.Where(x => x.TransactionStatus.Description != Constants.DataConstants.VoidStatus);
                }

                if (!string.IsNullOrWhiteSpace(excludeTransactionCodes))
                {
                    var transactionCodes = excludeTransactionCodes.Split(',');
                    if (transactionCodes != null && transactionCodes.Any())
                    {
                        queryable = queryable.Where(x => !transactionCodes.Contains(x.Code));
                    }
                }

                return await queryable.ToListAsync(cancellationToken);
            }
        }

        public async Task<IEnumerable<TransactionRecordWithBalance>> GetTransactionWithBalancesAsync(int userId, DateTime startDate, DateTime endDate, bool includeVoid, CancellationToken cancellationToken = default)
        {
            var transactions = await GetTransactionsAsync(userId,
                                    new DateTime(startDate.Year, startDate.Month, 1, startDate.Hour, startDate.Minute, startDate.Second),
                                    endDate, includeVoid, null, cancellationToken);

            var transactionsByAccount = transactions.GroupBy(x => x.Account.Id);

            var distinctAccounts = transactionsByAccount.Select(x => x.Key).Distinct();

            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var records = new List<TransactionRecordWithBalance>();

                var previousMonthEnd = await ctx.MonthEndProcesses.FirstOrDefaultAsync(x => x.Month == startDate.AddMonths(-1).Month
                                                                               && x.Year == startDate.AddMonths(-1).Year, cancellationToken);

                bool hasPreviousMonthEndClosed = previousMonthEnd is not null;

                var secondLastMonthEnd = await ctx.MonthEndProcesses.FirstOrDefaultAsync(x => x.Month == startDate.AddMonths(-2).Month
                                                                               && x.Year == startDate.AddMonths(-2).Year, cancellationToken);

                if (hasPreviousMonthEndClosed || secondLastMonthEnd is not null)
                {
                    var requiredProcessId = hasPreviousMonthEndClosed ? previousMonthEnd.Id : secondLastMonthEnd.Id;

                    var monthEndBalances = await ctx.MonthEndBalances
                                                    .Where(x => distinctAccounts.Contains(x.AccountId)
                                                                && x.MonthEndProcessId == requiredProcessId)
                                                    .ToListAsync(cancellationToken);

                    foreach (var transactionGroup in transactionsByAccount)
                    {
                        var orderedTransactions = transactionGroup.OrderBy(x => x.Date);
                        var account = orderedTransactions.First().Account;
                        // find month end balance for the account
                        var accountBalance = monthEndBalances.FirstOrDefault(x => x.AccountId == account.Id)?.EndingBalance ?? 0;
                        // find changes in balance before the start date but after the month end
                        var intermediaryBalanceChanges = orderedTransactions.Where(x => new DateTime(x.Date, TimeOnly.MinValue) < startDate).Sum(x => x.Amount);
                        // add up those changes to identify running balance
                        decimal runningBalance = accountBalance + intermediaryBalanceChanges;

                        string clientName = !string.IsNullOrWhiteSpace(account.Investor.CompanyName) ? account.Investor.CompanyName : $"{account.Investor.LastName} {account.Investor.FirstName}";
                        var transactionsWithinRange = orderedTransactions.Where(x => new DateTime(x.Date, TimeOnly.MinValue) >= startDate).OrderBy(x => x.Date).ThenBy(x => x.Id);
                        for (int i = 0; i < transactionsWithinRange.Count(); i++)
                        {
                            var transaction = transactionsWithinRange.ElementAt(i);
                            runningBalance += transaction.Amount;
                            var summaryRecord = new TransactionRecordWithBalance
                            {
                                ClientName = clientName,
                                AccountName = account.Name,
                                AccountNumber = account.AccountNumber,
                                Date = transaction.Date,
                                CreatedDate = transaction.CreatedOn,
                                Amount = transaction.Amount,
                                Status = transaction.TransactionStatus.Description,
                                BankTransactionType = transaction.BankTransactionType,
                                Description = transaction.Description,
                                Balance = runningBalance
                            };

                            records.Add(summaryRecord);
                        }
                    }
                }

                return [.. records.OrderByDescending(r => r.Date)];
            }
        }

        private IQueryable<Poco.Entities.Transaction> GetTransactionsQueryable(DataContext ctx, int userId, DateTime startDate, DateTime endDate)
        {
            return (from permission in ctx.UserAccountPermissions
                    join account in ctx.InvestmentAccounts on permission.InvestmentAccountId equals account.Id
                    join investor in ctx.Investors on account.InvestorId equals investor.Id
                    join transaction in ctx.Transactions on account.Id equals transaction.AccountId
                    join transactionCode in ctx.TransactionCodes on transaction.CodeId equals transactionCode.Id
                    join transactionStatus in ctx.TransactionStatuses on transaction.StatusId equals transactionStatus.Id
                    where account.Id > SYSTEM_ACCOUNT_ID
                    && permission.UserId == userId
                    && transaction.Date >= startDate && transaction.Date <= endDate
                    select new Poco.Entities.Transaction
                    {
                        Id = transaction.Id,
                        Code = transactionCode.Description,
                        Amount = transaction.Amount,
                        Date = DateOnly.FromDateTime(transaction.Date),
                        Description = transaction.Description,
                        WireNumber = transaction.WireNumber,
                        CreatedOn = transaction.CreatedOn,
                        TransactionStatus = new Poco.Entities.TransactionStatus
                        {
                            Id = transactionStatus.Id,
                            Description = transactionStatus.Description
                        },
                        Account = new Poco.Entities.InvestmentAccount
                        {
                            Id = account.Id,
                            AccountNumber = account.AccountNumber,
                            Name = account.Name,
                            Balance = account.Balance,
                            Investor = new Poco.Entities.Investor
                            {
                                Id = investor.Id,
                                FirstName = investor.FirstName,
                                LastName = investor.LastName,
                                CompanyName = investor.CompanyName,
                                Type = investor.Type
                            }
                        },
                        AchDetails = transaction.AchDetails,
                        BankTransactionType = transaction.BankTransactionType
                    });
        }
    }
}
