using Microsoft.EntityFrameworkCore;
using System.Linq;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.DataProviders;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Poco.Reports;
using TIQuest.Api.Reports.Filters;

namespace TIQuest.Api.Reports.DataProviders
{
    internal class DistributionTransactionDataProvider : IDistributionTransactionDataProvider
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;
        private readonly IInvestorService _investorService;

        public DistributionTransactionDataProvider(IDbContextFactory<DataContext> contextFactory, IInvestorService investorService)
        {
            _contextFactory = contextFactory;
            _investorService = investorService;
        }

        public async Task<DistributionTransactionReport> GetDistributionReportDataAsync(
            DistributionTransactionFilter filter, 
            CancellationToken cancellationToken = default)
        {
            using var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken);

            // Get distribution details
            var distribution = await ctx.Distributions
                .Include(d => d.Partnership)
                .FirstOrDefaultAsync(d => d.Id == filter.DistributionId, cancellationToken);

            if (distribution == null)
            {
                throw new ArgumentException($"Distribution with ID {filter.DistributionId} not found.");
            }

            // Get investor information from cash account (ID = 1)
            var investor = await _investorService.FindByIdAsync(Constants.DataConstants.CashAccountId, cancellationToken);

            // Use investor information if available, otherwise use default values
            var companyName = investor?.CompanyName ?? 
                             (investor != null ? $"{investor.FirstName} {investor.LastName}".Trim() : "");
            var companyAddress = investor?.Address ?? "";
            var companyCityStateZip = investor != null ? 
                                    $"{investor.City}, {investor.State} {investor.Zip}".Trim().TrimEnd(',').Trim() : "";
            var companyEmail = investor?.Email ?? "";
            var rawPhone = investor?.OfficePhone ?? investor?.HomePhone ?? investor?.Mobile ?? "";
            var companyPhone = FormatPhoneNumber(rawPhone);

            // Get distribution transactions excluding cash account (ID = 1)
            var transactions = await ctx.DistributionTransactions
                .Include(dt => dt.Transaction)
                    .ThenInclude(t => t.InvestmentAccount)
                        .ThenInclude(ia => ia.AccountInvestor)
                .Include(dt => dt.Transaction)
                    .ThenInclude(t => t.TransactionCode)
                .Where(dt => dt.DistributionId == filter.DistributionId 
                    && dt.Transaction.AccountId != Constants.DataConstants.CashAccountId)
                .ToListAsync(cancellationToken);

            var transactionDetails = transactions.Select(dt => new DistributionTransactionDetail
            {
                Id = dt.Transaction.Id,
                Date = DateOnly.FromDateTime(dt.Transaction.Date),
                Description = dt.Transaction.Description ?? string.Empty,
                Amount = dt.Transaction.Amount,
                AccountName = dt.Transaction.InvestmentAccount.Name,
                AccountNumber = dt.Transaction.InvestmentAccount.AccountNumber,
                // Include investor information from the transaction's investment account
                InvestorCompanyName = dt.Transaction.InvestmentAccount.AccountInvestor != null ?
                                     (dt.Transaction.InvestmentAccount.AccountInvestor.Type == "Company" ?
                                      dt.Transaction.InvestmentAccount.AccountInvestor.CompanyName ?? "" :
                                      $"{dt.Transaction.InvestmentAccount.AccountInvestor.FirstName} {dt.Transaction.InvestmentAccount.AccountInvestor.LastName}".Trim()) : "",
                InvestorAddress1 = dt.Transaction.InvestmentAccount.AccountInvestor?.Address ?? "",
                InvestorCityStateZip = dt.Transaction.InvestmentAccount.AccountInvestor != null ? 
                                      $"{dt.Transaction.InvestmentAccount.AccountInvestor.City}, {dt.Transaction.InvestmentAccount.AccountInvestor.State} {dt.Transaction.InvestmentAccount.AccountInvestor.Zip}".Trim().TrimEnd(',').Trim() : "",
                InvestorCountry = dt.Transaction.InvestmentAccount.AccountInvestor?.Country ?? "",
                InvestorAddress2 = dt.Transaction.InvestmentAccount.AccountInvestor?.Apartment ?? "",
                // Apply conditional AsOfDate logic for each transaction
                TransactionAsOfDate = dt.Transaction.Date < DateTime.Now ?
                                     DateTime.Now.ToShortDateString() :
                                     dt.Transaction.Date.ToShortDateString()
            })
            .OrderBy(t => t.Date)
            .ToList();

            return new DistributionTransactionReport
            {
                DistributionId = distribution.Id,
                PartnershipName = distribution.Partnership.Name,
                DistributionDate = DateOnly.FromDateTime(distribution.Date),
                TotalAmount = distribution.Amount,
                CompanyName = companyName,
                CompanyAddress = companyAddress,
                CompanyCityStateZip = companyCityStateZip,
                CompanyEmail = companyEmail,
                CompanyPhone = companyPhone,
                ReceiptNumber = distribution.Id.ToString(),
                AsOfDate = DateTime.UtcNow,
                ClientAddress1 = distribution.Partnership.Address ?? string.Empty,
                ClientAddress2 = distribution.Partnership.Apartment ?? string.Empty,
                ClientCityStateZip = $"{distribution.Partnership.City ?? string.Empty}, {distribution.Partnership.State ?? string.Empty} {distribution.Partnership.Zip ?? string.Empty}".Trim(),
                ClientCountry = distribution.Partnership.Country ?? string.Empty,
                Transactions = transactionDetails
            };
        }

        private static string FormatPhoneNumber(string? phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return "";

            // Remove all non-digit characters
            var digits = new string(phoneNumber.Where(char.IsDigit).ToArray());

            // Format as (XXX) XXX-XXXX if we have 10 digits
            if (digits.Length == 10)
            {
                return $"({digits.Substring(0, 3)}) {digits.Substring(3, 3)}-{digits.Substring(6, 4)}";
            }

            // Return original if not 10 digits
            return phoneNumber;
        }
    }
}
