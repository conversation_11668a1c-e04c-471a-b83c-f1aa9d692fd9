﻿using Microsoft.EntityFrameworkCore;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.DataProviders;
using TIQuest.Api.Poco.Reports;

namespace TIQuest.Api.Reports.DataProviders
{
    internal class CheckDataProvider : ICheckDataProvider
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;

        public CheckDataProvider(IDbContextFactory<DataContext> dbContextFactory)
        {
            _contextFactory = dbContextFactory;
        }

        public async Task<IEnumerable<Check>> GetPrintableChecksAsync(IEnumerable<int> ids, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var checkRegisters = await ctx.CheckRegisters
                                            .Include(x => x.Transaction)
                                            .Include(x => x.Transaction.InvestmentAccount)
                                            .Where(x => !x.IsPrinted && x.Number != null & ids.Contains(x.Id))
                                            .OrderBy(x => x.Number)
                                            .ToListAsync(cancellationToken);

                return checkRegisters.Select(x => new Check
                (
                    x.Id,
                    x.Number,
                    x.Transaction.InvestmentAccount.AccountNumber,
                    x.Memo,
                    Math.Abs(x.Transaction.Amount),
                    x.Date,
                    x.Payee,
                    x.Address,
                    x.Apartment,
                    x.City,
                    x.State,
                    x.Zip,
                    x.Country
                ));
            }
        }
    }
}
