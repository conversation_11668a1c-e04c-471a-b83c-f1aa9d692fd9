﻿using TIQuest.Api.Interfaces.DataProviders;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Poco;
using TIQuest.Api.Poco.Reports;
using TIQuest.Api.Reports.Filters;

namespace TIQuest.Api.Reports.DataProviders
{
    internal class AccountStatementDataProvider : IAccountStatementDataProvider
    {
        private readonly IReportService _reportService;
        private readonly ICompanyReportService _companyReportService;

        public AccountStatementDataProvider(IReportService reportService, ICompanyReportService companyReportService)
        {
            _reportService = reportService;
            _companyReportService = companyReportService;
        }

        public Task<CompanyReport> GetCompanyReportAsync(CancellationToken cancellationToken = default)
        {
            return _companyReportService.GetCompanyReportDataAsync(cancellationToken);
        }

        public Task<IEnumerable<TransactionStatementRecord>> GetTransactionStatementsAsync(StatementFilter filter, CancellationToken cancellationToken = default)
        {
            return _reportService.GetStatementDetailsAsync(filter.Month, filter.Year, filter.AccountType, cancellationToken);
        }
    }
}
