<?xml version="1.0" encoding="utf-8"?>
<ActiveReportsLayout Version="3.2" PrintWidth="12240" DocumentName="ActiveReports Document" ScriptLang="C#" MasterReport="0">
  <StyleSheet>
    <Style Name="Normal" Value="font-family: Arial; font-style: normal; font-variant: inherit; font-weight: normal; font-size: 10pt; font-size-adjust: inherit; font-stretch: inherit; color: rgb(0,0,0); background-repeat: inherit; background-attachment: inherit; opacity: inherit; word-spacing: inherit; letter-spacing: inherit; text-decoration: none; vertical-align: inherit; text-transform: inherit; text-align: inherit; text-indent: inherit; unicode-bidi: inherit; line-height: inherit; white-space: inherit; ddo-char-set: 0" />
    <Style Name="Heading1" Value="font-family: inherit; font-style: inherit; font-variant: inherit; font-weight: bold; font-size: 16pt; font-size-adjust: inherit; font-stretch: inherit" />
    <Style Name="Heading2" Value="font-family: Times New Roman; font-style: italic; font-variant: inherit; font-weight: bold; font-size: 14pt; font-size-adjust: inherit; font-stretch: inherit" />
    <Style Name="Heading3" Value="font-family: inherit; font-style: inherit; font-variant: inherit; font-weight: bold; font-size: 13pt; font-size-adjust: inherit; font-stretch: inherit" />
  </StyleSheet>
  <Sections>
    <Section Type="PageHeader" Name="PageHeader" Height="4320" BackColor="********">
      <Control Type="AR.Field" Name="MgmtCompanyName" Left="2160" Top="360" Width="3420" Height="288" Text="MgmtCompanyName" />
      <Control Type="AR.Field" Name="MgmtAddress1" Left="2160" Top="630" Width="3420" Height="288" Text="MgmtAddress1" />
      <Control Type="AR.Field" Name="MgmtCityStateZip" Left="2160" Top="900" Width="3420" Height="288" Text="MgmtCityStateZip" />
      <Control Type="AR.Label" Name="MonthlyStatementTitle" Left="7754.399" Top="180" Width="3240" Height="288" Caption="Investment Transaction Receipt #" Style="font-size: 9.75pt; font-weight: bold" />
      <Control Type="AR.Field" Name="AsOfDate" Left="10260" Top="450" Width="1440" Height="288" Text="AsOfDate" />
      <Control Type="AR.Label" Name="AsOf" Left="9540" Top="450" Width="720" Height="288" Caption="As Of:" />
      <Control Type="AR.Image" Name="Picture2" Left="450" Top="90" Width="1620" Height="1620" LineColor="********" LineWeight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ontrol>
      <Control Type="AR.Field" Name="ClientCompanyName" Left="810" Top="1890" Width="6480" Height="288" Text="ClientCompanyName" />
      <Control Type="AR.Field" Name="ClientAddress1" Left="819.9999" Top="2160" Width="6470" Height="288" Text="ClientAddress1" />
      <Control Type="AR.Label" Name="TranDateTitle" Left="450" Top="3690" Width="1350" Height="288" Caption="Date" />
      <Control Type="AR.Label" Name="TranDescriptionTitle" Left="1890" Top="3690" Width="3510" Height="288" Caption="Description" />
      <Control Type="AR.Label" Name="TranAmountTitle" Left="10350" Top="3690" Width="1440" Height="288" Caption="Amount" Style="text-align: right" />
      <Control Type="AR.Label" Name="TransHistoryTitle" Left="450" Top="3420" Width="11330" Height="288" Caption="Transaction" Style="background-color: Gainsboro; font-size: 9.75pt; font-weight: bold" />
      <Control Type="AR.Line" Name="Line7" X1="450" Y1="1800" X2="11790" Y2="1800" />
      <Control Type="AR.Line" Name="Line8" X1="460" Y1="3430" X2="11800" Y2="3430" />
      <Control Type="AR.Line" Name="Line9" X1="460" Y1="3970" X2="11800" Y2="3970" />
      <Control Type="AR.Field" Name="MgmtEmail" Left="2160" Top="1170" Width="3420" Height="288" Text="MgmtEmail" />
      <Control Type="AR.Field" Name="MgmtPhone" Left="2160" Top="1440" Width="3420" Height="288" Text="MgmtPhone" OutputFormat="(###) ###-####" />
      <Control Type="AR.Field" Name="AccountID" Left="8850" Top="1905" Width="2850" Height="288" Text="AccountID" />
      <Control Type="AR.Field" Name="AccountName" Left="8850" Top="2205" Width="2850" Height="288" Text="AccountName" />
      <Control Type="AR.Label" Name="AccountIDLabel" Left="7320" Top="1920" Width="1440" Height="288" Caption="Account #:" />
      <Control Type="AR.Label" Name="LabelAccountName" Left="7335" Top="2220" Width="1440" Height="288" Caption="Account Name:" />
      <Control Type="AR.Field" Name="ReceiptNumber" Left="10994.4" Top="180" Width="825.6006" Height="288" Text="Receipt#" />
      <Control Type="AR.Line" Name="Line6" X1="8005" Y1="460" X2="11800" Y2="460" />
      <Control Type="AR.Field" Name="ClientCityStateZip" Left="825" Top="2730" Width="6465" Height="288" Text="ClientCityStateZip" CanShrink="1" />
      <Control Type="AR.Field" Name="ClientCountry" Left="810" Top="3060" Width="6570" Height="270" Text="ClientCountry" />
      <Control Type="AR.Field" Name="ClientAddress2" Left="819.9999" Top="2430" Width="6470" Height="288" Text="ClientAddress2" CanShrink="1" />
    </Section>
    <Section Type="Detail" Name="Detail" Height="270" BackColor="********">
      <Control Type="AR.Field" Name="TranDate" DataField="TranDate" Left="450" Top="0" Width="1260" Height="288" Text="" Style="vertical-align: top" />
      <Control Type="AR.Field" Name="TransDescription" DataField="TransDescription" Left="1890" Top="0" Width="8010" Height="288" Text="" Style="vertical-align: top" />
      <Control Type="AR.Field" Name="TransAmount" DataField="TransAmount" Left="9990" Top="0" Width="1800" Height="288" Text="" OutputFormat="$#,##0.00" Style="text-align: right" />
    </Section>
    <Section Type="PageFooter" Name="PageFooter" Height="104" BackColor="********" />
  </Sections>
  <ReportComponentTray />
  <PageSettings LeftMargin="0" RightMargin="0" TopMargin="720" />
  <Parameters />
</ActiveReportsLayout>
