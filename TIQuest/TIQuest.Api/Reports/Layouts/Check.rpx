﻿<?xml version="1.0" encoding="utf-8"?>
<ActiveReportsLayout Version="3.5" PrintWidth="12225" DocumentName="ActiveReports Document" ScriptLang="C#" MasterReport="0" CompatibilityMode="CrossPlatform">
  <StyleSheet>
    <Style Name="Normal" Value="font-family: Arial; font-style: normal; font-variant: inherit; font-weight: normal; font-size: 10pt; font-size-adjust: inherit; font-stretch: inherit; color: rgb(0,0,0); background-repeat: inherit; background-attachment: inherit; opacity: inherit; word-spacing: inherit; letter-spacing: inherit; text-decoration: none; vertical-align: inherit; text-transform: inherit; text-align: inherit; text-indent: inherit; unicode-bidi: inherit; line-height: inherit; white-space: inherit; ddo-char-set: 0" />
    <Style Name="Heading1" Value="font-family: inherit; font-style: inherit; font-variant: inherit; font-weight: bold; font-size: 16pt; font-size-adjust: inherit; font-stretch: inherit" />
    <Style Name="Heading2" Value="font-family: Times New Roman; font-style: italic; font-variant: inherit; font-weight: bold; font-size: 14pt; font-size-adjust: inherit; font-stretch: inherit" />
    <Style Name="Heading3" Value="font-family: inherit; font-style: inherit; font-variant: inherit; font-weight: bold; font-size: 13pt; font-size-adjust: inherit; font-stretch: inherit" />
  </StyleSheet>
  <Sections>
    <Section Type="PageHeader" Name="PageHeader" Height="15810" BackColor="********">
      <Control Type="AR.Field" Name="AccountNum1" DataField="AccountNumber" Left="1260" Top="900" Width="3330" Height="288" Text="AccountNum1" />
      <Control Type="AR.Label" Name="lblAccount1" Left="360" Top="900" Width="900" Height="288" Caption="Account:" />
      <Control Type="AR.Label" Name="lblAccount2" Left="360" Top="6641" Width="900" Height="288" Caption="Account:" />
      <Control Type="AR.Field" Name="AccountNum2" DataField="AccountNumber" Left="1260" Top="6641" Width="3330" Height="288" Text="AccountNum2" />
      <Control Type="AR.Label" Name="lblMemo1" Left="360" Top="1170" Width="900" Height="288" Caption="Memo:" />
      <Control Type="AR.Label" Name="lblMemo2" Left="360" Top="6911" Width="900" Height="288" Caption="Memo:" />
      <Control Type="AR.Field" Name="CheckDate1" DataField="DateText" Left="7560" Top="1980" Width="1440" Height="288" Text="CheckDate1" Style="text-align: left" />
      <Control Type="AR.Field" Name="CheckDate2" DataField="DateText" Left="7560" Top="7200" Width="1440" Height="288" Text="CheckDate2" Style="text-align: left" />
      <Control Type="AR.Field" Name="CheckAmount1" DataField="AmountText" Left="9450" Top="1980" Width="1710" Height="288" Text="CheckAmount1" OutputFormat="$#,##0.00" Style="text-align: right" />
      <Control Type="AR.Field" Name="CheckAmount2" DataField="AmountText" Left="9450" Top="7200" Width="1710" Height="288" Text="CheckAmount2" OutputFormat="$#,##0.00" Style="text-align: right" />
      <Control Type="AR.Field" Name="Memo1" DataField="Memo" Left="1260" Top="1170" Width="10170" Height="288" Text="Memo1" />
      <Control Type="AR.Field" Name="Memo2" DataField="Memo" Left="1260" Top="6911" Width="10170" Height="288" Text="Memo2" />
      <Control Type="AR.Field" Name="ClientName1" DataField="Payee" Left="1260" Top="4121.28" Width="10170" Height="288" Text="ClientName1" />
      <Control Type="AR.Field" Name="ClientCityStateZip1" DataField="CityStateZip" Left="1260" Top="4770" Width="10170" Height="288" Text="" />
      <Control Type="AR.Field" Name="ClientName2" DataField="Payee" Left="1349.28" Top="9082.08" Width="10070" Height="288" Text="ClientName2" />
      <Control Type="AR.Field" Name="ClientCityStateZip2" DataField="CityStateZip" Left="1360" Top="9640" Width="10070" Height="288" Text="" />
      <Control Type="AR.Field" Name="ClientName3" DataField="Payee" Left="1349.28" Top="13811.04" Width="10080" Height="288" Text="ClientName3" />
      <Control Type="AR.Field" Name="ClientCityStateZip3" DataField="CityStateZip" Left="1350" Top="14369.04" Width="10080" Height="288" Text="ClientCityStateZip3" />
      <Control Type="AR.Field" Name="CheckDate3" DataField="DateText" Left="7380" Top="13271.04" Width="1440" Height="288" Text="CheckDate3" Style="text-align: right" />
      <Control Type="AR.Field" Name="CheckAmount3" DataField="AmountText" Left="10080" Top="13271.04" Width="1710" Height="288" Text="CheckAmount3" OutputFormat="$#,##0.00" Style="text-align: right" />
      <Control Type="AR.Field" Name="TextAmount1" DataField="AmountInWords" Left="540" Top="2430" Width="11520" Height="810" Text="TextAmount1" />
      <Control Type="AR.Field" Name="TextAmount2" DataField="AmountInWords" Left="450" Top="7740" Width="11610" Height="810" Text="TextAmount2" />
      <Control Type="AR.Field" Name="TextAmount3" DataField="AmountInWords" Left="449" Top="12618.72" Width="11610" Height="540" Text="TextAmount3" />
      <Control Type="AR.Field" Name="ClientAddress21" DataField="CombinedAddress" Left="1260" Top="4410" Width="10170" Height="288" Text="" />
      <Control Type="AR.Field" Name="ClientAddress22" DataField="CombinedAddress" Left="1360" Top="9370" Width="10070" Height="288" />
      <Control Type="AR.Field" Name="ClientAddress23" DataField="CombinedAddress" Left="1350" Top="14099.04" Width="10080" Height="288" Text="ClientAddress23" />
    </Section>
    <Section Type="Detail" Name="Detail" Height="0" BackColor="********" />
    <Section Type="PageFooter" Name="PageFooter" Height="0" BackColor="********" />
  </Sections>
  <ReportComponentTray />
  <PageSettings LeftMargin="0" RightMargin="0" TopMargin="0" BottomMargin="0" />
  <Parameters />
</ActiveReportsLayout>