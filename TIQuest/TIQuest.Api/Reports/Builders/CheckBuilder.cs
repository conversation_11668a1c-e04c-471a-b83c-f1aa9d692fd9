﻿using TIQuest.Api.Enums;
using TIQuest.Api.Interfaces.DataProviders;

namespace TIQuest.Api.Reports.Builders
{
    internal class CheckBuilder : ReportBuilderBase
    {
        private readonly ICheckDataProvider _dataProvider;
        private IEnumerable<Poco.Reports.Check> _checks;

        public CheckBuilder(ICheckDataProvider dataProvider) : base("Check.rpx")
        {
            _dataProvider = dataProvider;
            HasSection(ReportSection.Detail);
        }

        public override async Task PreloadDataAsync<TFilter>(TFilter filter, CancellationToken cancellationToken = default)
        {
            if (filter is not IEnumerable<int> idList)
            {
                throw new ArgumentException("filter is not valid", nameof(filter));
            }
            _checks = await _dataProvider.GetPrintableChecksAsync(idList, cancellationToken);
        }

        protected override Task<IEnumerable<dynamic>?> LoadReportDetailDataAsync<TFilter>(TFilter filter, CancellationToken cancellationToken = default)
        {
            if (filter is not int id)
            {
                throw new ArgumentException("filter is not valid", nameof(filter));
            }
            var returnList = new List<dynamic> { _checks.First(x => x.Id == id) };
            return Task.FromResult<IEnumerable<dynamic>?>(returnList);
        }
    }
}
