﻿using GrapeCity.ActiveReports;
using GrapeCity.ActiveReports.SectionReportModel;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.Linq;
using TIQuest.Api.Entities;
using TIQuest.Api.Enums;
using TIQuest.Api.Extensions;
using TIQuest.Api.Interfaces.DataProviders;
using TIQuest.Api.Poco.Reports;
using TIQuest.Api.Reports.Filters;

namespace TIQuest.Api.Reports.Builders
{
    internal class ClientAccountBuilder : ReportBuilderBase
    {
        private IClientAccountDataProvider _dataProvider;
        private readonly IDbContextFactory<DataContext> _contextFactory;
        private CompanyReport? _companyReport = null;
        private ICollection<ClientAccountReport>? _clientAccountReports = null;
        private IEnumerable<Transaction>? _transactions = null;
        private DateTime? _startDate = null;
        private DateTime? _endDate = null;
        private string? _quote = null;
        private string? _statement = null;
        private MonthEndProcess? _monthEndProcess = null;
        private IEnumerable<MonthEndBalance>? _previousMonthEndBalances = null;
        private IEnumerable<InvestmentAccount>? _investmentAccounts = null;

        public ClientAccountBuilder(IClientAccountDataProvider dataProvider, IDbContextFactory<DataContext> dbContextFactory) : base("ReportMonthlyAccountStatement.rpx")
        {
            _dataProvider = dataProvider;
            _contextFactory = dbContextFactory;
            // Initialize sections
            HasSection(ReportSection.ReportHeader);
            HasSection(ReportSection.PageHeader);
            HasSection(ReportSection.Detail);
            HasSection(ReportSection.ReportFooter);
        }

        public override async Task PreloadDataAsync<TFilter>(TFilter filter, CancellationToken cancellationToken = default)
        {
            if (filter is not ClientAccountFilter statementFilter)
            {
                throw new ArgumentException("filter is not valid", nameof(filter));
            }
            _startDate = statementFilter.StartDate;
            _endDate = statementFilter.EndDate;
            _companyReport = await _dataProvider.GetCompanyReportAsync(cancellationToken);
            _clientAccountReports = await _dataProvider.GetClientsAccountReportAsync(statementFilter, cancellationToken);
            _investmentAccounts = await _dataProvider.GetApplicableInvestmentAccountsAsync(statementFilter, cancellationToken);
            _transactions = await _dataProvider.GetTransactionsAsync(_clientAccountReports.Select(x => x.AccountId), statementFilter.StartDate, statementFilter.EndDate, cancellationToken);
            using (var ctx = _contextFactory.CreateDbContext())
            {
                _monthEndProcess = await ctx.MonthEndProcesses.FirstOrDefaultAsync(x => x.Month == statementFilter.StartDate.Month && x.Year == statementFilter.StartDate.Year, cancellationToken);
                var quoteSetting = await ctx.Settings.FirstOrDefaultAsync(x => x.Key == Constants.DataConstants.QuoteKey, cancellationToken);
                _quote = quoteSetting?.Value;
                var statementSetting = await ctx.Settings.FirstOrDefaultAsync(x => x.Key == Constants.DataConstants.InvestmentMessageKey, cancellationToken);
                _statement = statementSetting?.Value;
                var previousMonth = statementFilter.StartDate.AddMonths(-1);
                _previousMonthEndBalances = await ctx.MonthEndBalances
                                            .Include(x => x.MonthEndProcess)
                                            .Where(x => x.MonthEndProcess.Month == previousMonth.Month
                                                                        && x.MonthEndProcess.Year == previousMonth.Year)
                                            .ToListAsync(cancellationToken);
            }
        }

        protected override Task AssignReportHeaderFieldsAsync<TFilter>(SectionReport mainLayout, TFilter? filter, CancellationToken cancellationToken = default) where TFilter : default
        {
            if (filter is not int accountId)
            {
                throw new ArgumentException("filter is not valid", nameof(filter));
            }
            var reportHeaderSection = mainLayout.Sections["ReportHeader"];

            // company header fields
            SetCompanyHeaderFields(reportHeaderSection);
            var accountReport = _clientAccountReports.FirstOrDefault(x => x.AccountId == accountId);

            if (accountReport is not null)
            {
                // client header fields
                SetClientHeaderFields(accountReport, reportHeaderSection);
            }

            // account header fields
            SetAccountHeaderFields(reportHeaderSection, accountId);

            return Task.CompletedTask;
        }

        protected override Task AssignPageHeaderFieldsAsync<TFilter>(SectionReport mainLayout, TFilter? filter, CancellationToken cancellationToken = default) where TFilter : default
        {
            return Task.CompletedTask;
        }

        protected override Task<IEnumerable<dynamic>?> LoadReportDetailDataAsync<TFilter>(TFilter filter, CancellationToken cancellationToken = default)
        {
            if (filter is not int accountId)
            {
                throw new ArgumentException("filter is not valid", nameof(filter));
            }
            if (_transactions is not null)
            {
                var applicableTransactions = _transactions.Where(x => x.AccountId == accountId);
                var previousMonthBalanceForAccount = _previousMonthEndBalances?.FirstOrDefault(x => x.AccountId == accountId);
                decimal runningBalance = previousMonthBalanceForAccount?.EndingBalance ?? 0;
                var accountStatementDetails = applicableTransactions.OrderBy(x => x.Date)
                                                                    .Select(x =>
                                                                    {
                                                                        runningBalance += x.Amount;
                                                                        string currencyAmount = Math.Abs(x.Amount).FormatAsCurrency();
                                                                        return new
                                                                        {
                                                                            Date = x.Date.ToString(Constants.DataConstants.DateFormat),
                                                                            Description = x.Description,
                                                                            Debit = x.Amount < 0 ? currencyAmount : "",
                                                                            Credit = x.Amount > 0 ? currencyAmount : "",
                                                                            Balance = Math.Abs(runningBalance).FormatAsCurrency()
                                                                        };
                                                                    }).ToList();
                return Task.FromResult<IEnumerable<dynamic>?>(accountStatementDetails);
            }
            return Task.FromResult<IEnumerable<dynamic>?>(null);
        }

        protected override Task AssignReportFooterFieldsAsync<TFilter>(SectionReport mainLayout, TFilter? filter, CancellationToken cancellationToken = default) where TFilter : default
        {
            var reportFooterSection = mainLayout.Sections["ReportFooter"];
            ((Label)reportFooterSection.Controls["Quote"]).Text = _quote;
            ((Label)reportFooterSection.Controls["Statement"]).Text = _statement;

            return Task.CompletedTask;
        }

        private void SetAccountHeaderFields(Section reportHeaderSection, int accountId)
        {
            var transactions = _transactions.Where(x => x.AccountId == accountId);

            var avgBalanceTransactions = _transactions.Where(x => x.AccountId == accountId
                                                        && x.TransactionCode.Code != Constants.DataConstants.LoanInterestCode
                                                        && x.TransactionCode.Code != Constants.DataConstants.InvestmentInterestCode);

            var withdrawalTransactions = transactions.Where(x => x.Amount < 0
                                                        && x.TransactionCode.Code != Constants.DataConstants.LoanInterestCode
                                                        && x.TransactionCode.Code != Constants.DataConstants.InvestmentInterestCode);
            var depositTransactions = transactions.Where(x => x.Amount > 0 
                                                        && x.TransactionCode.Code != Constants.DataConstants.InvestmentInterestCode 
                                                        && x.TransactionCode.Code != Constants.DataConstants.LoanInterestCode);
            decimal withdrawalTotal = Math.Abs(withdrawalTransactions.Sum(x => x.Amount));
            decimal depositTotal = Math.Abs(depositTransactions.Sum(x => x.Amount));
            var investmentAccount = _investmentAccounts?.Where(x => x.Id == accountId).FirstOrDefault();
            decimal interestTotal = 0, startingBalance = 0, endingBalance = 0, annualRate = 0, effectiveRate = 0;


            if (investmentAccount is not null)
            {
                var previousMonthBalance = _previousMonthEndBalances?.FirstOrDefault(x => x.AccountId == accountId);
                // previous months ending balance is current months starting balance
                if (previousMonthBalance is not null)
                {
                    startingBalance = previousMonthBalance.EndingBalance;
                }
                var balanceCalculator = new MonthlyBalanceCalculator(_startDate.Value, _endDate.Value);
                var balance = balanceCalculator.CalculateAverageMonthlyBalance(avgBalanceTransactions, startingBalance);
                ((TextBox)reportHeaderSection.Controls["AverageDailyBalance"]).Text = Math.Abs(balance).FormatAsCurrency();

                if (investmentAccount.AccountType == Constants.DataConstants.InvestmentAccountType)
                {
                    ((Label)reportHeaderSection.Controls["DepositsTitle"]).Text = "Deposits";
                    ((Label)reportHeaderSection.Controls["WithdrawalsTitle"]).Text = "Withdrawals";

                    interestTotal = transactions.Where(x => x.TransactionCode.Code == Constants.DataConstants.InvestmentInterestCode).Sum(x => Math.Abs(x.Amount));
                    endingBalance = startingBalance + depositTotal + interestTotal - withdrawalTotal;
                    if (_monthEndProcess is not null)
                    {
                        annualRate = _monthEndProcess.InvestmentRate;
                        effectiveRate = CalculateEffectiveRate(_monthEndProcess.InvestmentRate, 12);
                    }
                    ((Label)reportHeaderSection.Controls["EffectiveInterestRateTitle"]).Text = "Effective Interest Rate:";
                    ((TextBox)reportHeaderSection.Controls["EffectiveInterestRate"]).Text = effectiveRate.FormatAsPercentage();

                    ((Label)reportHeaderSection.Controls["WithdrawalCount"]).Text = withdrawalTransactions.Count().ToString();
                    ((Label)reportHeaderSection.Controls["DepositCount"]).Text = depositTransactions.Count().ToString();
                    ((TextBox)reportHeaderSection.Controls["Withdrawals"]).Text = withdrawalTotal.FormatAsCurrency();
                    ((TextBox)reportHeaderSection.Controls["Deposits"]).Text = depositTotal.FormatAsCurrency();
                }
                else if (investmentAccount.AccountType == Constants.DataConstants.LoanAccountType)
                {
                    ((Label)reportHeaderSection.Controls["DepositsTitle"]).Text = "Loans";
                    ((Label)reportHeaderSection.Controls["WithdrawalsTitle"]).Text = "Loan Payments";
                    interestTotal = transactions.Where(x => x.TransactionCode.Code == Constants.DataConstants.LoanInterestCode).Sum(x => Math.Abs(x.Amount));
                    endingBalance = startingBalance + depositTotal - withdrawalTotal - interestTotal;
                    if (investmentAccount.Rate is not null)
                    {
                        annualRate = investmentAccount.Rate.Value / 100;
                    }
                    else if (_monthEndProcess is not null)
                    {
                        annualRate = _monthEndProcess.LoanRate;
                    }

                    ((Label)reportHeaderSection.Controls["DepositCount"]).Text = withdrawalTransactions.Count().ToString();
                    ((Label)reportHeaderSection.Controls["WithdrawalCount"]).Text = depositTransactions.Count().ToString();
                    ((TextBox)reportHeaderSection.Controls["Deposits"]).Text = withdrawalTotal.FormatAsCurrency();
                    ((TextBox)reportHeaderSection.Controls["Withdrawals"]).Text = depositTotal.FormatAsCurrency();

                    ((Label)reportHeaderSection.Controls["EffectiveInterestRateTitle"]).Text = "";
                    ((TextBox)reportHeaderSection.Controls["EffectiveInterestRate"]).Text = "";
                }
            }

            
            ((TextBox)reportHeaderSection.Controls["BeginningBalance"]).Text = Math.Abs(startingBalance).FormatAsCurrency();
            ((TextBox)reportHeaderSection.Controls["Interest"]).Text = interestTotal.FormatAsCurrency();
            ((TextBox)reportHeaderSection.Controls["NewBalance"]).Text = Math.Abs(endingBalance).FormatAsCurrency();
            ((TextBox)reportHeaderSection.Controls["AnnualInterestRate"]).Text = annualRate.FormatAsPercentage();
        }

        private static void SetClientHeaderFields(ClientAccountReport accountReport, Section pageHeaderSection)
        {
            ((Label)pageHeaderSection.Controls["AccountNumber"]).Text = accountReport.AccountNumber;
            ((Label)pageHeaderSection.Controls["AccountName"]).Text = accountReport.AccountName;
            ((Label)pageHeaderSection.Controls["ClientCompanyName"]).Text = accountReport.ClientName;

            string[] clientAddressParts = [accountReport.ClientAddress ?? string.Empty, accountReport.ClientApartment ?? string.Empty];
            string[] filteredClientAddressParts = clientAddressParts.Where(s => !string.IsNullOrEmpty(s)).ToArray();
            string combinedClientAddress = string.Join(", ", filteredClientAddressParts);
            ((Label)pageHeaderSection.Controls["ClientAddress"]).Text = combinedClientAddress;

            ((TextBox)pageHeaderSection.Controls["ClientCityStateZip"]).Text = $"{accountReport.ClientCity}{(string.IsNullOrWhiteSpace(accountReport.ClientCity) ? "" : ", ")}{accountReport.ClientState} {accountReport.ClientZip}";
        }

        private void SetCompanyHeaderFields(Section pageHeaderSection)
        {
            if (_companyReport is not null)
            {
                ((Label)pageHeaderSection.Controls["MgmtCompanyName"]).Text = _companyReport.Name;
                ((Label)pageHeaderSection.Controls["MgmtAddress"]).Text = _companyReport.Address;
                ((Label)pageHeaderSection.Controls["MgmtCityStateZip"]).Text = _companyReport.CityStateZip;
                ((TextBox)pageHeaderSection.Controls["MgmtEmail"]).Text = _companyReport.Email;
                ((TextBox)pageHeaderSection.Controls["MgmtPhone"]).Text = _companyReport.OfficePhone;
                ((Label)pageHeaderSection.Controls["StartDate"]).Text = _startDate?.ToString(Constants.DataConstants.DateFormat);
                ((Label)pageHeaderSection.Controls["EndDate"]).Text = _endDate?.AddHours(-1).ToString(Constants.DataConstants.DateFormat);
                ((Label)pageHeaderSection.Controls["PrintedDate"]).Text = DateTime.UtcNow.ToString(Constants.DataConstants.DateFormat);
            }
        }

        private decimal CalculateEffectiveRate(decimal rate, int compoundingPeriods)
        {
            return (decimal)(Math.Pow((double)(1 + (rate) / compoundingPeriods), compoundingPeriods) - 1);
        }
    }
}
