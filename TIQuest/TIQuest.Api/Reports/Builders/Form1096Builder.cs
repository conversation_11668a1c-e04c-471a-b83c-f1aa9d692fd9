﻿using GrapeCity.ActiveReports;
using GrapeCity.ActiveReports.SectionReportModel;
using TIQuest.Api.Enums;
using TIQuest.Api.Extensions;
using TIQuest.Api.Interfaces.DataProviders;
using TIQuest.Api.Poco;
using TIQuest.Api.Poco.Reports;
using TIQuest.Api.Reports.Filters;

namespace TIQuest.Api.Reports.Builders
{
    internal class Form1096ReportBuilder : ReportBuilderBase
    {
        private readonly IForm1096DataProvider _dataProvider;
        private IEnumerable<Form1096Report> _form1096Reports = [];

        public Form1096ReportBuilder(IForm1096DataProvider dataProvider) : base("Form1096.rpx")
        {
            _dataProvider = dataProvider;
            HasSection(ReportSection.Detail);
        }

        public override async Task PreloadDataAsync<TFilter>(TFilter filter, CancellationToken cancellationToken = default)
        {
            if (filter is not Form1096Filter form1096Filter)
            {
                throw new ArgumentException("Invalid filter type", nameof(filter));
            }

            _form1096Reports = await _dataProvider.GetReportDataAsync(form1096Filter, cancellationToken);
        }

        protected override Task<IEnumerable<dynamic>?> LoadReportDetailDataAsync<TFilter>(TFilter filter, CancellationToken cancellationToken = default)
        {
            return Task.FromResult<IEnumerable<dynamic>?>(_form1096Reports);
        }
    }
}
