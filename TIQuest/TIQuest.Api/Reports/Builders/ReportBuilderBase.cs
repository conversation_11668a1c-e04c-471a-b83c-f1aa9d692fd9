﻿using GrapeCity.ActiveReports;
using System.Xml;
using TIQuest.Api.Enums;
using TIQuest.Api.Interfaces.Reports;

namespace TIQuest.Api.Reports.Builders
{
    internal abstract class ReportBuilderBase : IReportBuilder
    {
        protected readonly string ReportsBasePath = Path.Combine("Reports", "Layouts");
        protected virtual string ReportPath { get; }
        private SectionReport _mainLayout { get; }
        protected ReportSection ReportSections { get; private set; }

        protected ReportBuilderBase(string reportName)
        {
            ArgumentException.ThrowIfNullOrEmpty(reportName);

            ReportPath = Path.Combine(ReportsBasePath, reportName);

            _mainLayout = LoadReportLayout(ReportPath);
        }

        protected void HasSection(ReportSection section)
        {
            ReportSections |= section;
        }

        /// <inheritdoc />
        public virtual async Task<SectionReport> BuildAsync<TFilter>(TFilter filter, CancellationToken cancellationToken = default)
        {
            ConfigureReportLayout(_mainLayout);
            if (ReportSections.HasFlag(ReportSection.ReportHeader))
            {
                await AssignReportHeaderFieldsAsync(_mainLayout, filter, cancellationToken);
            }
            if (ReportSections.HasFlag(ReportSection.PageHeader))
            {
                await AssignPageHeaderFieldsAsync(_mainLayout, filter, cancellationToken);
            }
            if (ReportSections.HasFlag(ReportSection.Detail))
            {
                await AssignReportDetailsAsync(_mainLayout, filter, cancellationToken);
            }
            if (ReportSections.HasFlag(ReportSection.ReportFooter))
            {
                await AssignReportFooterFieldsAsync(_mainLayout, filter, cancellationToken);
            }

            _mainLayout.Run(false);
            return _mainLayout;
        }

        protected virtual void ConfigureReportLayout(SectionReport mainLayout)
        {
            // intentionally left blank
        }

        protected virtual async Task AssignReportDetailsAsync<TFilter>(SectionReport mainLayout, TFilter? filter, CancellationToken cancellationToken)
        {
            var details = await LoadReportDetailDataAsync(filter, cancellationToken);
            if (details is not null)
            {
                _mainLayout.DataSource = details.ToList();
            }
        }

        protected virtual Task AssignReportHeaderFieldsAsync<TFilter>(SectionReport mainLayout, TFilter? filter, CancellationToken cancellationToken = default)
        {
            return Task.FromException(new NotImplementedException());
        }

        protected virtual Task AssignPageHeaderFieldsAsync<TFilter>(SectionReport mainLayout, TFilter? filter, CancellationToken cancellationToken = default)
        {
            return Task.FromException(new NotImplementedException());
        }

        protected virtual Task AssignReportFooterFieldsAsync<TFilter>(SectionReport mainLayout, TFilter? filter, CancellationToken cancellationToken = default)
        {
            return Task.FromException(new NotImplementedException());
        }

        public virtual Task PreloadDataAsync<TFilter>(TFilter filter, CancellationToken cancellationToken = default)
        {
            return Task.CompletedTask;
        }

        protected SectionReport LoadReportLayout(string reportPath)
        {
            if (!File.Exists(reportPath))
            {
                throw new FileNotFoundException($"Report {reportPath} was not found");
            }
            var sectionReport = new SectionReport();
            using (XmlTextReader xtr = new(Path.Combine(reportPath)))
            {
                sectionReport.LoadLayout(xtr);
            }
            return sectionReport;
        }

        protected virtual Task<IEnumerable<dynamic>?> LoadReportDetailDataAsync<TFilter>(TFilter filter, CancellationToken cancellationToken = default)
        {
            return Task.FromException<IEnumerable<dynamic>?>(new NotImplementedException());
        }
    }
}
