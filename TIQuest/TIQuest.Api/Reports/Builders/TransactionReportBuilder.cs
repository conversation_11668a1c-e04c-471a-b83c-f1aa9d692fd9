﻿using GrapeCity.ActiveReports;
using GrapeCity.ActiveReports.SectionReportModel;
using TIQuest.Api.Enums;
using TIQuest.Api.Extensions;
using TIQuest.Api.Interfaces.DataProviders;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Reports.Filters;

namespace TIQuest.Api.Reports.Builders
{
    internal class TransactionReportBuilder : ReportBuilderBase
    {
        private readonly ITransactionDataProvider _dataProvider;
        private readonly ICompanyReportService _companyReportService;

        public TransactionReportBuilder(ITransactionDataProvider transactionDataProvider, ICompanyReportService companyReportService) : base("ReportAllTransactions.rpx")
        {
            _dataProvider = transactionDataProvider;
            _companyReportService = companyReportService;

            HasSection(ReportSection.PageHeader);
            HasSection(ReportSection.Detail);
        }

        protected override async Task AssignPageHeaderFieldsAsync<TFilter>(SectionReport mainLayout, TFilter? filter, CancellationToken cancellationToken = default) where TFilter : default
        {
            if (filter is not TransactionFilter transactionFilter)
            {
                throw new ArgumentException("Invalid filter type", nameof(filter));
            }
            var companyReport = await _companyReportService.GetCompanyReportDataAsync(cancellationToken);

            var pageHeaderSection = mainLayout.Sections["PageHeader"];

            ((Label)pageHeaderSection.Controls["MgmtCompanyName"]).Text = companyReport.Name;
            ((Label)pageHeaderSection.Controls["MgmtAddress"]).Text = companyReport.Address;
            ((Label)pageHeaderSection.Controls["MgmtCityStateZip"]).Text = companyReport.CityStateZip;
            ((TextBox)pageHeaderSection.Controls["MgmtEmail"]).Text = companyReport.Email;
            ((TextBox)pageHeaderSection.Controls["MgmtPhone"]).Text = companyReport.OfficePhone;
            ((Label)pageHeaderSection.Controls["StartDate"]).Text = transactionFilter.StartDate.ToString(Constants.DataConstants.DateFormat);
            ((Label)pageHeaderSection.Controls["EndDate"]).Text = transactionFilter.EndDate.ToString(Constants.DataConstants.DateFormat);
        }

        protected override async Task<IEnumerable<dynamic>?> LoadReportDetailDataAsync<TFilter>(TFilter filter, CancellationToken cancellationToken = default)
        {
            if (filter is not TransactionFilter transactionFilter)
            {
                throw new ArgumentException("Invalid filter type", nameof(filter));
            }

            var records = await _dataProvider.GetTransactionWithBalancesAsync(transactionFilter.UserId,
                                                                    transactionFilter.StartDate.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc),
                                                                    transactionFilter.EndDate.ToDateTime(TimeOnly.MaxValue, DateTimeKind.Utc),
                                                                    true, cancellationToken);

            return records
                    .OrderBy(x => x.Date)
                    .ThenBy(x => x.CreatedDate)
                    .Select(x => new
                    {
                        ClientName = x.ClientName,
                        AccountName = x.AccountName,
                        AccountNumber = x.AccountNumber,
                        Date = x.Date.ToString(Constants.DataConstants.DateFormat),
                        Amount = x.Amount.FormatAsCurrency(),
                        Status = x.Status.ToUpper()[0],
                        Description = x.Description,
                        Balance = x.Balance.FormatAsCurrency()
                    });
        }
    }
}
