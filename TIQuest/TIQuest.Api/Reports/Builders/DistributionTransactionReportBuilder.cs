using GrapeCity.ActiveReports;
using GrapeCity.ActiveReports.SectionReportModel;
using TIQuest.Api.Enums;
using TIQuest.Api.Extensions;
using TIQuest.Api.Interfaces.DataProviders;
using TIQuest.Api.Poco.Reports;
using TIQuest.Api.Reports.Filters;

namespace TIQuest.Api.Reports.Builders
{
    internal class DistributionTransactionReportBuilder : ReportBuilderBase
    {
        private readonly IDistributionTransactionDataProvider _dataProvider;
        private DistributionTransactionReport? _reportData = null;

        public DistributionTransactionReportBuilder(IDistributionTransactionDataProvider dataProvider) 
            : base("DistributionTransactionReceipt.rpx")
        {
            _dataProvider = dataProvider;
            HasSection(ReportSection.PageHeader);
            HasSection(ReportSection.Detail);
        }

        public override async Task PreloadDataAsync<TFilter>(TFilter filter, CancellationToken cancellationToken = default)
        {
            if (filter is not DistributionTransactionFilter distributionFilter)
            {
                throw new ArgumentException("Invalid filter type", nameof(filter));
            }

            _reportData = await _dataProvider.GetDistributionReportDataAsync(distributionFilter, cancellationToken);
        }

        protected override Task AssignPageHeaderFieldsAsync<TFilter>(SectionReport mainLayout, TFilter? filter, CancellationToken cancellationToken = default) where TFilter : default
        {
            if (_reportData == null)
            {
                throw new InvalidOperationException("Report data must be loaded before assigning fields.");
            }

            var pageHeaderSection = mainLayout.Sections["PageHeader"];

            // Company information
            ((TextBox)pageHeaderSection.Controls["MgmtCompanyName"]).Text = _reportData.CompanyName;
            ((TextBox)pageHeaderSection.Controls["MgmtAddress1"]).Text = _reportData.CompanyAddress;
            ((TextBox)pageHeaderSection.Controls["MgmtCityStateZip"]).Text = _reportData.CompanyCityStateZip;
            ((TextBox)pageHeaderSection.Controls["MgmtEmail"]).Text = _reportData.CompanyEmail;
            ((TextBox)pageHeaderSection.Controls["MgmtPhone"]).Text = _reportData.CompanyPhone;

            // Client information will be bound to transaction data - remove static assignment
            // ((TextBox)pageHeaderSection.Controls["ClientCompanyName"]).Text = _reportData.PartnershipName;
            // AccountID, AccountName, ReceiptNumber and AsOfDate will be bound to transaction data - remove static assignment
            // ((TextBox)pageHeaderSection.Controls["AccountID"]).Text = _reportData.DistributionId.ToString();
            // ((TextBox)pageHeaderSection.Controls["AccountName"]).Text = "Investment";
            // ((TextBox)pageHeaderSection.Controls["ReceiptNumber"]).Text = _reportData.ReceiptNumber;
            // ((TextBox)pageHeaderSection.Controls["AsOfDate"]).Text = _reportData.AsOfDate.ToString(Constants.DataConstants.DateFormat);
            // Client address fields will be bound to transaction data - remove static assignment  
            // ((TextBox)pageHeaderSection.Controls["ClientAddress1"]).Text = _reportData.ClientAddress1;
            // ((TextBox)pageHeaderSection.Controls["ClientAddress2"]).Text = _reportData.ClientAddress2;
            // ((TextBox)pageHeaderSection.Controls["ClientCityStateZip"]).Text = _reportData.ClientCityStateZip;
            // ((TextBox)pageHeaderSection.Controls["ClientCountry"]).Text = _reportData.ClientCountry;

            return Task.CompletedTask;
        }

        protected override Task<IEnumerable<dynamic>?> LoadReportDetailDataAsync<TFilter>(TFilter filter, CancellationToken cancellationToken = default)
        {
            if (_reportData == null)
            {
                throw new InvalidOperationException("Report data must be loaded before loading detail data.");
            }

            var detailData = _reportData.Transactions.Select(t => new
            {
                TranDate = t.Date.ToString(Constants.DataConstants.DateFormat),
                TransDescription = t.Description,
                TransAmount = t.Amount,
                // Include transaction-specific account information
                AccountNumber = t.AccountNumber,
                AccountName = t.AccountName,
                // Include transaction ID for receipt number
                TransactionId = t.Id.ToString(),
                // Include investor information for client details
                ClientCompanyName = t.InvestorCompanyName,
                ClientAddress1 = t.InvestorAddress1,
                ClientCityStateZip = t.InvestorCityStateZip,
                ClientCountry = t.InvestorCountry,
                ClientAddress2 = t.InvestorAddress2,
                // Include transaction-specific AsOfDate
                AsOfDate = t.TransactionAsOfDate
            }).ToList();

            return Task.FromResult<IEnumerable<dynamic>?>(detailData);
        }

        // Override ConfigureReportLayout to try to set page breaks and bind account info
        protected override void ConfigureReportLayout(SectionReport mainLayout)
        {
            // Configure the detail section for page breaks if possible
            var detailSection = mainLayout.Sections["Detail"];
            if (detailSection != null)
            {
                // Try different approaches to set page breaks
                try
                {
                    // Method 1: Try to set page break via reflection
                    var newPageProperty = detailSection.GetType().GetProperty("NewPage");
                    if (newPageProperty != null && newPageProperty.CanWrite)
                    {
                        // Look for the enum value "After" in the property type
                        var enumType = newPageProperty.PropertyType;
                        if (enumType.IsEnum)
                        {
                            var afterValue = Enum.Parse(enumType, "After");
                            newPageProperty.SetValue(detailSection, afterValue);
                        }
                    }
                    else
                    {
                        // Method 2: Try ForceNewPage property
                        var forceNewPageProperty = detailSection.GetType().GetProperty("ForceNewPage");
                        if (forceNewPageProperty != null && forceNewPageProperty.CanWrite)
                        {
                            var enumType = forceNewPageProperty.PropertyType;
                            if (enumType.IsEnum)
                            {
                                var afterValue = Enum.Parse(enumType, "After");
                                forceNewPageProperty.SetValue(detailSection, afterValue);
                            }
                        }
                    }
                }
                catch (Exception)
                {
                    // If setting page breaks fails, continue without them
                    // The report will still show all transactions, just not on separate pages
                }
            }

            // Bind AccountID, AccountName, ReceiptNumber, AsOfDate and Client fields to transaction data
            var pageHeaderSection = mainLayout.Sections["PageHeader"];
            if (pageHeaderSection != null)
            {
                try
                {
                    // Bind AccountID to AccountNumber field from detail data
                    var accountIdControl = pageHeaderSection.Controls["AccountID"] as TextBox;
                    if (accountIdControl != null)
                    {
                        accountIdControl.DataField = "AccountNumber";
                    }

                    // Bind AccountName to AccountName field from detail data  
                    var accountNameControl = pageHeaderSection.Controls["AccountName"] as TextBox;
                    if (accountNameControl != null)
                    {
                        accountNameControl.DataField = "AccountName";
                    }

                    // Bind ReceiptNumber to TransactionId field from detail data
                    var receiptNumberControl = pageHeaderSection.Controls["ReceiptNumber"] as TextBox;
                    if (receiptNumberControl != null)
                    {
                        receiptNumberControl.DataField = "TransactionId";
                    }

                    // Bind AsOfDate to transaction-specific AsOfDate field from detail data
                    var asOfDateControl = pageHeaderSection.Controls["AsOfDate"] as TextBox;
                    if (asOfDateControl != null)
                    {
                        asOfDateControl.DataField = "AsOfDate";
                    }

                    // Bind client information to investor data from each transaction
                    var clientCompanyNameControl = pageHeaderSection.Controls["ClientCompanyName"] as TextBox;
                    if (clientCompanyNameControl != null)
                    {
                        clientCompanyNameControl.DataField = "ClientCompanyName";
                    }

                    var clientAddress1Control = pageHeaderSection.Controls["ClientAddress1"] as TextBox;
                    if (clientAddress1Control != null)
                    {
                        clientAddress1Control.DataField = "ClientAddress1";
                    }

                    var clientAddress2Control = pageHeaderSection.Controls["ClientAddress2"] as TextBox;
                    if (clientAddress2Control != null)
                    {
                        clientAddress2Control.DataField = "ClientAddress2";
                    }

                    var clientCityStateZipControl = pageHeaderSection.Controls["ClientCityStateZip"] as TextBox;
                    if (clientCityStateZipControl != null)
                    {
                        clientCityStateZipControl.DataField = "ClientCityStateZip";
                    }

                    var clientCountryControl = pageHeaderSection.Controls["ClientCountry"] as TextBox;
                    if (clientCountryControl != null)
                    {
                        clientCountryControl.DataField = "ClientCountry";
                    }
                }
                catch (Exception)
                {
                    // If binding fails, the controls will remain empty
                }
            }
        }
    }
}
