﻿using GrapeCity.ActiveReports;
using GrapeCity.ActiveReports.SectionReportModel;
using System.Globalization;
using TIQuest.Api.Enums;
using TIQuest.Api.Extensions;
using TIQuest.Api.Interfaces.DataProviders;
using TIQuest.Api.Poco;
using TIQuest.Api.Reports.Filters;

namespace TIQuest.Api.Reports.Builders
{
    internal class AccountStatementBuilder : ReportBuilderBase
    {
        private readonly IAccountStatementDataProvider _dataProvider;
        private IEnumerable<TransactionStatementRecord> _transactionRecords = new List<TransactionStatementRecord>();

        public AccountStatementBuilder(IAccountStatementDataProvider dataProvider) : base("ReportAccountStatement.rpx")
        {
            _dataProvider = dataProvider;

            HasSection(ReportSection.PageHeader);
            HasSection(ReportSection.Detail);
            HasSection(ReportSection.ReportFooter);
        }

        public override async Task PreloadDataAsync<TFilter>(TFilter filter, CancellationToken cancellationToken = default)
        {
            if (filter is not StatementFilter loanStatementFilter)
            {
                throw new ArgumentException("Invalid filter type", nameof(filter));
            }

            _transactionRecords = await _dataProvider.GetTransactionStatementsAsync(loanStatementFilter, cancellationToken);
        }

        protected override Task<IEnumerable<dynamic>?> LoadReportDetailDataAsync<TFilter>(TFilter filter, CancellationToken cancellationToken = default)
        {
            return Task.FromResult<IEnumerable<dynamic>?>(_transactionRecords.OrderBy(x => x.InvestorName));
        }

        protected override async Task AssignPageHeaderFieldsAsync<TFilter>(SectionReport mainLayout, TFilter? filter, CancellationToken cancellationToken) where TFilter : default
        {
            if (filter is not StatementFilter accountStatementFilter)
            {
                throw new ArgumentException("Invalid filter type", nameof(filter));
            }
            var companyReport = await _dataProvider.GetCompanyReportAsync(cancellationToken);

            string title = accountStatementFilter.AccountType == Constants.DataConstants.LoanAccountType
                            ? "Loan Statement Summary"
                            : "Investment Statement Summary";

            var pageHeaderSection = mainLayout.Sections["PageHeader"];

            ((Label)pageHeaderSection.Controls["MgmtCompanyName"]).Text = companyReport.Name;
            ((Label)pageHeaderSection.Controls["MgmtAddress"]).Text = companyReport.Address;
            ((Label)pageHeaderSection.Controls["MgmtCityStateZip"]).Text = companyReport.CityStateZip;
            ((TextBox)pageHeaderSection.Controls["MgmtEmail"]).Text = companyReport.Email;
            ((TextBox)pageHeaderSection.Controls["MgmtPhone"]).Text = companyReport.OfficePhone;
            ((Label)pageHeaderSection.Controls["StartDate"]).Text = accountStatementFilter.StartDate.ToString(Constants.DataConstants.DateFormat);
            ((Label)pageHeaderSection.Controls["EndDate"]).Text = accountStatementFilter.EndDate.AddHours(-1).ToString(Constants.DataConstants.DateFormat);
            ((Label)pageHeaderSection.Controls["ReportTitle"]).Text = title;
            ((TextBox)pageHeaderSection.Controls["PrintedDate"]).Text = DateTime.UtcNow.ToString(Constants.DataConstants.DateFormat);
        }

        protected override Task AssignReportFooterFieldsAsync<TFilter>(SectionReport mainLayout, TFilter? filter, CancellationToken cancellationToken) where TFilter : default
        {
            if (filter is not StatementFilter accountStatementFilter)
            {
                throw new ArgumentException("Invalid filter type", nameof(filter));
            }

            var totalStartingBalance = _transactionRecords.Sum(x => x.StartingBalance ?? 0);
            var totalDebits = _transactionRecords.Sum(x => x.Debit ?? 0);
            var totalCredits = _transactionRecords.Sum(x => x.Credit ?? 0);
            var totalInterest = _transactionRecords.Sum(x => x.Interest ?? 0);
            var totalEndingBalance = _transactionRecords.Sum(x => x.EndingBalance ?? 0);

            var reportFooterSection = mainLayout.Sections["ReportFooter"];

         ((TextBox)reportFooterSection.Controls["InitialBalanceTotal"]).Text = totalStartingBalance.ToString("C", CultureInfo.GetCultureInfo("en-US"));
				((TextBox)reportFooterSection.Controls["DebitTotal"]).Text = totalDebits.ToString("C", CultureInfo.GetCultureInfo("en-US"));
			((TextBox)reportFooterSection.Controls["CreditTotal"]).Text = totalCredits.ToString("C", CultureInfo.GetCultureInfo("en-US"));
			((TextBox)reportFooterSection.Controls["InterestTotal"]).Text = totalInterest.ToString("C", CultureInfo.GetCultureInfo("en-US"));
			((TextBox)reportFooterSection.Controls["EndBalanceTotal"]).Text = totalEndingBalance.ToString("C", CultureInfo.GetCultureInfo("en-US"));

			return Task.CompletedTask;
        }
    }
}
