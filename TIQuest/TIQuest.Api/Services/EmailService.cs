﻿using MimeKit;
using System.Text.Json;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Entities.Models;

namespace TIQuest.Api.Services
{
    /// <summary>
    /// Represents a service for sending email messages.
    /// </summary>
    public class EmailService : IEmailService
    {
        private readonly IAzureStorageService _azureStorageService;

        /// <summary>
        /// Initializes a new instance of the <see cref="EmailService"/> class with the provided <see cref="IAzureStorageService"/> instance.
        /// </summary>
        /// <param name="azureStorageService">The instance of <see cref="IAzureStorageService"/> used for interactions with Azure Storage.</param>
        public EmailService(IAzureStorageService azureStorageService)
        {
            _azureStorageService = azureStorageService;
        }

        /// <summary>
        /// Sends an email asynchronously.
        /// </summary>
        /// <param name="from">The sender's email address.</param>
        /// <param name="to">A list of recipient email addresses.</param>
        /// <param name="cc">A list of CC (carbon copy) email addresses.</param>
        /// <param name="bcc">A list of BCC (blind carbon copy) email addresses.</param>
        /// <param name="subject">The subject of the email.</param>
        /// <param name="body">The body content of the email.</param>
        /// <param name="isHtml">Specifies whether the email body is in HTML format (default is false).</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<string> SendAsync(MailboxAddress from, IList<MailboxAddress> to, IList<MailboxAddress>? cc, IList<MailboxAddress>? bcc,
            string subject, string body, bool isHtml = false, IEnumerable<(string Name, Stream Content)>? attachments = null)
        {
            if (from == null || string.IsNullOrWhiteSpace(from.Address))
            {
                throw new ArgumentException("Sender address (from) cannot be null or empty.");
            }

            if (to == null || !to.Any())
            {
                throw new ArgumentException("Recipient addresses (to) cannot be null or empty.");
            }

            var emailMessage = new EmailMessage()
            {
                From = new EmailAddress() { Name = from.Name, Address = from.Address },
                To = to.Select(t => new EmailAddress() { Name = t.Name, Address = t.Address }).ToList(),
                Subject = subject,
                Body = body,
                SubType = isHtml ? "html" : "plain"
            };

            if (cc is not null && cc.Any())
            {
                emailMessage.Cc = cc.Select(e => new EmailAddress() { Name = e.Name, Address = e.Address }).ToList();
            }

            if (bcc is not null && bcc.Any())
            {
                emailMessage.Bcc = bcc.Select(e => new EmailAddress() { Name = e.Name, Address = e.Address }).ToList();
            }

            if (attachments is not null && attachments.Any())
            {
                emailMessage.Attachments = new List<EmailAttachment>();
                foreach (var attachment in attachments)
                {
                    using (MemoryStream memStream = new MemoryStream())
                    {
                        await attachment.Content.CopyToAsync(memStream);
                        var streamContent = memStream.ToArray();
                        emailMessage.Attachments.Add(new EmailAttachment() { Name = attachment.Name, Content = streamContent });
                    }
                }
            }

            var serializedMessage = JsonSerializer.Serialize(emailMessage, new JsonSerializerOptions { WriteIndented = true });

            return await _azureStorageService.EnqueueEmailMessageAsync(serializedMessage);
        }
    }
}
