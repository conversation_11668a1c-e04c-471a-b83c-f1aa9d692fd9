﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Services
{
    internal class RoleService : IRoleService
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;
        private readonly IMapper _mapper;

        public RoleService(IDbContextFactory<DataContext> dbContextFactory, IMapper mapper)
        {
            _contextFactory = dbContextFactory;
            _mapper = mapper;
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.UserRole?> FindByIdAsync(int id, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                UserRole? userRole = await ctx.Roles.FindAsync(new object?[] { id }, cancellationToken: cancellationToken);
                if (userRole is not null)
                {
                    return _mapper.Map<Poco.Entities.UserRole>(userRole);
                }
                return null;
            }
        }
    }
}
