﻿using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Poco.Transactions;

namespace TIQuest.Api.Services
{
    internal class TransactionOperationService : ITransactionOperationService
    {
        /// <inheritdoc/>
        public async Task<(Transaction First, Transaction Second, CheckRegister Check)> CreateCheckWithdrawalAsync(DataContext ctx, CheckTransaction transaction, CancellationToken cancellationToken = default)
        {
            var mainTransaction = await CreateNewTransactionAsync(ctx, transaction with { Amount = transaction.Amount * -1 }, cancellationToken);

            var cashTransaction = await CreateNewTransactionAsync(ctx, transaction with { Account = Constants.DataConstants.CashAccountId, Amount = transaction.Amount * -1 }, cancellationToken);
            cashTransaction.ParentTransaction = mainTransaction;

            var checkRegister = new CheckRegister
            {
                Date = mainTransaction.Date,
                Memo = transaction.Memo,
                Transaction = mainTransaction,
                Payee = transaction.Payee,
                Address = transaction.Address,
                Apartment = transaction.Apartment,
                City = transaction.City,
                State = transaction.State,
                Zip = transaction.Zip,
                Country = transaction.Country,
                IsPrinted = false
            };

            return (mainTransaction, cashTransaction, checkRegister);
        }

        /// <inheritdoc/>
        public async Task<(Transaction First, Transaction Second, AchRegister Ach)> CreateAchWithdrawalAsync(DataContext ctx, AchTransaction transaction, CancellationToken cancellationToken = default)
        {
            var mainTransaction = await CreateNewTransactionAsync(ctx, transaction with { Amount = transaction.Amount * -1 }, cancellationToken);

            var cashTransaction = await CreateNewTransactionAsync(ctx, transaction with { Account = Constants.DataConstants.CashAccountId, Amount = transaction.Amount * -1 }, cancellationToken);
            cashTransaction.ParentTransaction = mainTransaction;

            var achRegister = new AchRegister
            {
                Date = mainTransaction.Date,
                Description = transaction.AchDetails,
                Transaction = mainTransaction,
                IsPrinted = false
            };

            return (mainTransaction, cashTransaction, achRegister);
        }

        /// <inheritdoc/>
        public async Task<(Transaction First, Transaction Second)> CreateDepositAsync(DataContext ctx, DepositTransaction depositTransaction, CancellationToken cancellationToken = default)
        {
            Transaction mainTransaction = await CreateNewTransactionAsync(ctx, depositTransaction, cancellationToken);

            Transaction cashTransaction = await CreateNewTransactionAsync(ctx, depositTransaction with { Account = Constants.DataConstants.CashAccountId }, cancellationToken);
            cashTransaction.ParentTransaction = mainTransaction;

            return (mainTransaction, cashTransaction);
        }

        /// <inheritdoc/>
        public async Task<(Transaction First, Transaction Second)> CreateSystemCashWithdrawalAsync(DataContext ctx, DepositTransaction transaction, CancellationToken cancellationToken = default)
        {
            var mainTransaction = await CreateNewTransactionAsync(ctx, transaction with { Amount = transaction.Amount * -1 }, cancellationToken);

            var cashTransaction = await CreateNewTransactionAsync(ctx, transaction with { Account = Constants.DataConstants.CashAccountId, Amount = transaction.Amount * -1 }, cancellationToken);
            cashTransaction.ParentTransaction = mainTransaction;

            return (mainTransaction, cashTransaction);
        }

        /// <inheritdoc/>
        public async Task<(Transaction VoidedTransaction, Transaction VoidedChildTransaction)> VoidAsync(DataContext ctx, Transaction parentTransaction, Transaction childTransaction,
            int voidStatusId, int modifierId, CancellationToken cancellationToken = default)
        {
            var voidStatus = await ctx.TransactionStatuses.FindAsync([voidStatusId], cancellationToken: cancellationToken);

#pragma warning disable CS8604 // Possible null reference argument.
            MarkAsVoid(parentTransaction, voidStatus, modifierId);
#pragma warning restore CS8604 // Possible null reference argument.
            MarkAsVoid(childTransaction, voidStatus, modifierId);

            var voidedTransaction = await CreateNewTransactionAsync(ctx,
                                                new DepositTransaction
                                                {
                                                    Account = parentTransaction.AccountId,
                                                    Amount = parentTransaction.Amount * -1,
                                                    Date = parentTransaction.Date,
                                                    Description = parentTransaction.Description,
                                                    Status = voidStatusId,
                                                    Code = parentTransaction.CodeId,
                                                    ModifierId = modifierId
                                                }, cancellationToken);

            voidedTransaction.InvestmentAccount.Balance += voidedTransaction.Amount;

            var voidedChildTransaction = await CreateNewTransactionAsync(ctx,
                                                new DepositTransaction
                                                {
                                                    Account = childTransaction.AccountId,
                                                    Amount = parentTransaction.Amount * -1,
                                                    Date = parentTransaction.Date,
                                                    Description = parentTransaction.Description,
                                                    Status = voidStatusId,
                                                    Code = parentTransaction.CodeId,
                                                    ModifierId = modifierId
                                                }, cancellationToken);
            voidedChildTransaction.ParentTransaction = voidedTransaction;
            voidedChildTransaction.InvestmentAccount.Balance += voidedChildTransaction.Amount;

            return (voidedTransaction, voidedChildTransaction);
        }

        private void MarkAsVoid(Transaction transaction, TransactionStatus voidStatus, int modifierId)
        {
            transaction.TransactionStatus = voidStatus;
            transaction.LastModifiedBy = modifierId;
            transaction.LastModifiedOn = DateTime.UtcNow;
        }

        private async Task<Transaction> CreateNewTransactionAsync(DataContext ctx, BaseTransaction transaction, CancellationToken cancellationToken = default)
        {
            var account = await ctx.InvestmentAccounts.FindAsync([transaction.Account], cancellationToken: cancellationToken);
#pragma warning disable CS8601 // Possible null reference assignment.
            var newTransaction = new Transaction
            {
                InvestmentAccount = account,
                Amount = transaction.Amount,
                Date = transaction.Date,
                Description = transaction.Description,
                StatusId = transaction.Status,
                CodeId = transaction.Code,
                LastModifiedBy = transaction.ModifierId,
                CreatedBy = transaction.ModifierId,
                LastModifiedOn = DateTime.UtcNow,
                CreatedOn = DateTime.UtcNow,
                BankTransactionType = transaction.BankTransactionType
            };
#pragma warning restore CS8601 // Possible null reference assignment.

            return newTransaction;
        }
    }
}
