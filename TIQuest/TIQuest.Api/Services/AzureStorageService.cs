﻿using Azure.Storage.Blobs;
using Azure.Storage.Queues;
using Azure.Storage.Queues.Models;
using Microsoft.Extensions.Options;
using System.Text;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Options;

namespace TIQuest.Api.Services
{
    /// <summary>
    /// Represents a service for interacting with Azure Storage services.
    /// Implements the <see cref="IAzureStorageService"/> interface.
    /// </summary>
    public class AzureStorageService : IAzureStorageService
    {
        private readonly BlobServiceClient _blobServiceClient;
        private readonly QueueServiceClient _queueServiceClient;
        private readonly EmailSettings _emailConfiguration;

        /// <summary>
        /// Initializes a new instance of the <see cref="AzureStorageService"/> class with the provided BlobServiceClient and QueueServiceClient instances.
        /// </summary>
        /// <param name="blobServiceClient">The BlobServiceClient instance used for working with Azure Blob Storage.</param>
        /// <param name="queueServiceClient">The QueueServiceClient instance used for working with Azure Storage Queues.</param>
        public AzureStorageService(BlobServiceClient blobServiceClient, QueueServiceClient queueServiceClient, IOptions<EmailSettings> emailSettings)
        {
            _blobServiceClient = blobServiceClient;
            _queueServiceClient = queueServiceClient;
            _emailConfiguration = emailSettings.Value;
        }

        /// <summary>
        /// Asynchronously enqueues an email message for sending.
        /// </summary>
        /// <param name="message">The email message to enqueue for sending.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains a <see cref="SendReceipt"/> indicating the status of the sending process.</returns>
        public async Task<string> EnqueueEmailMessageAsync(string message)
        {
            ArgumentException.ThrowIfNullOrEmpty(message);

            BlobContainerClient blobContainerClient = _blobServiceClient.GetBlobContainerClient(_emailConfiguration.ContainerName);

            var base64EncodedBytes = Convert.ToBase64String(Encoding.UTF8.GetBytes(message));

            using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(base64EncodedBytes)))
            {
                var blobName = Guid.NewGuid().ToString();

                await blobContainerClient.UploadBlobAsync(blobName, stream);

                var queueClient = _queueServiceClient.GetQueueClient(_emailConfiguration.QueueName);
                await queueClient.SendMessageAsync(Convert.ToBase64String(Encoding.UTF8.GetBytes(blobName)));
                return blobName;
            }
        }
    }
}
