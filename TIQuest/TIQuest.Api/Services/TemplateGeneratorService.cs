﻿using Microsoft.Extensions.Options;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Options;
using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Services
{
    internal class TemplateGeneratorService : ITemplateGeneratorService
    {
        private readonly ApplicationConfig _applicationConfig;

        public TemplateGeneratorService(IOptions<ApplicationConfig> applicationConfig)
        {
            _applicationConfig = applicationConfig.Value;
        }

        /// <inheritdoc/>
        public string GetChangePasswordTemplate(User appUser, string token)
        {
            return GetPasswordHtml(appUser, token, Resources.ChangePassword);
        }

        /// <inheritdoc/>
        public string GetCreateUserTemplate(User appUser, string token)
        {
            return GetPasswordHtml(appUser, token, Resources.CreateNewUser);
        }

        /// <inheritdoc/>
        public string GetResetPasswordTemplate(User appUser, string token)
        {
            return GetPasswordHtml(appUser, token, Resources.ResetPassword);
        }

        private string GetPasswordHtml(User appUser, string token, string templateName)
        {
            string url = $"{_applicationConfig.DomainBaseUrl}/reset-password?accessToken={token}";

            string emailTemplate = string.Format(templateName, appUser.FirstName, url);

            return emailTemplate;
        }
    }
}
