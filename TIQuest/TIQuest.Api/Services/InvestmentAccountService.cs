﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using System.Text;
using TIQuest.Api.DTO.Request;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Services
{
    internal class InvestmentAccountService : IInvestmentAccountService
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;
        private readonly IMapper _mapper;

        public InvestmentAccountService(IDbContextFactory<DataContext> dbContextFactory, IMapper mapper)
        {
            _contextFactory = dbContextFactory;
            _mapper = mapper;
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.InvestmentAccount> CreateAsync(AccountRequest account, int modifierId, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                using (IDbContextTransaction transaction = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        var entity = _mapper.Map<InvestmentAccount>(account);

                        if (account.AccountType == Constants.DataConstants.LoanAccountType)
                        {
                            entity.Rate = account.Rate;
                            entity.InterestType = null;
                        }

                        // Figure out for account number
                        var accountNumbers = await ctx.InvestmentAccounts.Select(x => x.AccountNumber).ToListAsync(cancellationToken);
                        int lastAccountNumber = accountNumbers.Max(x => int.TryParse(x, out int result) ? result : 0);

                        entity.AccountNumber = $"{lastAccountNumber + 1}";
                        entity.CreatedBy = modifierId;
                        entity.CreatedOn = DateTime.UtcNow;
                        entity.LastModifiedBy = modifierId;
                        entity.LastModifiedOn = DateTime.UtcNow;

                        var entitySet = await ctx.InvestmentAccounts.AddAsync(entity);

                        await ctx.SaveChangesAsync(cancellationToken);

                        await AssignAccountPermissionsToUserAsync(ctx, entitySet.Entity.Id, cancellationToken);

                        await transaction.CommitAsync(cancellationToken);

                        return await FindByIdAsync(entitySet.Entity.Id);
                    }
                    catch (Exception)
                    {
                        await transaction.RollbackAsync(cancellationToken);
                        throw;
                    }
                }
            }
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.InvestmentAccount> UpdateAsync(EditAccountRequest account, int modifierId, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var entity = await ctx.InvestmentAccounts
                                        .Include(x => x.AccountInvestor)
                                        .FirstOrDefaultAsync(x => x.Id == account.Id, cancellationToken);
                if (entity is null)
                {
                    throw new InvalidOperationException($"Account with id {account.Id} does not exist.");
                }

                entity = _mapper.Map<AccountRequest, InvestmentAccount>(account, entity);

                if (account.AccountType == Constants.DataConstants.LoanAccountType)
                {
                    entity.Rate = account.Rate;
                    entity.InterestType = null;
                }

                entity.LastModifiedBy = modifierId;
                entity.LastModifiedOn = DateTime.UtcNow;

                ctx.InvestmentAccounts.Update(entity);

                await ctx.SaveChangesAsync(cancellationToken);

                return _mapper.Map<Poco.Entities.InvestmentAccount>(entity);
            }
        }

        private async Task AssignAccountPermissionsToUserAsync(DataContext ctx, int id, CancellationToken cancellationToken)
        {
            List<int> users = await (from permission in ctx.UserAccountPermissions
                                     join user in ctx.Users on permission.UserId equals user.Id
                                     join userRole in ctx.UserRoles on user.UserRoleId equals userRole.Id
                                     // If the user already has a permission for this account, don't add them again.
                                     where permission.InvestmentAccountId != id
                                     // only add users who are admins or auditors, as they can see all accounts
                                     // client role will need to be manually assigned anyway.
                                         && (userRole.Description == Constants.RoleConstants.AdminRole
                                           || userRole.Description == Constants.RoleConstants.AuditorRole)
                                     select user.Id)
                                .Distinct()
                                .ToListAsync(cancellationToken);

            if (users.Any())
            {
                StringBuilder sb = new StringBuilder();
                foreach (int user in users)
                {
                    sb.AppendLine($"INSERT INTO UserAccountPermission ([User], [Account]) VALUES({user}, {id})");
                }

                await ctx.Database.ExecuteSqlRawAsync(sb.ToString(), cancellationToken);
            }
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.InvestmentAccount?> FindByIdAsync(int id, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var account = await ctx.InvestmentAccounts
                                        .Include(x => x.AccountInvestor)
                                        .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
                if (account is null)
                {
                    return null;
                }
                return _mapper.Map<Poco.Entities.InvestmentAccount?>(account);
            }
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<Poco.Entities.InvestmentAccount>> GetAccountsByInvestorAsync(int investorId, int userId, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var accessibleAccounts = await ctx.UserAccountPermissions.Where(x => x.UserId == userId).Select(x => x.InvestmentAccountId).Distinct().ToListAsync(cancellationToken);
                if (accessibleAccounts is not null && accessibleAccounts.Any())
                {
                    IQueryable<Poco.Entities.InvestmentAccount> accountQueryable = GetAccountQueryable(ctx, accessibleAccounts).Where(x => x.Investor.Id == investorId);
                    var accounts = await accountQueryable.ToListAsync(cancellationToken);

                    return accounts;
                }
                return new List<Poco.Entities.InvestmentAccount>();
            }
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<Poco.Entities.InvestmentAccount>> GetInvestmentAccountsAsync(int userId, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var accounts = await ctx.UserAccountPermissions.Where(x => x.UserId == userId).Select(x => x.InvestmentAccountId).Distinct().ToListAsync(cancellationToken);
                if (accounts is not null && accounts.Any())
                {
                    return await GetAccountQueryable(ctx, accounts).ToListAsync(cancellationToken);
                }
                return new List<Poco.Entities.InvestmentAccount>();
            }
        }

        private IQueryable<Poco.Entities.InvestmentAccount> GetAccountQueryable(DataContext ctx, List<int> accounts)
        {
            return from account in ctx.InvestmentAccounts
                   where account.InvestorId != Constants.DataConstants.SystemInvestorId
                   && accounts.Contains(account.Id)
                   select new Poco.Entities.InvestmentAccount
                   {
                       Id = account.Id,
                       Investor = new Poco.Entities.Investor
                       {
                           Id = account.InvestorId,
                           CompanyName = account.AccountInvestor.CompanyName,
                           FirstName = account.AccountInvestor.FirstName,
                           LastName = account.AccountInvestor.LastName
                       },
                       Name = account.Name,
                       Email = account.Email,
                       AccountNumber = account.AccountNumber,
                       Rate = account.Rate,
                       Balance = account.Balance,
                       StartDate = account.StartDate,
                       EndDate = account.EndDate,
                       AccountType = account.AccountType,
                       InterestType = account.InterestType,
                       Report1099Name = account.Report1099Name,
                       SendEmail = account.SendEmail,
                       SendMail = account.SendMail,
                       TaxNumber = account.TaxNumber
                   };
        }
    }
}
