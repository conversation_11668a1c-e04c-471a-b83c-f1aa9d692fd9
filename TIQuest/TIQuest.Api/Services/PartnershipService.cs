﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TIQuest.Api.DTO.Request.Partnerships;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Services
{
    internal class PartnershipService : IPartnershipService
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;
        private readonly IMapper _mapper;

        public PartnershipService(IDbContextFactory<DataContext> dbContextFactory, IMapper mapper)
        {
            _contextFactory = dbContextFactory;
            _mapper = mapper;
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<Poco.Entities.Partnership>> GetAllAsync(CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var partnerships = await ctx.Partnerships.ToListAsync(cancellationToken);

                return _mapper.Map<IEnumerable<Poco.Entities.Partnership>>(partnerships);
            }
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.Partnership?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var partnership = await ctx.Partnerships.FindAsync(new object[] { id }, cancellationToken);

                if (partnership is null)
                {
                    return null;
                }

                return _mapper.Map<Poco.Entities.Partnership?>(partnership);
            }
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.Partnership> CreateAsync(PartnershipRequest request, int modifierId, CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                Partnership partnership = _mapper.Map<Partnership>(request);

                partnership.CreatedBy = modifierId;
                partnership.LastModifiedBy = modifierId;
                partnership.CreatedOn = DateTime.UtcNow;
                partnership.LastModifiedOn = DateTime.UtcNow;
                partnership.IsActive = true;

                var entity = await ctx.Partnerships.AddAsync(partnership, cancellationToken);
                await ctx.SaveChangesAsync(cancellationToken);

                return _mapper.Map<Poco.Entities.Partnership>(entity.Entity);
            }
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.Partnership> UpdateAsync(EditPartnershipRequest request, int modifierId, CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                Partnership? partnership = await ctx.Partnerships.FindAsync(new object[] { request.Id }, cancellationToken);

                if (partnership is null)
                {
                    throw new ArgumentException($"Partnership with id {request.Id} does not exist");
                }

                partnership = _mapper.Map(request, partnership);

                partnership.LastModifiedBy = modifierId;
                partnership.LastModifiedOn = DateTime.UtcNow;

                var entity = ctx.Partnerships.Update(partnership);
                await ctx.SaveChangesAsync(cancellationToken);

                return _mapper.Map<Poco.Entities.Partnership>(entity.Entity);
            }
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<Poco.Entities.PartnershipOwner>> GetPartnershipInvestorsAsync(int partnershipId, CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                return await
                    (from owner in ctx.PartnershipOwners
                     join account in ctx.InvestmentAccounts on owner.AccountId equals account.Id
                     join investor in ctx.Investors on account.InvestorId equals investor.Id
                     where owner.PartnershipId == partnershipId
                     select new Poco.Entities.PartnershipOwner
                     {
                         Id = owner.Id,
                         PartnershipId = owner.PartnershipId,
                         Percentage = owner.Percentage,
                         Account = new Poco.Entities.InvestmentAccount
                         {
                             Id = account.Id,
                             Name = account.Name,
                             StartDate = account.StartDate,
                             EndDate = account.EndDate,
                             AccountNumber = account.AccountNumber
                         },
                         Investor = new Poco.Entities.Investor
                         {
                             Id = investor.Id,
                             FirstName = investor.FirstName,
                             LastName = investor.LastName,
                             CompanyName = investor.CompanyName
                         }
                     }).ToListAsync(cancellationToken);
            }
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.PartnershipOwner?> GetPartnershipInvestorAsync(int id, CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                return await
                    (from owner in ctx.PartnershipOwners
                     join account in ctx.InvestmentAccounts on owner.AccountId equals account.Id
                     join investor in ctx.Investors on account.InvestorId equals investor.Id
                     where owner.Id == id
                     select new Poco.Entities.PartnershipOwner
                     {
                         Id = owner.Id,
                         PartnershipId = owner.PartnershipId,
                         Percentage = owner.Percentage,
                         Account = new Poco.Entities.InvestmentAccount
                         {
                             Id = account.Id,
                             Name = account.Name,
                         },
                         Investor = new Poco.Entities.Investor
                         {
                             Id = investor.Id,
                             FirstName = investor.FirstName,
                             LastName = investor.LastName,
                             CompanyName = investor.CompanyName
                         }
                     }).FirstOrDefaultAsync(cancellationToken);
            }
        }

        /// <inheritdoc/>
        public async Task AddInvestorAsync(AddPartnershipInvestorRequest request, int partnershipId, int modifierId, CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                PartnershipOwner partnershipOwner = _mapper.Map<PartnershipOwner>(request);

                partnershipOwner.PartnershipId = partnershipId;
                partnershipOwner.CreatedBy = modifierId;
                partnershipOwner.LastModifiedBy = modifierId;
                partnershipOwner.CreatedOn = DateTime.UtcNow;
                partnershipOwner.LastModifiedOn = DateTime.UtcNow;

                await ctx.PartnershipOwners.AddAsync(partnershipOwner, cancellationToken);
                await ctx.SaveChangesAsync(cancellationToken);
            }
        }

        /// <inheritdoc/>
        public async Task EditInvestorAsync(EditPartnershipInvestorRequest request, int modifierId, CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var partnershipOwner = await ctx.PartnershipOwners.FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
                if (partnershipOwner is null)
                {
                    throw new ArgumentException($"Could not find association with id {request.Account}");
                }

                partnershipOwner.AccountId = request.Account;
                partnershipOwner.Percentage = request.Percentage;
                partnershipOwner.LastModifiedBy = modifierId;
                partnershipOwner.LastModifiedOn = DateTime.UtcNow;

                ctx.PartnershipOwners.Update(partnershipOwner);
                await ctx.SaveChangesAsync(cancellationToken);
            }
        }

        /// <inheritdoc/>
        public async Task RemoveInvestorAsync(int partnershipId, int accountId, CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                PartnershipOwner? partnershipOwner = await ctx.PartnershipOwners.FirstOrDefaultAsync(x => x.PartnershipId == partnershipId && x.AccountId == accountId, cancellationToken);
                if (partnershipOwner is null)
                {
                    throw new ArgumentException($"Could not find account {accountId} associated to partnership {partnershipId}");
                }
                ctx.PartnershipOwners.Remove(partnershipOwner);
                await ctx.SaveChangesAsync(cancellationToken);
            }
        }
    }
}
