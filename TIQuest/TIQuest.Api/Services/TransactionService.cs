﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TIQuest.Api.DTO.Request.Transactions;
using TIQuest.Api.Entities;
using TIQuest.Api.Enums;
using TIQuest.Api.Interfaces.DataProviders;
using TIQuest.Api.Interfaces.Factories;
using TIQuest.Api.Interfaces.Managers;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Services
{
	internal class TransactionService : ITransactionService
	{
		private readonly IDbContextFactory<DataContext> _contextFactory;
		private readonly ITransactionManagerFactory _transactionManagerFactory;
		private readonly ITransactionDataProvider _transactionDataProvider;
		private readonly IMapper _mapper;
		private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

		private readonly int StartOfMonth = 1;

		public TransactionService(
			 IDbContextFactory<DataContext> dbContextFactory,
			 ITransactionManagerFactory transactionManagerFactory,
			 ITransactionDataProvider transactionDataProvider,
			 IMapper mapper
		)
		{
			_contextFactory = dbContextFactory;
			_transactionManagerFactory = transactionManagerFactory;
			_transactionDataProvider = transactionDataProvider;
			_mapper = mapper;
		}

		/// <inheritdoc/>
		public async Task<int> RecordTransactionAsync(
			 TransactionRequest request,
			 int modifierId,
			 TransactionType transactionType,
			 CancellationToken cancellationToken = default
		)
		{
			ITransactionManager transactionManager =
				 _transactionManagerFactory.GetTransactionManager(transactionType);
			// lock transaction creation to avoid dirty read/writes of account balance
			await _semaphore.WaitAsync(cancellationToken);

			int transactionId = 0;
			try
			{
				transactionId = await transactionManager.CreateTransactionAsync(
					 request,
					 modifierId,
					 cancellationToken
				);
			}
			finally
			{
				_semaphore.Release();
			}

			return transactionId;
		}

		/// <inheritdoc/>
		public async Task<IEnumerable<Poco.TransactionAccountRecord>> GetUserTransactionsAsync(
			 int userId,
			 bool isClient,
			 DateOnly startDate,
			 DateOnly endDate,
			 string? excludeTransactionCodes,
			 CancellationToken cancellationToken = default
		)
		{
			using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
			{
				var transactions = await _transactionDataProvider.GetTransactionsAsync(
					 userId,
					 startDate.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc),
					 endDate.ToDateTime(TimeOnly.MaxValue, DateTimeKind.Utc),
					 !isClient,
					 excludeTransactionCodes,
					 cancellationToken
				);

				var transactionIds = transactions.Select(x => x.Id).Distinct().ToList();

				var checkTransactions = await ctx
					 .CheckRegisters.Where(x => transactionIds.Contains(x.TransactionId))
					 .ToListAsync(cancellationToken);

				var distinctAccounts = transactions.Select(x => x.Account.Id).Distinct().ToList();
				var previousAccountBalances = await ctx
					 .MonthEndBalances.Where(x =>
						  distinctAccounts.Contains(x.InvestmentAccount.Id)
						  && x.MonthEndProcess.Month == startDate.AddMonths(-1).Month
						  && x.MonthEndProcess.Year == startDate.AddMonths(-1).Year
					 )
					 .ToListAsync(cancellationToken);

				var transactionRecords = new List<Poco.TransactionAccountRecord>();
				foreach (var transaction in transactions)
				{
					var checkTransaction = checkTransactions.FirstOrDefault(x =>
						 x.TransactionId == transaction.Id
					);
					if (checkTransaction is not null)
					{
						transaction.CheckRegister = new Poco.Entities.CheckRegister
						{
							Number = checkTransaction.Number,
						};
					}

					var existingRecord = transactionRecords.FirstOrDefault(x =>
						 x.Id == transaction.Account.Id
					);
					if (existingRecord is null)
					{
						var balance = previousAccountBalances.FirstOrDefault(x =>
							 x.AccountId == transaction.Account.Id
						);

						if (balance is not null)
						{
							//TI - 2177 Balance changes based on custom date selected
							decimal balanceOffset = 0m;
							if (startDate.Day != StartOfMonth)
							{
								var retroactiveStartDate = new DateOnly(
									 startDate.Year,
									 startDate.Month,
									 StartOfMonth
								);
								var retroactiveEndDate = new DateOnly(
									 startDate.Year,
									 startDate.Month,
									 startDate.Day - 1
								);
								var retroactiveTransactions =
									 await _transactionDataProvider.GetTransactionsAsync(
										  userId,
										  retroactiveStartDate.ToDateTime(
												TimeOnly.MinValue,
												DateTimeKind.Utc
										  ),
										  retroactiveEndDate.ToDateTime(
												TimeOnly.MaxValue,
												DateTimeKind.Utc
										  ),
										  !isClient,
										  excludeTransactionCodes,
										  cancellationToken
									 );
								var retroactiveTransactionsForAccount = retroactiveTransactions
									 .Where(t => t.Account.Id == transaction.Account.Id)
									 .ToList();
								balanceOffset = retroactiveTransactionsForAccount.Sum(t =>
									 t.Amount
								);
							}
							transaction.Account.Balance = balance.EndingBalance + balanceOffset;
						}
						else
						{
							//TI-2127 [Bug] Balance Mismatch on the Transaction History Dashboard and Client View Mode
							transaction.Account.Balance = 0;
						}

						// create
						var newRecord = new Poco.TransactionAccountRecord
						{
							Id = transaction.Account.Id,
							AccountNumber = transaction.Account.AccountNumber,
							Name = transaction.Account.Name,
							Balance = transaction.Account.Balance,
							Investor = transaction.Account.Investor,
							Transactions = new List<Poco.Entities.Transaction> { transaction },
						};
						transactionRecords.Add(newRecord);
					}
					else
					{
						// update
						existingRecord.Transactions.Add(transaction);
					}
				}

				return transactionRecords;
			}
		}

		/// <inheritdoc/>
		public async Task<IEnumerable<Poco.TransactionAccountRecord>> GetDistributionTransactionsAsync(
			 int userId,
			 bool isClient,
			 int distributionId,
			 DateOnly? startDate,
			 DateOnly? endDate,
			 string? excludeTransactionCodes,
			 CancellationToken cancellationToken = default
		)
		{
			using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
			{
				// Get distribution transactions excluding cash account (ID = 1)
				var query = ctx.DistributionTransactions
					.Include(dt => dt.Transaction)
						.ThenInclude(t => t.InvestmentAccount)
							.ThenInclude(a => a.AccountInvestor)
					.Include(dt => dt.Transaction)
						.ThenInclude(t => t.TransactionCode)
					.Include(dt => dt.Transaction)
						.ThenInclude(t => t.TransactionStatus)
					.Where(dt => dt.DistributionId == distributionId 
						&& dt.Transaction.AccountId != Constants.DataConstants.CashAccountId);

				// Apply date filters only if provided
				if (startDate.HasValue)
				{
					query = query.Where(dt => dt.Transaction.Date >= startDate.Value.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc));
				}
				if (endDate.HasValue)
				{
					query = query.Where(dt => dt.Transaction.Date <= endDate.Value.ToDateTime(TimeOnly.MaxValue, DateTimeKind.Utc));
				}

				var distributionTransactions = await query.ToListAsync(cancellationToken);

				// Filter by user access if client
				if (isClient)
				{
					var userAccounts = await ctx.UserAccountPermissions
						.Where(x => x.UserId == userId)
						.Select(x => x.InvestmentAccountId)
						.ToListAsync(cancellationToken);

					distributionTransactions = distributionTransactions
						.Where(dt => userAccounts.Contains(dt.Transaction.AccountId))
						.ToList();
				}

				var transactions = distributionTransactions.Select(dt => dt.Transaction).ToList();

				// Apply transaction code exclusion filter
				if (!string.IsNullOrWhiteSpace(excludeTransactionCodes))
				{
					var transactionCodes = excludeTransactionCodes.Split(',');
					if (transactionCodes.Any())
					{
						transactions = transactions.Where(x => !transactionCodes.Contains(x.TransactionCode.Code)).ToList();
					}
				}

				// Get check register information
				var transactionIds = transactions.Select(x => x.Id).Distinct().ToList();
				var checkTransactions = await ctx
					.CheckRegisters.Where(x => transactionIds.Contains(x.TransactionId))
					.ToListAsync(cancellationToken);

				// Build transaction records
				var transactionRecords = new List<Poco.TransactionAccountRecord>();
				foreach (var transaction in transactions)
				{
					var checkTransaction = checkTransactions.FirstOrDefault(x => x.TransactionId == transaction.Id);

					// Convert entity to POCO
					var transactionPoco = new Poco.Entities.Transaction
					{
						Id = transaction.Id,
						Account = new Poco.Entities.InvestmentAccount
						{
							Id = transaction.InvestmentAccount.Id,
							Name = transaction.InvestmentAccount.Name,
							AccountNumber = transaction.InvestmentAccount.AccountNumber,
							Balance = transaction.InvestmentAccount.Balance,
							Investor = new Poco.Entities.Investor
							{
								Id = transaction.InvestmentAccount.AccountInvestor.Id,
								FirstName = transaction.InvestmentAccount.AccountInvestor.FirstName,
								LastName = transaction.InvestmentAccount.AccountInvestor.LastName,
								CompanyName = transaction.InvestmentAccount.AccountInvestor.CompanyName,
								Type = transaction.InvestmentAccount.AccountInvestor.Type
							}
						},
						Date = DateOnly.FromDateTime(transaction.Date),
						Amount = transaction.Amount,
						Description = transaction.Description,
						Code = transaction.TransactionCode.Code,
						TransactionStatus = new Poco.Entities.TransactionStatus
						{
							Description = transaction.TransactionStatus.Description
						},
						CheckRegister = checkTransaction != null ? new Poco.Entities.CheckRegister
						{
							Number = checkTransaction.Number,
						} : null
					};

					// Find or create account record
					var existingRecord = transactionRecords.FirstOrDefault(x => x.Id == transaction.InvestmentAccount.Id);
					if (existingRecord is null)
					{
						var newRecord = new Poco.TransactionAccountRecord
						{
							Id = transaction.InvestmentAccount.Id,
							AccountNumber = transaction.InvestmentAccount.AccountNumber,
							Name = transaction.InvestmentAccount.Name,
							Balance = transaction.InvestmentAccount.Balance,
							Investor = transactionPoco.Account.Investor,
							Transactions = new List<Poco.Entities.Transaction> { transactionPoco },
						};
						transactionRecords.Add(newRecord);
					}
					else
					{
						existingRecord.Transactions.Add(transactionPoco);
					}
				}

				return transactionRecords;
			}
		}

		/// <inheritdoc/>
		public async Task<Poco.Entities.Transaction?> FindByIdAsync(
			 int id,
			 CancellationToken cancellationToken = default
		)
		{
			using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
			{
				var transaction = await ctx
					 .Transactions.Include(x => x.TransactionStatus)
					 .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
				if (transaction is null)
				{
					return null;
				}
				return _mapper.Map<Poco.Entities.Transaction>(transaction);
			}
		}

		/// <inheritdoc/>
		public async Task<Poco.Entities.Transaction?> GetByIdAsync(
			 int id,
			 CancellationToken cancellationToken = default
		)
		{
			using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
			{
				var transaction = await ctx
					 .Transactions.Include(x => x.TransactionStatus)
					 .Include(x => x.TransactionCode)
					 .Include(x => x.InvestmentAccount)
					 .Where(x =>
						  x.TransactionStatus.Description != Constants.DataConstants.VoidStatus
					 )
					 .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
				if (transaction is null)
				{
					return null;
				}
				var transactionPoco = _mapper.Map<Poco.Entities.Transaction>(transaction);
				if (transaction.TransactionCode.Code == Constants.DataConstants.WithdrawalCode)
				{
					if (string.IsNullOrWhiteSpace(transaction.WireNumber))
					{
						//TI-2116   [Bug] ACH Transaction Details Not Visible in Edit Mode and Transaction Dashboard
						//var achDetails = await ctx.AchRegisters.FirstOrDefaultAsync(x => x.TransactionId == transaction.Id, cancellationToken);
						//if (achDetails is not null)
						//{
						//    transactionPoco.AchDetails = achDetails.Description;
						//}
						//else
						//{
						var checkDetails = await ctx.CheckRegisters.FirstOrDefaultAsync(
							 x => x.TransactionId == transaction.Id,
							 cancellationToken
						);
						if (checkDetails is not null)
						{
							transactionPoco.CheckRegister =
								 _mapper.Map<Poco.Entities.CheckRegister>(checkDetails);
						}
						//}
					}
				}
				else if (transaction.TransactionCode.Code == Constants.DataConstants.TransferCode)
				{
					if (transaction.ParentId is null)
					{
						var secondTransaction = await ctx
							 .Transactions.Include(x => x.InvestmentAccount)
							 .Include(x => x.TransactionStatus)
							 .Include(x => x.TransactionCode)
							 .FirstOrDefaultAsync(x => x.ParentId == id, cancellationToken);
						transactionPoco.ParentTransaction = _mapper.Map<Poco.Entities.Transaction>(
							 secondTransaction
						);
					}
					else
					{
						var baseTransaction = await ctx
							 .Transactions.Include(x => x.InvestmentAccount)
							 .FirstOrDefaultAsync(
								  x => x.Id == transaction.ParentId,
								  cancellationToken
							 );
						if (baseTransaction is not null)
						{
							var baseTransactionPoco = _mapper.Map<Poco.Entities.Transaction>(
								 baseTransaction
							);
							baseTransactionPoco.ParentTransaction = transactionPoco;
							return baseTransactionPoco;
						}
					}
				}
				return transactionPoco;
			}
		}

		/// <inheritdoc/>
		public async Task VoidTransactionAsync(
			 int transactionId,
			 int modifierId,
			 CancellationToken cancellationToken = default
		)
		{
			using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
			{
				var transaction = await ctx
					 .Transactions.Include(x => x.InvestmentAccount)
					 .FirstAsync(x => x.Id == transactionId, cancellationToken);

				var voidStatus = await ctx.TransactionStatuses.FirstAsync(
					 x => x.Description == Constants.DataConstants.VoidStatus,
					 cancellationToken
				);

				VoidTransaction(transaction, voidStatus, modifierId);

				bool isBaseTransaction = transaction.ParentId is null;
				Transaction secondTransaction;
				// check if base transaction?
				if (isBaseTransaction)
				{
					secondTransaction = await ctx
						 .Transactions.Include(x => x.InvestmentAccount)
						 .FirstAsync(x => x.ParentId == transactionId, cancellationToken);
				}
				else
				{
					secondTransaction = await ctx
						 .Transactions.Include(x => x.InvestmentAccount)
						 .FirstAsync(x => x.Id == transaction.ParentId, cancellationToken);
				}

				// void existing transactions
				VoidTransaction(secondTransaction, voidStatus, modifierId);

				ctx.Transactions.UpdateRange(transaction, secondTransaction);

				// create reverse transactions
				var originalTransactionReversed = isBaseTransaction
					 ? GetReversedTransaction(transaction, modifierId)
					 : GetReversedTransaction(secondTransaction, modifierId);
				var secondTransactionReversed = isBaseTransaction
					 ? GetReversedTransaction(secondTransaction, modifierId)
					 : GetReversedTransaction(transaction, modifierId);

				secondTransactionReversed.ParentTransaction = originalTransactionReversed;

				await ctx.Transactions.AddRangeAsync(
					 new[] { originalTransactionReversed, secondTransactionReversed },
					 cancellationToken
				);
				await ctx.SaveChangesAsync(cancellationToken);
			}
		}

		/// <inheritdoc/>
		public Task ReconcileTransactionAsync(
			 int transactionId,
			 int modifierId,
			 CancellationToken cancellationToken = default
		)
		{
			return ChangeReconciliationStatusAsync(
				 transactionId,
				 modifierId,
				 Constants.DataConstants.ReconciledStatus,
				 cancellationToken
			);
		}

		/// <inheritdoc/>
		public Task UnreconcileTransactionAsync(
			 int transactionId,
			 int modifierId,
			 CancellationToken cancellationToken = default
		)
		{
			return ChangeReconciliationStatusAsync(
				 transactionId,
				 modifierId,
				 Constants.DataConstants.OutstandingStatus,
				 cancellationToken
			);
		}

		private async Task ChangeReconciliationStatusAsync(
			 int transactionId,
			 int modifierId,
			 string status,
			 CancellationToken cancellationToken = default
		)
		{
			using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
			{
				var transaction = await ctx.Transactions.FirstAsync(
					 x => x.Id == transactionId,
					 cancellationToken
				);

				var reconciledStatus = await ctx.TransactionStatuses.FirstAsync(
					 x => x.Description == status,
					 cancellationToken
				);

				transaction.TransactionStatus = reconciledStatus;
				transaction.LastModifiedBy = modifierId;
				transaction.LastModifiedOn = DateTime.UtcNow;

				bool isBaseTransaction = transaction.ParentId is null;
				Transaction secondTransaction;
				// check if base transaction?
				if (isBaseTransaction)
				{
					secondTransaction = await ctx.Transactions.FirstAsync(
						 x => x.ParentId == transactionId,
						 cancellationToken
					);
				}
				else
				{
					secondTransaction = await ctx.Transactions.FirstAsync(
						 x => x.Id == transaction.ParentId,
						 cancellationToken
					);
				}

				secondTransaction.TransactionStatus = reconciledStatus;
				secondTransaction.LastModifiedBy = modifierId;
				secondTransaction.LastModifiedOn = DateTime.UtcNow;

				ctx.Transactions.UpdateRange(transaction, secondTransaction);
				await ctx.SaveChangesAsync(cancellationToken);
			}
		}

		private void VoidTransaction(
			 Transaction transaction,
			 TransactionStatus voidStatus,
			 int modifierId
		)
		{
			transaction.TransactionStatus = voidStatus;
			transaction.LastModifiedBy = modifierId;
			transaction.LastModifiedOn = DateTime.UtcNow;
			transaction.InvestmentAccount.Balance += transaction.Amount * -1;
		}

		private Transaction GetReversedTransaction(Transaction transaction, int modifierId)
		{
			return new Transaction()
			{
				AccountId = transaction.AccountId,
				Amount = transaction.Amount * -1,
				TransactionStatus = transaction.TransactionStatus,
				Date = transaction.Date,
				Description = transaction.Description,
				CodeId = transaction.CodeId,
				LastModifiedBy = modifierId,
				LastModifiedOn = DateTime.UtcNow,
				CreatedBy = modifierId,
				CreatedOn = DateTime.UtcNow,
			};
		}

		public async Task<bool> IsDistributionTransactionAsync(
			 int transactionId,
			 CancellationToken cancellationToken = default
		)
		{
			using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
			{
				var distributionTransaction =
					 await ctx.DistributionTransactions.FirstOrDefaultAsync(
						  x => x.TransactionId == transactionId,
						  cancellationToken
					 );
				return distributionTransaction is not null;
			}
		}

		public async Task<bool> IsMonthEndTransactionAsync(
			int transactionId,
			CancellationToken cancellationToken = default
		)
		{
			using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
			{
				var monthEndProcessTransaction =
					 await ctx.MonthEndProcessTransactions.FirstOrDefaultAsync(
						  x => x.TransactionId == transactionId,
						  cancellationToken
					 );
				return monthEndProcessTransaction is not null;
			}
		}

		public async Task<int> UpdateTransactionAsync(
			 int transactionId,
			 UpdateTransactionRequest request,
			 int lastModifiedBy,
			 CancellationToken cancellationToken = default
		)
		{
			using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
			{
				var transaction = await ctx.Transactions.FirstOrDefaultAsync(
					 x => x.Id == transactionId,
					 cancellationToken
				);

				if (transaction is null)
				{
					throw new KeyNotFoundException($"Transaction with ID {transactionId} not found.");
				}

				transaction.BankTransactionType = request.BankTransactionType;
				transaction.LastModifiedBy = lastModifiedBy;
				transaction.LastModifiedOn = DateTime.UtcNow;
				ctx.Transactions.Update(transaction);

				var parentTransaction = await ctx.Transactions.FirstOrDefaultAsync(
					 x => x.ParentId == transaction.Id,
					 cancellationToken
				);

				if (parentTransaction != null)
				{
					parentTransaction.BankTransactionType = request.BankTransactionType;
					parentTransaction.LastModifiedBy = lastModifiedBy;
					parentTransaction.LastModifiedOn = DateTime.UtcNow;
					ctx.Transactions.Update(parentTransaction);
				}

				await ctx.SaveChangesAsync(cancellationToken);

				return transaction.Id;
			}
		}
	}
}
