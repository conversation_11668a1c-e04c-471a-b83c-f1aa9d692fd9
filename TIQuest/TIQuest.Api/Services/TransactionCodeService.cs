﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Services
{
    internal class TransactionCodeService : ITransactionCodeService
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;
        private readonly IMapper _mapper;

        public TransactionCodeService(IDbContextFactory<DataContext> dbContextFactory, IMapper mapper)
        {
            _contextFactory = dbContextFactory;
            _mapper = mapper;
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.TransactionCode?> FindByIdAsync(int id, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var code = await ctx.TransactionCodes.FindAsync(new object?[] { id }, cancellationToken: cancellationToken);
                if (code is not null)
                {
                    return _mapper.Map<TransactionCode, Poco.Entities.TransactionCode>(code);
                }
                return null;
            }
        }
    }
}
