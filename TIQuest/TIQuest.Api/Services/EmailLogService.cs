﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Services
{
    internal class EmailLogService : IEmailLogService
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;
        private readonly IMapper _mapper;

        public EmailLogService(IDbContextFactory<DataContext> dbContextFactory, IMapper mapper)
        {
            _contextFactory = dbContextFactory;
            _mapper = mapper;
        }

        public async Task AddLogAsync(Poco.Entities.EmailLog emailLog, CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var log = _mapper.Map<EmailLog>(emailLog);
                ctx.EmailLogs.Add(log);
                await ctx.SaveChangesAsync(cancellationToken);
            }
        }

        public async Task<IEnumerable<Poco.Entities.EmailLog>> GetAllAsync(CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var logs = await ctx.EmailLogs.ToListAsync(cancellationToken);
                return _mapper.Map<IEnumerable<Poco.Entities.EmailLog>>(logs);
            }
        }
    }
}
