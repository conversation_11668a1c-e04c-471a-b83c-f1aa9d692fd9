﻿using Microsoft.EntityFrameworkCore;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Services
{
    internal class AuditLogService : IAuditLogService
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;

        public AuditLogService(IDbContextFactory<DataContext> dbContextFactory)
        {
            _contextFactory = dbContextFactory;
        }

        public async Task<IEnumerable<Poco.Entities.AuditLog>> GetAllAsync(CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var auditLogs = await ctx.AuditLogs
                                         .Include(x => x.UserModifiedBy)
                                         .Select(x => new Poco.Entities.AuditLog
                                         {
                                             Date = x.Date,
                                             Operation = x.Operation,
                                             Description = x.Description,
                                             ModifiedBy = $"{x.UserModifiedBy.LastName}, {x.UserModifiedBy.FirstName}"
                                         })
                                         .ToListAsync(cancellationToken);
                return auditLogs;
            }
        }

        public async Task InsertAuditLogAsync(string operation, string description, int modifiedBy, CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                // insert auditlog to database using sql query
                await ctx.Database.ExecuteSqlInterpolatedAsync($"INSERT INTO AuditLog (Date, Operation, Description, ModifiedBy) VALUES ({DateTime.UtcNow}, {operation}, {description}, {modifiedBy})", cancellationToken);
            }
        }
    }
}
