﻿using AutoMapper;
using CsvHelper;
using Microsoft.EntityFrameworkCore;
using System.Globalization;
using TIQuest.Api.DTO;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.Managers;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Services
{
    internal class AchRegisterService : IAchRegisterService
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;
        private readonly IMapper _mapper;
        private readonly ICsvManager _csvManager;

        public AchRegisterService(IDbContextFactory<DataContext> dbContextFactory, IMapper mapper, ICsvManager csvManager)
        {
            _contextFactory = dbContextFactory;
            _mapper = mapper;
            _csvManager = csvManager;
        }

        /// <inheritdoc />
        public async Task<Stream> GetAchReportAsync(IEnumerable<int> ids, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            using (var dbTransaction = await ctx.Database.BeginTransactionAsync(cancellationToken))
            {
                var achRegisterQueryable = GetAchRecordQueryable(ctx, cancellationToken);
                var recordsToExport = await achRegisterQueryable
                                            .Where(x => ids.Contains(x.Id))
                                            .ToListAsync(cancellationToken);
                var achRecords = _mapper.Map<IEnumerable<AchCsvResponse>>(recordsToExport);

                var stream = await _csvManager.WriteAsync(achRecords);

                foreach (var record in recordsToExport)
                {
                    record.IsPrinted = true;
                }

                ctx.AchRegisters.UpdateRange(recordsToExport);
                await ctx.SaveChangesAsync(cancellationToken);
                await dbTransaction.CommitAsync(cancellationToken);

                return stream;
            }
        }

        /// <inheritdoc />
        public async Task<IEnumerable<Poco.Entities.AchRegister>> GetAllAsync(CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var achRegisterQueryable = GetAchRecordQueryable(ctx, cancellationToken);
                var achRegisters = await achRegisterQueryable.ToListAsync(cancellationToken);

                return _mapper.Map<IEnumerable<Poco.Entities.AchRegister>>(achRegisters);
            }
        }

        private IQueryable<AchRegister> GetAchRecordQueryable(DataContext ctx, CancellationToken cancellationToken)
        {
            return ctx.AchRegisters
                    .Include(x => x.Transaction)
                    .Include(x => x.Transaction.TransactionStatus)
                    .Include(x => x.Transaction.InvestmentAccount)
                    .Include(x => x.Transaction.InvestmentAccount.AccountInvestor)
                    .Where(x => !x.IsPrinted
                    && x.Transaction.TransactionStatus.Description != Constants.DataConstants.VoidStatus);
        }

        private async Task<MemoryStream> WriteToCsvAsync(IEnumerable<AchCsvResponse> achRecordsToExport)
        {
            using (var memoryStream = new MemoryStream())
            using (var writer = new StreamWriter(memoryStream))
            using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                await csv.WriteRecordsAsync(achRecordsToExport);
                await writer.FlushAsync();
                memoryStream.Seek(0, SeekOrigin.Begin);
                var cloneStream = new MemoryStream();
                await memoryStream.CopyToAsync(cloneStream);
                cloneStream.Seek(0, SeekOrigin.Begin);
                return cloneStream;
            }
        }
    }
}
