﻿using Microsoft.EntityFrameworkCore;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Poco.Reports;

namespace TIQuest.Api.Services
{
    internal class CompanyReportService : ICompanyReportService
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;

        public CompanyReportService(IDbContextFactory<DataContext> dbContextFactory)
        {
            _contextFactory = dbContextFactory;
        }

        public async Task<CompanyReport> GetCompanyReportDataAsync(CancellationToken cancellationToken)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var cashAccount = await ctx.InvestmentAccounts
                                        .Include(x => x.AccountInvestor)
                                        .FirstAsync(x => x.Id == Constants.DataConstants.CashAccountId, cancellationToken: cancellationToken);

                var company = cashAccount.AccountInvestor;

                var email = await ctx.Settings.FirstAsync(x => x.Key == Constants.DataConstants.CompanyEmailKey, cancellationToken);

                return new CompanyReport
                                (
                                    company.CompanyName,
                                    company.FirstName,
                                    company.LastName,
                                    company.Fax,
                                    company.OfficePhone,
                                    email.Value,
                                    company.TaxNumber,
                                    company.Address,
                                    company.Apartment,
                                    company.City,
                                    company.State,
                                    company.Zip
                                );
            }
        }
    }
}
