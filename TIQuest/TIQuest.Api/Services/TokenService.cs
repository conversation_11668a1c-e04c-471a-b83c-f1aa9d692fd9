﻿using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Options;

namespace TIQuest.Api.Services
{
    internal sealed class TokenService : ITokenService
    {
        private readonly JwtConfigOptions _jwtConfig;

        public TokenService(IOptions<JwtConfigOptions> jwtConfigOptions)
        {
            _jwtConfig = jwtConfigOptions.Value;
        }

        public string GetToken(IEnumerable<Claim> claims, DateTime expiryTime)
        {
            var token = CreateJwtToken(claims, expiryTime);
            var tokenHandler = new JwtSecurityTokenHandler();
            return tokenHandler.WriteToken(token);
        }

        private JwtSecurityToken CreateJwtToken(IEnumerable<Claim> claims, DateTime expiration)
        {
            var credentials = GetSigningCredentials();
            return new(
                _jwtConfig.Issuer,
                _jwtConfig.Audience,
                claims,
                notBefore: DateTime.UtcNow,
                expires: expiration,
                signingCredentials: credentials);
        }

        private SigningCredentials GetSigningCredentials()
        {
            return new SigningCredentials(
                new SymmetricSecurityKey(
                    Encoding.UTF8.GetBytes(_jwtConfig.Secret)
                ),
                SecurityAlgorithms.HmacSha256
            );
        }

    }
}
