﻿using Microsoft.Extensions.Options;
using System.Security.Claims;
using TIQuest.Api.Enums;
using TIQuest.Api.Extensions;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Options;
using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Services
{
    internal class TokenMailService : ITokenMailService
    {
        private readonly ITokenService _tokenService;
        private readonly ApplicationConfig _appConfig;
        private readonly ITemplateGeneratorService _templateService;
        private readonly EmailSettings _emailSettings;
        private readonly IEmailService _emailService;
        private readonly IEmailLogService _emailLogService;

        public TokenMailService(ITokenService tokenService, ITemplateGeneratorService templateGeneratorService, IEmailService emailService,
            IOptions<ApplicationConfig> appConfig, IOptions<EmailSettings> emailSettings, IEmailLogService emailLogService)
        {
            _tokenService = tokenService;
            _appConfig = appConfig.Value;
            _templateService = templateGeneratorService;
            _emailSettings = emailSettings.Value;
            _emailService = emailService;
            _emailLogService = emailLogService;
        }

        /// <inheritdoc/>
        public async Task SendTokenAsync(ICollection<Claim> claims, TemplateType templateType, User user, CancellationToken cancellationToken = default)
        {
            DateTime expiryTime = DateTime.UtcNow.AddHours(_appConfig.ResetTokenExpirationHours);
            string token = _tokenService.GetToken(claims, expiryTime);
            await SendTemplatedEmailAsync(user, token, templateType, cancellationToken);
        }

        private async Task SendTemplatedEmailAsync(User user, string token, TemplateType templateType, CancellationToken cancellationToken)
        {
            string template;
            string? subject = null;
            string? blobId = null;
            switch (templateType)
            {
                case TemplateType.CreateUser:
                    subject = "Confirmation Instructions and Create Password for Your New Account";
                    template = _templateService.GetCreateUserTemplate(user, token);
                    blobId = await _emailService.SendEmailAsync(_emailSettings, user, subject, template, true);
                    break;
                case TemplateType.ChangePassword:
                    subject = "Reset Your Password";
                    template = _templateService.GetChangePasswordTemplate(user, token);
                    blobId = await _emailService.SendEmailAsync(_emailSettings, user, subject, template, true);
                    break;
                case TemplateType.ResetPassword:
                    subject = "Reset Your Password";
                    template = _templateService.GetResetPasswordTemplate(user, token);
                    blobId = await _emailService.SendEmailAsync(_emailSettings, user, subject, template, true);
                    break;
            }

            await _emailLogService.AddLogAsync(new EmailLog
            {
                Email = user.Email,
                Subject = subject,
                Date = DateOnly.FromDateTime(DateTime.UtcNow),
                Status = EmailLogStatus.Queued.ToString(),
                Location = blobId
            }, cancellationToken);
        }
    }
}
