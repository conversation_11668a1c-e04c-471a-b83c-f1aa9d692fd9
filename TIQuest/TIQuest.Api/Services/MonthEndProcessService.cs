﻿using System.Diagnostics.CodeAnalysis;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Services
{
    internal class MonthEndProcessService : IMonthEndProcessService
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;
        private readonly IMapper _mapper;
        private readonly ITransactionOperationService _operationService;
        private readonly ITransactionService _transactionService;

        public MonthEndProcessService(
            IDbContextFactory<DataContext> dbContextFactory,
            IMapper mapper,
            ITransactionOperationService transactionOperationService,
            ITransactionService transactionService
        )
        {
            _contextFactory = dbContextFactory;
            _mapper = mapper;
            _operationService = transactionOperationService;
            _transactionService = transactionService;
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<Poco.Entities.MonthEndProcess>> GetAllAsync(
            CancellationToken cancellationToken = default
        )
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var monthEndProcesses = await ctx.MonthEndProcesses.ToListAsync(cancellationToken);

                return _mapper.Map<IEnumerable<Poco.Entities.MonthEndProcess>>(monthEndProcesses);
            }
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.MonthEndProcess> GetLatestMonthEndProcessAsync(
            CancellationToken cancellationToken
        )
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var latestMonthEndProcess = await ctx
                    .MonthEndProcesses.OrderByDescending(m => m.Id)
                    .FirstOrDefaultAsync(cancellationToken);
                return _mapper.Map<Poco.Entities.MonthEndProcess>(latestMonthEndProcess);
            }
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.MonthEndProcess?> GetByIdAsync(
            int id,
            CancellationToken cancellationToken = default
        )
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var monthEndProcesses = await ctx.MonthEndProcesses.FirstOrDefaultAsync(
                    x => x.Id == id,
                    cancellationToken
                );
                if (monthEndProcesses is null)
                {
                    return null;
                }

                return _mapper.Map<Poco.Entities.MonthEndProcess>(monthEndProcesses);
            }
        }

        /// <inheritdoc/>
        public async Task<DateTime> GetLastProcessedMonthAsync(
            CancellationToken cancellationToken = default
        )
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                MonthEndProcess? lastMonth = await ctx
                    .MonthEndProcesses.OrderByDescending(x => x.Year)
                    .ThenByDescending(x => x.Month)
                    .FirstOrDefaultAsync(cancellationToken);
                if (lastMonth is null)
                {
                    throw new InvalidOperationException("Unable to find last processed month.");
                }
                DateTime lastProcessedMonth = new(lastMonth.Year, lastMonth.Month, 1, 0, 0, 0);
                return lastProcessedMonth.AddMonths(1).AddTicks(-1);
            }
        }

        /// <inheritdoc/>
        public async Task<(
            decimal AverageBalance,
            decimal InterestDue
        )> GetInvestmentInterestSummaryAsync(
            DateTime monthStartDate,
            DateTime monthEndDate,
            decimal interestRate,
            CancellationToken cancellationToken = default
        )
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var voidStatus = await ctx.TransactionStatuses.FirstAsync(
                    x => x.Description == Constants.DataConstants.VoidStatus,
                    cancellationToken
                );
                var transactionsByAccount = await GetInvestmentTransactionsByAccountAsync(
                    ctx,
                    monthStartDate,
                    monthEndDate,
                    voidStatus.Id,
                    true,
                    cancellationToken
                );

                var distinctAccounts = transactionsByAccount.Select(x => x.Id).Distinct().ToList();

                var previousMonth = monthStartDate.AddMonths(-1);

                var previousAccountBalances = await ctx
                    .MonthEndBalances.Where(x =>
                        distinctAccounts.Contains(x.AccountId)
                        && x.MonthEndProcess.Month == previousMonth.Month
                        && x.MonthEndProcess.Year == previousMonth.Year
                    )
                    .ToListAsync(cancellationToken);

                var balanceCalculator = new MonthlyBalanceCalculator(monthStartDate, monthEndDate);

                decimal interestDue = 0;
                decimal totalBalance = 0;

                foreach (InvestmentAccount account in transactionsByAccount)
                {
                    // check account balance as of previous month end
                    var previousBalance = GetPreviousMonthEndBalanceForAccount(
                        account.Id,
                        previousAccountBalances
                    );

                    var averageBalance = balanceCalculator.CalculateAverageMonthlyBalance(
                        account.Transactions,
                        previousBalance
                    );
                    totalBalance += averageBalance;
                    interestDue += balanceCalculator.CalculateMonthlyInterestDue(
                        averageBalance,
                        account.Rate ?? interestRate
                    );
                }

                return (totalBalance, interestDue);
            }
        }

        /// <inheritdoc/>
        public async Task RunMonthEndProcessAsync(
            DateTime monthStartDate,
            DateTime monthEndDate,
            decimal interestRate,
            string description,
            decimal managementRate,
            int modifierId,
            CancellationToken cancellationToken = default
        )
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            using (var transaction = await ctx.Database.BeginTransactionAsync())
            {
                var monthEndProcess = new MonthEndProcess
                {
                    Month = monthStartDate.Month,
                    Year = monthStartDate.Year,
                    InvestmentRate = interestRate / 100,
                    Description = description,
                    ManagementRate = managementRate / 100,
                };

                var balanceCalculator = new MonthlyBalanceCalculator(monthStartDate, monthEndDate);
                var codes = await ctx.TransactionCodes.ToListAsync(cancellationToken);
                var withdrawalCode = codes.First(x =>
                    x.Code == Constants.DataConstants.WithdrawalCode
                );
                var outstandingStatus = await ctx.TransactionStatuses.FirstAsync(
                    x => x.Description == Constants.DataConstants.OutstandingStatus,
                    cancellationToken
                );
                var reconciledStatus = await ctx.TransactionStatuses.FirstAsync(
                    x => x.Description == Constants.DataConstants.ReconciledStatus,
                    cancellationToken
                );
                var voidStatus = await ctx.TransactionStatuses.FirstAsync(
                    x => x.Description == Constants.DataConstants.VoidStatus,
                    cancellationToken
                );
                var cashAccount = await ctx.InvestmentAccounts.FirstAsync(
                    x => x.Id == Constants.DataConstants.CashAccountId,
                    cancellationToken
                );
                var previousMonthEndBalances = await ctx
                    .MonthEndBalances.Where(x =>
                        x.MonthEndProcess.Month == monthStartDate.AddMonths(-1).Month
                        && x.MonthEndProcess.Year == monthStartDate.AddMonths(-1).Year
                    )
                    .ToListAsync(cancellationToken);

                var monthEndTransactions = new List<Transaction>();

                decimal variableInterestDue = 0;

                // Investment interest
                var investmentInterestDue = await RecordInvestmentInterestAsync(
                    ctx,
                    monthEndTransactions,
                    codes,
                    balanceCalculator,
                    cashAccount,
                    previousMonthEndBalances,
                    monthStartDate,
                    monthEndDate,
                    interestRate,
                    description,
                    modifierId,
                    reconciledStatus.Id,
                    outstandingStatus.Id,
                    voidStatus.Id,
                    cancellationToken
                );

                variableInterestDue += investmentInterestDue;

                // management fee
                if (investmentInterestDue != 0)
                {
                    monthEndProcess.ManagementFee = Math.Round(
                        investmentInterestDue * (managementRate / 100),
                        2
                    );
                    variableInterestDue += monthEndProcess.ManagementFee;
                }

                // fixed loan stuff
                var fixedLoanInterestAccrued = await RecordFixedLoanInterestAsync(
                    ctx,
                    monthEndTransactions,
                    codes,
                    balanceCalculator,
                    cashAccount,
                    previousMonthEndBalances,
                    monthStartDate,
                    monthEndDate,
                    description,
                    modifierId,
                    reconciledStatus.Id,
                    outstandingStatus.Id,
                    voidStatus.Id,
                    cancellationToken
                );
                variableInterestDue -= fixedLoanInterestAccrued;

                // variable loan stuff
                var variableLoanInterestAccrued = await RecordVariableLoanInterestAsync(
                    ctx,
                    monthEndTransactions,
                    codes,
                    balanceCalculator,
                    cashAccount,
                    previousMonthEndBalances,
                    monthStartDate,
                    monthEndDate,
                    variableInterestDue,
                    description,
                    modifierId,
                    reconciledStatus.Id,
                    outstandingStatus.Id,
                    voidStatus.Id,
                    cancellationToken
                );

                variableInterestDue -= variableLoanInterestAccrued.TotalDue;

                monthEndProcess.LoanRate = variableLoanInterestAccrued.ApplicableRate / 100;

                decimal totalout = investmentInterestDue + monthEndProcess.ManagementFee;
                decimal totalin =
                    Math.Abs(variableLoanInterestAccrued.TotalDue) + fixedLoanInterestAccrued;

                monthEndProcess.ManagementFee = monthEndProcess.ManagementFee + totalin - totalout;

                // record management fee
                if (investmentInterestDue != 0)
                {
                    await RecordManagementFeeAsync(
                        ctx,
                        monthEndTransactions,
                        codes,
                        monthEndProcess.ManagementFee,
                        cashAccount,
                        monthEndDate,
                        $"Management Fee for {monthStartDate.ToString("MMMM")} {monthStartDate.Year}",
                        modifierId,
                        outstandingStatus.Id,
                        cancellationToken
                    );
                }

                await ctx.MonthEndProcesses.AddAsync(monthEndProcess, cancellationToken);

                await ctx.Transactions.AddRangeAsync(monthEndTransactions, cancellationToken);
                ctx.InvestmentAccounts.Update(cashAccount);

                await ctx.SaveChangesAsync(cancellationToken);

                // insert into month end transaction table
                await RegisterMonthEndTransactionsAsync(
                    ctx,
                    monthEndProcess.Id,
                    monthEndTransactions.Select(x => x.Id),
                    cancellationToken
                );

                await AutoReconcileTransferTransactionsAsync(
                    ctx,
                    monthStartDate,
                    monthEndDate,
                    modifierId,
                    cancellationToken
                );

                var impactedAccounts = await ctx
                    .InvestmentAccounts.Where(ia => ia.StartDate <= monthEndDate)
                    .ToListAsync(cancellationToken);
                List<MonthEndBalance> monthEndBalances = new List<MonthEndBalance>();
                foreach (var account in impactedAccounts)
                {
                    var previousBalance = GetPreviousMonthEndBalanceForAccount(
                        account.Id,
                        previousMonthEndBalances
                    );
                    var currentMonthEndBalance = new MonthEndBalance
                    {
                        MonthEndProcess = monthEndProcess,
                        InvestmentAccount = account,
                        StartingBalance = previousBalance,
                        // !!!ALERT - BELOW LOGIC IS WRONG - EndBalance will get recalcuate in CalculateMonthEndBalances()
                        // latest running balance = this months end
                        // EndingBalance = account.Balance
                    };
                    monthEndBalances.Add(currentMonthEndBalance);
                }

                await CalculateMonthEndBalances(
                    ctx,
                    monthEndBalances,
                    monthStartDate,
                    monthEndDate,
                    cancellationToken
                );

                await transaction.CommitAsync(cancellationToken);
            }
        }

        private async Task AutoReconcileTransferTransactionsAsync(
            DataContext ctx,
            DateTime monthStartDate,
            DateTime monthEndDate,
            int modifierId,
            CancellationToken cancellationToken
        )
        {
            var transferTransactions = await ctx
                .Transactions.Include(x => x.TransactionCode)
                .Include(x => x.TransactionStatus)
                .Include(x => x.InvestmentAccount)
                .Where(x =>
                    x.Date >= monthStartDate
                    && x.Date < monthEndDate
                    && x.TransactionCode.Code == Constants.DataConstants.TransferCode
                    && x.TransactionStatus.Description == Constants.DataConstants.OutstandingStatus
                )
                .ToListAsync(cancellationToken);

            foreach (var transferTransaction in transferTransactions)
            {
                var transaction = await ctx.Transactions.FirstAsync(
                    x => x.Id == transferTransaction.Id,
                    cancellationToken
                );

                var reconciledStatus = await ctx.TransactionStatuses.FirstAsync(
                    x => x.Description == Constants.DataConstants.ReconciledStatus,
                    cancellationToken
                );

                transaction.TransactionStatus = reconciledStatus;
                transaction.LastModifiedBy = modifierId;
                transaction.LastModifiedOn = DateTime.UtcNow;

                bool isBaseTransaction = transaction.ParentId is null;
                Transaction secondTransaction;
                // check if base transaction?
                if (isBaseTransaction)
                {
                    secondTransaction = await ctx.Transactions.FirstAsync(
                        x => x.ParentId == transaction.Id,
                        cancellationToken
                    );
                }
                else
                {
                    secondTransaction = await ctx.Transactions.FirstAsync(
                        x => x.Id == transaction.ParentId,
                        cancellationToken
                    );
                }

                secondTransaction.TransactionStatus = reconciledStatus;
                secondTransaction.LastModifiedBy = modifierId;
                secondTransaction.LastModifiedOn = DateTime.UtcNow;

                ctx.Transactions.UpdateRange(transaction, secondTransaction);

                await ctx.SaveChangesAsync(cancellationToken);
            }
        }

        private async Task AutoUnreconcileTransferTransactionsAsync(
            DataContext ctx,
            DateTime monthStartDate,
            DateTime monthEndDate,
            int modifierId,
            CancellationToken cancellationToken
        )
        {
            var transactions = await ctx
                .Transactions.Include(x => x.TransactionCode)
                .Include(x => x.TransactionStatus)
                .Include(x => x.InvestmentAccount)
                .Where(x =>
                    x.Date >= monthStartDate
                    && x.Date < monthEndDate
                    && x.TransactionStatus.Description == Constants.DataConstants.ReconciledStatus
                )
                .ToListAsync(cancellationToken);

            foreach (var transaction in transactions)
            {
                var outstandingStatus = await ctx.TransactionStatuses.FirstAsync(
                    x => x.Description == Constants.DataConstants.OutstandingStatus,
                    cancellationToken
                );

                transaction.TransactionStatus = outstandingStatus;
                transaction.LastModifiedBy = modifierId;
                transaction.LastModifiedOn = DateTime.UtcNow;

                bool isBaseTransaction = transaction.ParentId is null;
                Transaction secondTransaction;

                if (isBaseTransaction)
                {
                    secondTransaction = await ctx.Transactions.FirstAsync(
                        x => x.ParentId == transaction.Id,
                        cancellationToken
                    );
                }
                else
                {
                    secondTransaction = await ctx.Transactions.FirstAsync(
                        x => x.Id == transaction.ParentId,
                        cancellationToken
                    );
                }

                secondTransaction.TransactionStatus = outstandingStatus;
                secondTransaction.LastModifiedBy = modifierId;
                secondTransaction.LastModifiedOn = DateTime.UtcNow;

                ctx.Transactions.UpdateRange(transaction, secondTransaction);

                await ctx.SaveChangesAsync(cancellationToken);
            }
        }

        private async Task CalculateMonthEndBalances(
            DataContext ctx,
            List<MonthEndBalance> monthEndBalances,
            DateTime startDate,
            DateTime endDate,
            CancellationToken cancellationToken
        )
        {
            foreach (MonthEndBalance balances in monthEndBalances)
            {
                var interestTransactions = ctx.Transactions.Where(x =>
                    x.Date >= startDate
                    && x.Date < endDate
                    && (
                        x.TransactionCode.Code == Constants.DataConstants.LoanInterestCode
                        || x.TransactionCode.Code == Constants.DataConstants.InvestmentInterestCode
                    )
                    && x.InvestmentAccount.Id == balances.InvestmentAccount.Id
                );

                var interest =
                    interestTransactions
                        .Where(x => x.Date >= startDate && x.Date < endDate)
                        .Sum(x => x.Amount) * -1; // ==> Possibility of negative interest

                var credits = ctx
                    .Transactions.Where(x =>
                        x.Amount < 0
                        && x.Date >= startDate
                        && x.Date < endDate
                        && x.InvestmentAccount.Id == balances.InvestmentAccount.Id
                        && !interestTransactions.Any(it => it.Id == x.Id)
                    )
                    .Sum(x => Math.Abs(x.Amount));

                var deposits = ctx
                    .Transactions.Where(x =>
                        x.Amount > 0
                        && x.Date >= startDate
                        && x.Date < endDate
                        && x.InvestmentAccount.Id == balances.InvestmentAccount.Id
                        && !interestTransactions.Any(it => it.Id == x.Id)
                    )
                    .Sum(x => Math.Abs(x.Amount));

                balances.EndingBalance =
                    balances.StartingBalance + deposits - credits + (interest * -1); //==> Possibility of negative interest
            }

            await InsertMonthEndBalancesAsyn(ctx, monthEndBalances, cancellationToken);
        }

        /// <inheritdoc/>
        public async Task DeleteMonthEndProcessAsync(
            int processId,
            int modifierId,
            CancellationToken cancellationToken = default
        )
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            using (var dbTransaction = await ctx.Database.BeginTransactionAsync())
            {
                var monthEndProcess = await ctx.MonthEndProcesses.FindAsync(
                    [processId],
                    cancellationToken
                );

                if (monthEndProcess is null)
                {
                    throw new ArgumentException($"Month end process {processId} not found.");
                }

                var monthEndTransactions = await ctx
                    .MonthEndProcessTransactions.Include(x => x.Transaction)
                    .Include(x => x.Transaction.InvestmentAccount)
                    .Where(x => x.MonthEndProcessId == processId)
                    .ToListAsync(cancellationToken);

                var transactions = monthEndTransactions
                    .Select(x => x.TransactionId)
                    .Distinct()
                    .ToList();
                var cashAccount = await ctx.InvestmentAccounts.FirstAsync(
                    x => x.Id == Constants.DataConstants.CashAccountId,
                    cancellationToken
                );
                var updatedAccounts = new List<InvestmentAccount>() { cashAccount };

                // reverse the balance
                foreach (var monthEndProcessTransaction in monthEndTransactions)
                {
                    monthEndProcessTransaction.Transaction.InvestmentAccount.Balance -=
                        monthEndProcessTransaction.Transaction.Amount;
                    if (
                        !updatedAccounts.Any(x =>
                            x.Id == monthEndProcessTransaction.Transaction.InvestmentAccount.Id
                        )
                    )
                    {
                        updatedAccounts.Add(
                            monthEndProcessTransaction.Transaction.InvestmentAccount
                        );
                    }
                }

                // delete the transactions
                await ctx
                    .MonthEndProcessTransactions.Where(x => x.MonthEndProcessId == processId)
                    .ExecuteDeleteAsync(cancellationToken);

                await ctx
                    .CheckRegisters.Where(x => transactions.Contains(x.TransactionId))
                    .ExecuteDeleteAsync(cancellationToken);
                await ctx
                    .AchRegisters.Where(x => transactions.Contains(x.TransactionId))
                    .ExecuteDeleteAsync(cancellationToken);
                await ctx
                    .Transactions.Where(x => transactions.Contains(x.Id))
                    .ExecuteDeleteAsync(cancellationToken);

                await ctx
                    .MonthEndBalances.Where(x => x.MonthEndProcessId == processId)
                    .ExecuteDeleteAsync(cancellationToken);

                ctx.InvestmentAccounts.UpdateRange(updatedAccounts);
                ctx.MonthEndProcesses.Remove(monthEndProcess);

                var monthStartDate = new DateTime(monthEndProcess.Year, monthEndProcess.Month, 1);
                var monthEndDate = monthStartDate.AddMonths(1).AddTicks(-1);

                await AutoUnreconcileTransferTransactionsAsync(
                    ctx,
                    monthStartDate,
                    monthEndDate,
                    modifierId,
                    cancellationToken
                );

                await ctx.SaveChangesAsync(cancellationToken);

                await dbTransaction.CommitAsync(cancellationToken);
            }
        }

        private async Task<(
            decimal TotalDue,
            decimal ApplicableRate
        )> RecordVariableLoanInterestAsync(
            DataContext ctx,
            List<Transaction> monthEndTransactions,
            List<TransactionCode> codes,
            MonthlyBalanceCalculator balanceCalculator,
            InvestmentAccount cashAccount,
            List<MonthEndBalance> previousMonthEndBalances,
            DateTime startDate,
            DateTime endDate,
            decimal interestDue,
            string description,
            int modifierId,
            int reconciledStatusId,
            int outstandingStatusId,
            int voidStatusId,
            CancellationToken cancellationToken
        )
        {
            var variableLoanTransactionsByAccount = await GetVariableLoanTransactionsByAccountAsync(
                ctx,
                startDate,
                endDate,
                voidStatusId,
                cancellationToken
            );
            var loanInterestCode = codes.First(x =>
                x.Code == Constants.DataConstants.LoanInterestCode
            );

            var variableLoanDetails = GetVariableLoanRate(
                interestDue,
                variableLoanTransactionsByAccount,
                previousMonthEndBalances,
                balanceCalculator
            );

            decimal totalDue = 0;
            decimal avgBalanceTotal = 0;

            foreach (InvestmentAccount account in variableLoanTransactionsByAccount)
            {
                var previousBalance = GetPreviousMonthEndBalanceForAccount(
                    account.Id,
                    previousMonthEndBalances
                );

                var averageBalance = balanceCalculator.CalculateAverageMonthlyBalance(
                    account.Transactions,
                    previousBalance
                );
                var dynamicInterestDue = balanceCalculator.CalculateMonthlyInterestDue(
                    averageBalance,
                    variableLoanDetails.ApplicableRate
                );

                //==> Summary sums up the balance and then calculates Interest Due at end.
                avgBalanceTotal += averageBalance;

                if (dynamicInterestDue != 0)
                {
                    var cashWithdrawalTuple =
                        await _operationService.CreateSystemCashWithdrawalAsync(
                            ctx,
                            new Poco.Transactions.DepositTransaction
                            {
                                Account = account.Id,
                                Amount = Math.Abs(dynamicInterestDue),
                                Code = loanInterestCode.Id,
                                Date = endDate.AddTicks(-1),
                                Description = description,
                                ModifierId = modifierId,
                                Status = reconciledStatusId,
                            },
                            cancellationToken
                        );

                    account.Balance += cashWithdrawalTuple.First.Amount;
                    cashAccount.Balance += cashWithdrawalTuple.Second.Amount;

                    monthEndTransactions.Add(cashWithdrawalTuple.First);
                    monthEndTransactions.Add(cashWithdrawalTuple.Second);

                    // update account balance and metadata
                    ctx.InvestmentAccounts.Update(account);
                }
            }

            totalDue = balanceCalculator.CalculateMonthlyInterestDue(
                avgBalanceTotal,
                variableLoanDetails.ApplicableRate
            );

            return (totalDue, variableLoanDetails.ApplicableRate);
        }

        private async Task<decimal> RecordFixedLoanInterestAsync(
            DataContext ctx,
            List<Transaction> monthEndTransactions,
            List<TransactionCode> codes,
            MonthlyBalanceCalculator balanceCalculator,
            InvestmentAccount cashAccount,
            List<MonthEndBalance> previousMonthEndBalances,
            DateTime startDate,
            DateTime endDate,
            string description,
            int modifierId,
            int reconciledStatusId,
            int outstandingStatusId,
            int voidStatusId,
            CancellationToken cancellationToken
        )
        {
            var fixedLoanTransactionsByAccount = await GetFixedLoanTransactionsByAccountAsync(
                ctx,
                startDate,
                endDate,
                voidStatusId,
                cancellationToken
            );
            var loanInterestCode = codes.First(x =>
                x.Code == Constants.DataConstants.LoanInterestCode
            );
            decimal totalDue = 0;
            foreach (InvestmentAccount account in fixedLoanTransactionsByAccount)
            {
                // check account balance as of previous month end
                var previousBalance = GetPreviousMonthEndBalanceForAccount(
                    account.Id,
                    previousMonthEndBalances
                );
                // calculate monthly balance
                var averageBalance = balanceCalculator.CalculateAverageMonthlyBalance(
                    account.Transactions,
                    previousBalance
                );
                var interestRate = account.Rate.HasValue ? account.Rate.Value : 0;
                var interestDue = balanceCalculator.CalculateMonthlyInterestDue(
                    averageBalance,
                    interestRate
                );
                if (interestDue != 0)
                {
                    totalDue += Math.Abs(interestDue);
                    var cashWithdrawalTuple =
                        await _operationService.CreateSystemCashWithdrawalAsync(
                            ctx,
                            new Poco.Transactions.DepositTransaction
                            {
                                Account = account.Id,
                                Amount = Math.Abs(interestDue),
                                Code = loanInterestCode.Id,
                                Date = endDate.AddTicks(-1),
                                Description = description,
                                ModifierId = modifierId,
                                Status = reconciledStatusId,
                            },
                            cancellationToken
                        );

                    account.Balance += cashWithdrawalTuple.First.Amount;
                    cashAccount.Balance += cashWithdrawalTuple.Second.Amount;

                    monthEndTransactions.Add(cashWithdrawalTuple.First);
                    monthEndTransactions.Add(cashWithdrawalTuple.Second);

                    // update account balance and metadata
                    ctx.InvestmentAccounts.Update(account);
                }
            }

            return totalDue;
        }

        private async Task RecordManagementFeeAsync(
            DataContext ctx,
            List<Transaction> monthEndTransactions,
            List<TransactionCode> codes,
            decimal managementFee,
            InvestmentAccount cashAccount,
            DateTime monthEndDate,
            string description,
            int modifierId,
            int outstandingStatusId,
            CancellationToken cancellationToken
        )
        {
            // management fee
            var managementFeeCode = codes.First(x =>
                x.Code == Constants.DataConstants.ManagementFeeCode
            );
            var withdrawalCode = codes.First(x => x.Code == Constants.DataConstants.WithdrawalCode);
            // Horrible, but legacy implementation
            // assumes migrated data has account id 2 that belongs to Triton
            int managementInvestmentAccountId = 2;
            var managementInvestmentAccount = await ctx.InvestmentAccounts.FindAsync(
                [managementInvestmentAccountId],
                cancellationToken
            );

            var depositTuple = await _operationService.CreateDepositAsync(
                ctx,
                new Poco.Transactions.DepositTransaction
                {
                    Account = managementInvestmentAccountId,
                    Amount = managementFee,
                    Code = managementFeeCode.Id,
                    Date = monthEndDate.AddTicks(-1),
                    Description = description,
                    ModifierId = modifierId,
                    Status = outstandingStatusId,
                },
                cancellationToken
            );
#pragma warning disable CS8602 // Dereference of a possibly null reference.
            managementInvestmentAccount.Balance += managementFee;
#pragma warning restore CS8602 // Dereference of a possibly null reference.
            cashAccount.Balance += managementFee;

            monthEndTransactions.Add(depositTuple.First);
            monthEndTransactions.Add(depositTuple.Second);

            // create a check withdrawal
            await CreateAchWithdrawal(
                ctx,
                monthEndTransactions,
                managementInvestmentAccount,
                cashAccount,
                monthEndDate.AddTicks(-1),
                description,
                modifierId,
                outstandingStatusId,
                withdrawalCode.Id,
                managementFee,
                Constants.DataConstants.BankTransactionTypeDebit,
                cancellationToken
            );

            ctx.InvestmentAccounts.Update(managementInvestmentAccount);
        }

        private async Task<decimal> RecordInvestmentInterestAsync(
            DataContext ctx,
            List<Transaction> monthEndTransactions,
            List<TransactionCode> codes,
            MonthlyBalanceCalculator balanceCalculator,
            InvestmentAccount cashAccount,
            List<MonthEndBalance> previousMonthEndBalances,
            DateTime startDate,
            DateTime endDate,
            decimal interestRate,
            string description,
            int modifierId,
            int reconciledStatusId,
            int outstandingStatusId,
            int voidStatusId,
            CancellationToken cancellationToken
        )
        {
            var investmentTransactionsByAccount = await GetInvestmentTransactionsByAccountAsync(
                ctx,
                startDate,
                endDate,
                voidStatusId,
                false,
                cancellationToken
            );
            var investmentInterestCode = codes.First(x =>
                x.Code == Constants.DataConstants.InvestmentInterestCode
            );
            var withdrawalCode = codes.First(x => x.Code == Constants.DataConstants.WithdrawalCode);
            decimal totalDue = 0;
            // investment stuff
            foreach (InvestmentAccount account in investmentTransactionsByAccount)
            {
                // check account balance as of previous month end
                var previousBalance = GetPreviousMonthEndBalanceForAccount(
                    account.Id,
                    previousMonthEndBalances
                );
                // calculate monthly balance
                var averageBalance = balanceCalculator.CalculateAverageMonthlyBalance(
                    account.Transactions,
                    previousBalance
                );
                var interestDue = balanceCalculator.CalculateMonthlyInterestDue(
                    averageBalance,
                    account.Rate ?? interestRate
                );
                if (interestDue != 0)
                {
                    totalDue += interestDue;
                    var depositTuple = await _operationService.CreateDepositAsync(
                        ctx,
                        new Poco.Transactions.DepositTransaction
                        {
                            Account = account.Id,
                            Amount = interestDue,
                            Code = investmentInterestCode.Id,
                            Date = endDate.AddTicks(-1),
                            Description = description,
                            ModifierId = modifierId,
                            Status = reconciledStatusId,
                        },
                        cancellationToken
                    );

                    account.Balance += depositTuple.First.Amount;
                    cashAccount.Balance += depositTuple.Second.Amount;

                    monthEndTransactions.Add(depositTuple.First);
                    monthEndTransactions.Add(depositTuple.Second);

                    if (!string.IsNullOrEmpty(account.InterestType))
                    {
                        if (
                            account.InterestType.Equals(
                                Constants.DataConstants.AchInterestType,
                                StringComparison.InvariantCultureIgnoreCase
                            )
                        )
                        {
                            await CreateAchWithdrawal(
                                ctx,
                                monthEndTransactions,
                                account,
                                cashAccount,
                                endDate.AddTicks(-1),
                                description,
                                modifierId,
                                outstandingStatusId,
                                withdrawalCode.Id,
                                interestDue,
                                null,
                                cancellationToken
                            );
                        }
                        else if (
                            account.InterestType.Equals(
                                Constants.DataConstants.CheckInterestType,
                                StringComparison.InvariantCultureIgnoreCase
                            )
                        )
                        {
                            await CreateCheckWithdrawal(
                                ctx,
                                monthEndTransactions,
                                account,
                                cashAccount,
                                endDate,
                                description,
                                modifierId,
                                outstandingStatusId,
                                withdrawalCode.Id,
                                interestDue,
                                cancellationToken
                            );
                        }
                    }

                    // update account balance and metadata
                    ctx.InvestmentAccounts.Update(account);
                }
            }
            return totalDue;
        }

        private decimal GetPreviousMonthEndBalanceForAccount(
            int id,
            List<MonthEndBalance> previousMonthEndBalances
        )
        {
            var previousMonthEndBalance = previousMonthEndBalances.FirstOrDefault(x =>
                x.AccountId == id
            );
            if (previousMonthEndBalance is null)
            {
                return 0;
            }
            return previousMonthEndBalance.EndingBalance;
        }

        private async Task CreateCheckWithdrawal(
            DataContext ctx,
            List<Transaction> monthEndTransactions,
            InvestmentAccount account,
            InvestmentAccount cashAccount,
            DateTime endDate,
            string description,
            int modifierId,
            int outstandingStatusId,
            int withdrawalCodeId,
            decimal interestDue,
            CancellationToken cancellationToken
        )
        {
            var investor = await ctx.Investors.FindAsync([account.InvestorId], cancellationToken);
#pragma warning disable CS8602 // Dereference of a possibly null reference.
            var checkWithdrawalTuple = await _operationService.CreateCheckWithdrawalAsync(
                ctx,
                new Poco.Transactions.CheckTransaction
                {
                    Account = account.Id,
                    Amount = interestDue,
                    Code = withdrawalCodeId,
                    Date = endDate.AddTicks(-1),
                    Description = description,
                    ModifierId = modifierId,
                    Status = outstandingStatusId,
                    Payee = string.IsNullOrEmpty(investor?.CompanyName)
                        ? $"{investor.LastName}, {investor.FirstName}"
                        : investor.CompanyName,
                    Address = account.AccountInvestor.Address,
                    Apartment = account.AccountInvestor.Apartment,
                    City = account.AccountInvestor.City,
                    State = account.AccountInvestor.State,
                    Zip = account.AccountInvestor.Zip,
                    Country = account.AccountInvestor.Country,
                    Memo = description,
                },
                cancellationToken
            );
#pragma warning restore CS8602 // Dereference of a possibly null reference.

            account.Balance += checkWithdrawalTuple.First.Amount;
            cashAccount.Balance += checkWithdrawalTuple.Second.Amount;

            monthEndTransactions.Add(checkWithdrawalTuple.First);
            monthEndTransactions.Add(checkWithdrawalTuple.Second);

            await ctx.CheckRegisters.AddAsync(checkWithdrawalTuple.Check, cancellationToken);
        }

        private async Task CreateAchWithdrawal(
            DataContext ctx,
            List<Transaction> monthEndTransactions,
            InvestmentAccount account,
            InvestmentAccount cashAccount,
            DateTime endDate,
            string description,
            int modifierId,
            int outstandingStatusId,
            int withdrawalCodeId,
            decimal interestDue,
            string? bankTransactionType = null,
            CancellationToken cancellationToken = default
        )
        {
            var achWithdrawalTuple = await _operationService.CreateAchWithdrawalAsync(
                ctx,
                new Poco.Transactions.AchTransaction
                {
                    Account = account.Id,
                    Amount = interestDue,
                    Code = withdrawalCodeId,
                    Date = endDate,
                    Description = description,
                    ModifierId = modifierId,
                    Status = outstandingStatusId,
                    AchDetails = description,
                    BankTransactionType = bankTransactionType,
                },
                cancellationToken
            );
            account.Balance += achWithdrawalTuple.First.Amount;
            cashAccount.Balance += achWithdrawalTuple.Second.Amount;

            monthEndTransactions.Add(achWithdrawalTuple.First);
            monthEndTransactions.Add(achWithdrawalTuple.Second);
            await ctx.AchRegisters.AddAsync(achWithdrawalTuple.Ach, cancellationToken);
        }

        /// <inheritdoc/>
        public async Task<(
            decimal AverageBalance,
            decimal InterestDue
        )> GetFixedLoanInterestSummaryAsync(
            DateTime monthStartDate,
            DateTime monthEndDate,
            CancellationToken cancellationToken = default
        )
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var voidStatus = await ctx.TransactionStatuses.FirstAsync(
                    x => x.Description == Constants.DataConstants.VoidStatus,
                    cancellationToken
                );
                var transactionsByAccount = await GetFixedLoanTransactionsByAccountAsync(
                    ctx,
                    monthStartDate,
                    monthEndDate,
                    voidStatus.Id,
                    cancellationToken
                );

                var distinctAccounts = transactionsByAccount.Select(x => x.Id).Distinct().ToList();

                var previousMonth = monthStartDate.AddMonths(-1);

                var previousAccountBalances = await ctx
                    .MonthEndBalances.Where(x =>
                        distinctAccounts.Contains(x.AccountId)
                        && x.MonthEndProcess.Month == previousMonth.Month
                        && x.MonthEndProcess.Year == previousMonth.Year
                    )
                    .ToListAsync(cancellationToken);

                var balanceCalculator = new MonthlyBalanceCalculator(monthStartDate, monthEndDate);

                decimal interestDue = 0;
                decimal totalBalance = 0;

                foreach (InvestmentAccount account in transactionsByAccount)
                {
                    // check account balance as of previous month end
                    var previousBalance = GetPreviousMonthEndBalanceForAccount(
                        account.Id,
                        previousAccountBalances
                    );

                    if (account.Rate is null)
                    {
                        continue;
                    }

                    var averageBalance = balanceCalculator.CalculateAverageMonthlyBalance(
                        account.Transactions,
                        previousBalance
                    );
                    totalBalance += averageBalance;
                    interestDue += balanceCalculator.CalculateMonthlyInterestDue(
                        averageBalance,
                        account.Rate.Value
                    );
                }

                return (totalBalance, interestDue);
            }
        }

        /// <inheritdoc/>
        public async Task<(
            decimal AverageBalance,
            decimal ApplicableDue,
            decimal ApplicableRate
        )> GetVariableLoanInterestSummaryAsync(
            DateTime monthStartDate,
            DateTime monthEndDate,
            decimal interestDue,
            CancellationToken cancellationToken = default
        )
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var voidStatus = await ctx.TransactionStatuses.FirstAsync(
                    x => x.Description == Constants.DataConstants.VoidStatus,
                    cancellationToken
                );
                var transactionsByAccount = await GetVariableLoanTransactionsByAccountAsync(
                    ctx,
                    monthStartDate,
                    monthEndDate,
                    voidStatus.Id,
                    cancellationToken
                );

                var distinctAccounts = transactionsByAccount.Select(x => x.Id).Distinct().ToList();

                var previousMonth = monthStartDate.AddMonths(-1);

                var previousAccountBalances = await ctx
                    .MonthEndBalances.Where(x =>
                        distinctAccounts.Contains(x.AccountId)
                        && x.MonthEndProcess.Month == previousMonth.Month
                        && x.MonthEndProcess.Year == previousMonth.Year
                    )
                    .ToListAsync(cancellationToken);

                var balanceCalculator = new MonthlyBalanceCalculator(monthStartDate, monthEndDate);
                var variableLoanDues = GetVariableLoanRate(
                    interestDue,
                    transactionsByAccount,
                    previousAccountBalances,
                    balanceCalculator
                );

                interestDue = balanceCalculator.CalculateMonthlyInterestDue(
                    Math.Abs(variableLoanDues.AverageBalance),
                    variableLoanDues.ApplicableRate
                );

                return (
                    variableLoanDues.AverageBalance,
                    interestDue,
                    variableLoanDues.ApplicableRate
                );
            }
        }

        private (decimal AverageBalance, decimal ApplicableRate) GetVariableLoanRate(
            decimal interestDue,
            ICollection<InvestmentAccount> transactionsByAccount,
            List<MonthEndBalance> previousMonthBalances,
            MonthlyBalanceCalculator balanceCalculator
        )
        {
            decimal totalBalance = 0;
            foreach (InvestmentAccount account in transactionsByAccount)
            {
                // check account balance as of previous month end
                var previousBalance = GetPreviousMonthEndBalanceForAccount(
                    account.Id,
                    previousMonthBalances
                );

                var averageBalance = balanceCalculator.CalculateAverageMonthlyBalance(
                    account.Transactions,
                    previousBalance
                );
                totalBalance += averageBalance;
            }

            var applicableRate = balanceCalculator.CalculateDynamicInterestRate(
                Math.Abs(totalBalance),
                interestDue
            );

            return (totalBalance, applicableRate);
        }

        private async Task<ICollection<InvestmentAccount>> GetInvestmentTransactionsByAccountAsync(
            DataContext ctx,
            DateTime monthStartDate,
            DateTime monthEndDate,
            int voidStatusId,
            bool isSummary = true,
            CancellationToken cancellationToken = default
        )
        {
            var accountTransactions = await ctx
                .InvestmentAccounts
                // check account was open between month being run and open as of month end process being run
                .Where(x =>
                    x.AccountType == Constants.DataConstants.InvestmentAccountType
                    && x.StartDate < monthEndDate
                    && x.InvestorId != 1
                    && (
                        x.EndDate == null
                        || (isSummary ? x.EndDate >= DateTime.UtcNow : monthEndDate <= x.EndDate)
                    )
                ) //x.EndDate >= DateTime.UtcNow //monthEndDate <= x.EndDate
                .SelectMany(
                    x =>
                        x.Transactions.Where(t =>
                                t != null
                                && t.AccountId != Constants.DataConstants.CashAccountId
                                && t.StatusId != voidStatusId
                                && t.Date >= monthStartDate
                                && t.Date < monthEndDate
                            )
                            .DefaultIfEmpty(),
                    (a, b) => new InvestmentTransaction { Account = a, Transaction = b }
                )
                .ToListAsync(cancellationToken);

            return ComposeAccountTransactionSet(accountTransactions);
        }

        private async Task<ICollection<InvestmentAccount>> GetFixedLoanTransactionsByAccountAsync(
            DataContext ctx,
            DateTime monthStartDate,
            DateTime monthEndDate,
            int voidStatusId,
            CancellationToken cancellationToken
        )
        {
            var accountTransactions = await ctx
                .InvestmentAccounts
                // check account was open between month being run and open as of month end process being run
                .Where(x =>
                    x.AccountType == Constants.DataConstants.LoanAccountType
                    && x.Rate > 0
                    && x.StartDate < monthEndDate
                    && (x.EndDate == null || x.EndDate >= DateTime.UtcNow)
                )
                .SelectMany(
                    x =>
                        x.Transactions.Where(t =>
                                t != null
                                && t.AccountId != Constants.DataConstants.CashAccountId
                                && t.StatusId != voidStatusId
                                && t.Date >= monthStartDate
                                && t.Date < monthEndDate
                            )
                            .DefaultIfEmpty(),
                    (a, b) => new InvestmentTransaction { Account = a, Transaction = b }
                )
                .ToListAsync(cancellationToken);

            return ComposeAccountTransactionSet(accountTransactions);
        }

        private async Task<
            ICollection<InvestmentAccount>
        > GetVariableLoanTransactionsByAccountAsync(
            DataContext ctx,
            DateTime monthStartDate,
            DateTime monthEndDate,
            int voidStatusId,
            CancellationToken cancellationToken
        )
        {
            var accountTransactions = await ctx
                .InvestmentAccounts
                // check account was open between month being run and open as of month end process being run
                .Where(x =>
                    x.AccountType == Constants.DataConstants.LoanAccountType
                    && (x.Rate == null || x.Rate == 0)
                    && x.StartDate < monthEndDate
                    && (x.EndDate == null || x.EndDate >= DateTime.UtcNow)
                )
                .SelectMany(
                    x =>
                        x.Transactions.Where(t =>
                                t != null
                                && t.AccountId != Constants.DataConstants.CashAccountId
                                && t.StatusId != voidStatusId
                                && t.Date >= monthStartDate
                                && t.Date < monthEndDate
                            )
                            .DefaultIfEmpty(),
                    (a, b) => new InvestmentTransaction { Account = a, Transaction = b }
                )
                .ToListAsync(cancellationToken);

            return ComposeAccountTransactionSet(accountTransactions);
        }

        private ICollection<InvestmentAccount> ComposeAccountTransactionSet(
            List<InvestmentTransaction> accountTransactions
        )
        {
            List<InvestmentAccount> accounts = new List<InvestmentAccount>();
            foreach (InvestmentTransaction accountTransaction in accountTransactions)
            {
                var existingAccount = accounts.FirstOrDefault(x =>
                    x.Id == accountTransaction.Account.Id
                );
                if (existingAccount is null)
                {
                    existingAccount = accountTransaction.Account;
                    accounts.Add(existingAccount);
                }
            }
            return accounts;
        }

        [SuppressMessage(
            "Security",
            "EF1002:Risk of vulnerability to SQL injection.",
            Justification = "Sanitized input within system control"
        )]
        private async Task RegisterMonthEndTransactionsAsync(
            DataContext ctx,
            int processId,
            IEnumerable<int> transactionIds,
            CancellationToken cancellationToken = default
        )
        {
            await ctx.Database.ExecuteSqlRawAsync(
                $@"INSERT INTO [MonthEndProcessTransaction] ([MonthEndProcess], [Transaction]) VALUES 
                    {string.Join(",", transactionIds.Select(x => $"({processId}, {x})"))}",
                cancellationToken
            );
        }

        [SuppressMessage(
            "Security",
            "EF1002:Risk of vulnerability to SQL injection.",
            Justification = "Sanitized input within system control"
        )]
        private async Task InsertMonthEndBalancesAsyn(
            DataContext ctx,
            List<MonthEndBalance> monthEndBalances,
            CancellationToken cancellationToken
        )
        {
            await ctx.Database.ExecuteSqlRawAsync(
                $@"INSERT INTO [MonthEndBalance] ([MonthEndProcess], [Account], [StartingBalance], [EndingBalance]) VALUES 
                    {string.Join(",", monthEndBalances.Select(x => $"({x.MonthEndProcess.Id}, {x.InvestmentAccount.Id}, {Math.Round(x.StartingBalance, 2, MidpointRounding.AwayFromZero)}, {Math.Round(x.EndingBalance, 2, MidpointRounding.AwayFromZero)})"))}",
                cancellationToken
            );
        }

        private class InvestmentTransaction
        {
            public InvestmentAccount Account { get; set; }
            public Transaction? Transaction { get; set; }
        }
    }
}
