﻿using AutoMapper;
using GrapeCity.ActiveReports;
using GrapeCity.ActiveReports.Document.Section;
using Microsoft.EntityFrameworkCore;
using TIQuest.Api.Entities;
using TIQuest.Api.Enums;
using TIQuest.Api.Exports;
using TIQuest.Api.Interfaces.Factories;
using TIQuest.Api.Interfaces.Reports;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Services
{
    internal class CheckRegisterService : ICheckRegisterService
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;
        private readonly IMapper _mapper;
        private readonly IReportBuilderFactory _reportBuilderFactory;
        private readonly IExportEngineFactory _exportProvider;

        public CheckRegisterService(IDbContextFactory<DataContext> dbContextFactory, IMapper mapper, IReportBuilderFactory reportBuilderFactory, IExportEngineFactory exportProvider)
        {
            _contextFactory = dbContextFactory;
            _mapper = mapper;
            _reportBuilderFactory = reportBuilderFactory;
            _exportProvider = exportProvider;
        }

        /// <inheritdoc />
        public async Task<IEnumerable<Poco.Entities.CheckRegister>> GetAllAsync(CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var checkRegisters = await GetCheckRecordQueryable(ctx)
                                            .Include(x => x.Transaction.InvestmentAccount)
                                            .Include(x => x.Transaction.InvestmentAccount.AccountInvestor)
                                            .ToListAsync(cancellationToken);

                return _mapper.Map<IEnumerable<Poco.Entities.CheckRegister>>(checkRegisters);
            }
        }

        /// <inheritdoc />
        public async Task<Stream> GetChecksAsync(List<int> ids, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var checkRegisters = await ctx.CheckRegisters
                                            .Where(x => !x.IsPrinted && x.Number != null & ids.Contains(x.Id))
                                            .ToListAsync(cancellationToken);
                IReportBuilder reportBuilder = _reportBuilderFactory.GetReportBuilder(ReportType.Check);
                await reportBuilder.PreloadDataAsync(checkRegisters.Select(x => x.Id), cancellationToken);
                SectionReport combinedReport = new SectionReport();
                // update check registers as printed
                foreach (var checkRegister in checkRegisters)
                {
                    SectionReport report = await reportBuilder.BuildAsync(checkRegister.Id, cancellationToken);
                    combinedReport.Document.Pages.AddRange((PagesCollection)report.Document.Pages.Clone());
                    checkRegister.IsPrinted = true;
                    ctx.CheckRegisters.Update(checkRegister);
                }

                if (combinedReport.Document.Pages.Count > 0)
                {
                    using (combinedReport)
                    {
                        Stream fileStream = GetReportStream(combinedReport);
                        await ctx.SaveChangesAsync(cancellationToken);
                        return fileStream;
                    }
                }
                return new MemoryStream();
            }
        }

        /// <inheritdoc />
        public async Task UpdateCheckNumberAsync(int nextCheckNumber, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var checkRegisters = await GetCheckRecordQueryable(ctx)
                                           .OrderBy(x => x.Date)
                                           .ToListAsync(cancellationToken);

                int nextNumber = nextCheckNumber;
                foreach (var checkRegister in checkRegisters)
                {
                    checkRegister.Number = nextNumber;
                    nextNumber++;
                    ctx.CheckRegisters.Update(checkRegister);
                }

                var setting = await ctx.Settings.FirstAsync(x => x.Key == Constants.DataConstants.NextCheckNumberKey);

                setting.Value = nextNumber.ToString();
                ctx.Settings.Update(setting);

                await ctx.SaveChangesAsync(cancellationToken);
            }
        }

        private static IQueryable<CheckRegister> GetCheckRecordQueryable(DataContext ctx)
        {
            return ctx.CheckRegisters
                    .Include(x => x.Transaction)
                    .Include(x => x.Transaction.TransactionStatus)
                    .Where(x => !x.IsPrinted
                    && x.Transaction.TransactionStatus.Description != Constants.DataConstants.VoidStatus);
        }

        private Stream GetReportStream(SectionReport report)
        {
            using (report)
            {
                IExportEngine exportEngine = _exportProvider.GetExportEngine(ExportFormat.Pdf);
                return exportEngine.Export(report);
            }
        }
    }
}
