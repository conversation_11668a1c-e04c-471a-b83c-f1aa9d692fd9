﻿using System.Collections.Generic;
using System.Linq;
using System.Xml;
using DocumentFormat.OpenXml.InkML;
using GrapeCity.ActiveReports;
using GrapeCity.ActiveReports.Document.Section;
using GrapeCity.ActiveReports.Extensibility.Rendering.Components;
using GrapeCity.ActiveReports.PageReportModel;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using TIQuest.Api.DTO.Request.Reports;
using TIQuest.Api.Entities;
using TIQuest.Api.Enums;
using TIQuest.Api.Exports;
using TIQuest.Api.Extensions;
using TIQuest.Api.Interfaces.DataProviders;
using TIQuest.Api.Interfaces.Factories;
using TIQuest.Api.Interfaces.Managers;
using TIQuest.Api.Interfaces.Reports;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Models;
using TIQuest.Api.Options;
using TIQuest.Api.Poco;
using TIQuest.Api.Poco.Reports;
using TIQuest.Api.Reports.Filters;

namespace TIQuest.Api.Services
{
    internal class ReportService : ControllerBase, IReportService
    {
        private readonly IReportBuilderFactory _reportBuilderFactory;
        private readonly IExportEngineFactory _exportProvider;
        private readonly ICsvManager _csvManager;
        private readonly IDbContextFactory<DataContext> _dbContextFactory;
        private readonly IEmailService _emailService;
        private readonly EmailSettings _emailOptions;
        private readonly IEmailLogService _emailLogService;

        private readonly IStatement1099DataProvider _statement1099DataProvider;
        private readonly IForm1096DataProvider _statement1096DataProvider;

        private readonly ITransactionDataProvider _transactionDataProvider;

        public ReportService(
            IReportBuilderFactory reportBuilderFactory,
            IExportEngineFactory exportFactory,
            ICsvManager csvManager,
            IDbContextFactory<DataContext> dbContextFactory,
            IEmailService emailService,
            IEmailLogService emailLogService,
            IStatement1099DataProvider statement1099DataProvider,
            IForm1096DataProvider statement1096DataProvider,
            ITransactionDataProvider transactionDataProvider,
            IOptions<EmailSettings> emailOptions
        )
        {
            _reportBuilderFactory = reportBuilderFactory;
            _exportProvider = exportFactory;
            _csvManager = csvManager;
            _dbContextFactory = dbContextFactory;
            _emailService = emailService;
            _emailOptions = emailOptions.Value;
            _emailLogService = emailLogService;
            _statement1099DataProvider = statement1099DataProvider;
            _statement1096DataProvider = statement1096DataProvider;
            _transactionDataProvider = transactionDataProvider;
        }

        /// <inheritdoc />
        public async Task<Stream> GetStatement1099Async(
            Statement1099Filter filters,
            CancellationToken cancellationToken = default
        )
        {
            var statement1099s = await _statement1099DataProvider.GetReportDataAsync(
                filters,
                cancellationToken
            );
            return await _csvManager.WriteAsync(statement1099s);
        }

        /// <inheritdoc />
        public async Task<Stream> GetForm1096Async(
            Form1096Filter filter,
            CancellationToken cancellationToken = default
        )
        {
            IReportBuilder reportBuilder = _reportBuilderFactory.GetReportBuilder(
                ReportType.Form1096
            );
            await reportBuilder.PreloadDataAsync(filter, cancellationToken);
            using SectionReport report = await reportBuilder.BuildAsync(filter, cancellationToken);
            IExportEngine exportEngine = _exportProvider.GetExportEngine(filter.Format);
            return exportEngine.Export(report);
        }

        /// <inheritdoc />
        public async Task<Stream?> GetClientAccountStatementAsync(
            ClientAccountFilter filter,
            TIQuest.Api.Enums.UserRole userRole,
            CancellationToken cancellationToken = default
        )
        {
            var applicableAccounts = await GetApplicableAccountsAsync(
                filter,
                userRole,
                cancellationToken
            );
            if (applicableAccounts.Any())
            {
                var accountEmails = await GetAccountEmails(applicableAccounts, cancellationToken);
                IReportBuilder reportBuilder = _reportBuilderFactory.GetReportBuilder(
                    ReportType.ClientAccount
                );
                await reportBuilder.PreloadDataAsync(filter, cancellationToken);

                var settings = await GetSettingsAsync(cancellationToken);
                var emailSubject = settings
                    .FirstOrDefault(x => x.Key == "StatementEmailSubject")
                    ?.Value;
                var emailBody = settings.FirstOrDefault(x => x.Key == "StatementEmailBody")?.Value;

                var combinedReport = new SectionReport();

                foreach (int accountId in applicableAccounts)
                {
                    // TODO: Investigate if this can be parallelized
                    SectionReport report = await reportBuilder.BuildAsync(
                        accountId,
                        cancellationToken
                    );
                    if (filter.Email)
                    {
                        var emailUsers = accountEmails.Where(x => x.Id == accountId);
                        var hasEmailUsers = emailUsers.Any();
                        if (emailUsers.First().SendEmail && hasEmailUsers)
                        {
                            await EmailClientReportAsync(
                                emailSubject,
                                emailBody,
                                report,
                                emailUsers
                            );
                        }

                        if (emailUsers.First().SendMail)
                        {
                            combinedReport.Document.Pages.AddRange(
                                (PagesCollection)report.Document.Pages.Clone()
                            );
                        }
                    }
                    else
                    {
                        combinedReport.Document.Pages.AddRange(
                            (PagesCollection)report.Document.Pages.Clone()
                        );
                    }
                }

                if (combinedReport.Document.Pages.Count > 0)
                {
                    using (combinedReport)
                    {
                        return GetReportStream(combinedReport);
                    }
                }
            }
            return null;
        }

        private async Task EmailClientReportAsync(
            string? emailSubject,
            string? emailBody,
            SectionReport report,
            IEnumerable<InvestmentAccount> emailUsers
        )
        {
            var emailStream = GetReportStream(report);
            string fileName =
                $@"{DateTime.Now.Month}{DateTime.Now.Day}{DateTime.Now.Year}{DateTime.Now.Minute}{DateTime.Now.Second}{DateTime.Now.Millisecond}.pdf";
            using (emailStream)
            {
                foreach (var user in emailUsers)
                {
                    List<Poco.Entities.User> recipients = new List<Poco.Entities.User>();
                    if (user.AccountInvestor.Email != null)
                    {
                        recipients.Add(
                            new Poco.Entities.User
                            {
                                FirstName = user.AccountInvestor.FirstName,
                                Email = user.AccountInvestor.Email,
                            }
                        );
                    }

                    if (
                        user.Email != null
                        && (recipients.Count > 0 && user.AccountInvestor.Email != user.Email)
                    )
                    {
                        recipients.Add(
                            new Poco.Entities.User { FirstName = user.Name, Email = user.Email }
                        );
                    }

                    emailStream.Position = 0;
                    foreach (var recipient in recipients)
                    {
                        // send email
                        var blobId = await _emailService.SendEmailAsync(
                            _emailOptions,
                            recipient,
                            emailSubject,
                            emailBody,
                            false,
                            new List<(string Name, Stream Content)> { new(fileName, emailStream) }
                        );

                        await _emailLogService.AddLogAsync(
                            new Poco.Entities.EmailLog
                            {
                                Date = DateOnly.FromDateTime(DateTime.UtcNow),
                                Email = recipient.Email,
                                Subject = emailSubject,
                                Status = EmailLogStatus.Queued.ToString(),
                                Location = blobId,
                            }
                        );
                    }
                }
            }
        }

        private Stream GetReportStream(SectionReport sectionReport)
        {
            IExportEngine exportEngine = _exportProvider.GetExportEngine(ExportFormat.Pdf);
            return exportEngine.Export(sectionReport);
        }

        private async Task<IEnumerable<Setting>> GetSettingsAsync(
            CancellationToken cancellationToken
        )
        {
            using (var ctx = await _dbContextFactory.CreateDbContextAsync(cancellationToken))
            {
                return await ctx.Settings.ToListAsync(cancellationToken);
            }
        }

        private async Task<IEnumerable<InvestmentAccount>> GetAccountEmails(
            IEnumerable<int> applicableAccounts,
            CancellationToken cancellationToken
        )
        {
            using (var ctx = await _dbContextFactory.CreateDbContextAsync(cancellationToken))
            {
                return await ctx
                    .InvestmentAccounts.Include(x => x.AccountInvestor)
                    .Where(x => applicableAccounts.Contains(x.Id))
                    .ToListAsync(cancellationToken);
            }
        }

        private async Task<IEnumerable<UserAccountStatement>> GetUserAccountStatements(
            IEnumerable<int> applicableAccounts,
            CancellationToken cancellationToken
        )
        {
            using (var ctx = await _dbContextFactory.CreateDbContextAsync(cancellationToken))
            {
                return await ctx
                    .UserAccountStatements.Include(x => x.AssociatedUser)
                    .Where(x =>
                        applicableAccounts.Contains(x.AccountId)
                        && x.AssociatedUser.UserRole.Description
                            == Constants.RoleConstants.ClientRole
                    )
                    .ToListAsync(cancellationToken);
            }
        }

        private async Task<IEnumerable<int>> GetApplicableAccountsAsync(
            ClientAccountFilter filter,
            TIQuest.Api.Enums.UserRole userRole,
            CancellationToken cancellationToken
        )
        {
            using (var ctx = await _dbContextFactory.CreateDbContextAsync(cancellationToken))
            {
                var queryable = ctx
                    .UserAccountPermissions.Include(x => x.InvestmentAccount)
                    .Include(x => x.InvestmentAccount.AccountInvestor)
                    .Where(x =>
                        x.UserId == filter.UserId
                        && x.InvestmentAccount.InvestorId
                            != Constants.DataConstants.SystemInvestorId
                    );

                bool isClientUser = userRole == Enums.UserRole.Client;

                if (filter.Email)
                {
                    queryable = queryable.Where(x =>
                        x.InvestmentAccount.SendEmail || x.InvestmentAccount.SendMail
                    );
                }
                else if (!isClientUser && filter.Clients.Count == 0)
                {
                    queryable = queryable.Where(x => x.InvestmentAccount.SendMail);
                }

                if (filter.Clients.Count > 0)
                {
                    queryable = queryable.Where(x =>
                        filter.Clients.Contains(x.InvestmentAccount.InvestorId)
                    );
                }

                if (filter.OnlyActiveClients)
                {
                    queryable = queryable.Where(x => x.InvestmentAccount.AccountInvestor.IsActive);
                }
                else
                {
                    queryable = queryable.Where(x => !x.InvestmentAccount.AccountInvestor.IsActive);
                }

                if (filter.Accounts.Count > 0)
                {
                    queryable = queryable.Where(x =>
                        filter.Accounts.Contains(x.InvestmentAccountId)
                    );
                }

                if (filter.OnlyActiveAccounts)
                {
                    queryable = queryable.Where(x =>
                        x.InvestmentAccount.StartDate <= filter.EndDate
                        && (
                            x.InvestmentAccount.EndDate == null
                            || x.InvestmentAccount.EndDate > DateTime.UtcNow
                        )
                    );
                }
                else
                {
                    queryable = queryable.Where(x => x.InvestmentAccount.EndDate < DateTime.UtcNow);
                }
                if (!string.IsNullOrWhiteSpace(filter.AccountType))
                {
                    queryable = queryable.Where(x =>
                        x.InvestmentAccount.AccountType == filter.AccountType
                    );
                }

                var sql = queryable.Select(x => x.InvestmentAccountId).Distinct().ToQueryString();
                return await queryable
                    .Select(x => x.InvestmentAccountId)
                    .Distinct()
                    .ToListAsync(cancellationToken);
            }
        }

        public async Task<IEnumerable<TransactionStatementRecord>> GetStatementDetailsAsync(
            int month,
            int year,
            string accountType,
            CancellationToken cancellationToken
        )
        {
            DateTime startDate = new DateTime(year, month, 1);
            DateTime endDate = startDate.AddMonths(1);
            // get all valid transaction for the supplied month.
            using (var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken))
            {
                var transactions = await context
                    .Transactions.Include(x => x.InvestmentAccount)
                    .Include(x => x.TransactionStatus)
                    .Include(x => x.TransactionCode)
                    .Where(x =>
                        x.Date >= startDate
                        && x.Date < endDate
                        && x.InvestmentAccount.Id != Constants.DataConstants.CashAccountId
                        && x.InvestmentAccount.AccountType == accountType
                    )
                    .ToListAsync(cancellationToken);

                var investmentAccounts = await context
                    .InvestmentAccounts.Include(x => x.AccountInvestor)
                    .Where(x =>
                        x.Id > 3 // ignore system accounts
                        && x.AccountType == accountType
                        && x.StartDate < endDate
                        && (x.EndDate == null || x.EndDate > startDate)
                    )
                    .ToListAsync(cancellationToken);

                var distinctAccounts = investmentAccounts.Select(x => x.Id);

                var previousMonthDate = startDate.AddDays(-1);
                var previousMonthBalances = await context
                    .MonthEndBalances.Where(x =>
                        x.MonthEndProcess.Month == previousMonthDate.Month
                        && x.MonthEndProcess.Year == previousMonthDate.Year
                        && distinctAccounts.Contains(x.AccountId)
                    )
                    .ToListAsync(cancellationToken);

                var transactionByAccount = transactions.GroupBy(x => x.AccountId);

                var transactionStatements = new List<TransactionStatementRecord>();

                foreach (var investmentAccount in investmentAccounts)
                {
                    var transactionGroup = transactionByAccount.FirstOrDefault(x =>
                        x.Key == investmentAccount.Id
                    );

                    var startingBalance =
                        previousMonthBalances
                            .FirstOrDefault(x => x.AccountId == investmentAccount.Id)
                            ?.EndingBalance ?? 0;

                    // No transactions for this account.
                    if (transactionGroup is null)
                    {
                        transactionStatements.Add(
                            new TransactionStatementRecord
                            {
                                Id = investmentAccount.Id,
                                Name = investmentAccount.Name,
                                StartingBalance = startingBalance,
                                Interest = 0,
                                Debit = 0,
                                Credit = 0,
                                EndingBalance = startingBalance,
                                AccountNumber = investmentAccount.AccountNumber,
                                Investor = new TransactionInvestor
                                {
                                    FirstName = investmentAccount.AccountInvestor.FirstName,
                                    LastName = investmentAccount.AccountInvestor.LastName,
                                    CompanyName = investmentAccount.AccountInvestor.CompanyName,
                                },
                            }
                        );
                    }
                    else
                    {
                        var interestTransactions = transactionGroup.Where(x =>
                            x.TransactionCode.Code == Constants.DataConstants.LoanInterestCode
                            || x.TransactionCode.Code
                                == Constants.DataConstants.InvestmentInterestCode
                        );

                        var deposits = transactionGroup
                            .Where(x =>
                                (
                                    x.TransactionCode.Code == Constants.DataConstants.DepositCode
                                    || x.TransactionCode.Code
                                        == Constants.DataConstants.LoanPaymentCode
                                )
                                || (
                                    x.Amount < 0
                                    && x.TransactionCode.Code
                                        == Constants.DataConstants.VoidAdjustmentCode
                                )
                                || (
                                    x.Amount > 0
                                    && x.TransactionCode.Code
                                        == Constants.DataConstants.TransferCode
                                )
                                || (
                                    x.Amount > 0
                                    && x.TransactionCode.Code
                                        == Constants.DataConstants.LoanTransferCode
                                )
                            )
                            .Sum(x => x.Amount);

                        var withdrawals = transactionGroup
                            .Where(x =>
                                (
                                    x.TransactionCode.Code == Constants.DataConstants.WithdrawalCode
                                    || x.TransactionCode.Code
                                        == Constants.DataConstants.LoanAdvanceCode
                                )
                                || (
                                    x.Amount > 0
                                    && x.TransactionCode.Code
                                        == Constants.DataConstants.VoidAdjustmentCode
                                )
                                || (
                                    x.Amount < 0
                                    && x.TransactionCode.Code
                                        == Constants.DataConstants.TransferCode
                                )
                                || (
                                    x.Amount < 0
                                    && x.TransactionCode.Code
                                        == Constants.DataConstants.LoanTransferCode
                                )
                            )
                            .Sum(x => x.Amount);

                        var interestDue = interestTransactions.Sum(x => x.Amount);

                        var endingBalance = 0M;

                        endingBalance = startingBalance + withdrawals + deposits + interestDue;

                        transactionStatements.Add(
                            new TransactionStatementRecord
                            {
                                Id = transactionGroup.Key,
                                Name = investmentAccount.Name,
                                StartingBalance = startingBalance,
                                Interest = interestDue,
                                Debit = withdrawals,
                                Credit = deposits,
                                EndingBalance = endingBalance,
                                AccountNumber = investmentAccount.AccountNumber,
                                Investor = new TransactionInvestor
                                {
                                    FirstName = investmentAccount.AccountInvestor.FirstName,
                                    LastName = investmentAccount.AccountInvestor.LastName,
                                    CompanyName = investmentAccount.AccountInvestor.CompanyName,
                                },
                            }
                        );
                    }
                }

                return transactionStatements;
            }
        }

        public async Task<Stream?> GetStatementReportAsync(
            int month,
            int year,
            string accountType,
            CancellationToken cancellationToken = default
        )
        {
            IReportBuilder reportBuilder = _reportBuilderFactory.GetReportBuilder(
                ReportType.AccountStatement
            );
            var reportFilter = new StatementFilter(month, year, accountType);
            await reportBuilder.PreloadDataAsync(reportFilter, cancellationToken);
            SectionReport report = await reportBuilder.BuildAsync(reportFilter, cancellationToken);
            using (report)
            {
                IExportEngine exportEngine = _exportProvider.GetExportEngine(ExportFormat.Pdf);
                return exportEngine.Export(report);
            }
        }

        public async Task<IEnumerable<ClientDetailReport>> GetClientReportDetailsAsync(
            CancellationToken cancellationToken = default
        )
        {
            using (var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken))
            {
                var investmentAccounts = await context
                    .InvestmentAccounts.Include(x => x.AccountInvestor)
                    .Where(x => x.Id > 3) // ignore migrated legacy system accounts
                    .ToListAsync(cancellationToken);

                var uniqueAccounts = investmentAccounts.Select(x => x.Id).Distinct();

                var statements = await context
                    .UserAccountStatements.Include(x => x.AssociatedUser)
                    .Where(x => uniqueAccounts.Contains(x.AccountId))
                    .OrderBy(x => x.AssociationDate)
                    .ToListAsync(cancellationToken);

                var clientDetailReports = new List<ClientDetailReport>();
                foreach (var investmentAccount in investmentAccounts)
                {
                    clientDetailReports.Add(
                        new ClientDetailReport
                        {
                            AccountId = investmentAccount.Id,
                            AccountName = investmentAccount.Name,
                            FirstName = investmentAccount.AccountInvestor.FirstName,
                            LastName = investmentAccount.AccountInvestor.LastName,
                            CompanyName = investmentAccount.AccountInvestor.CompanyName,
                            TaxNumber = investmentAccount.AccountInvestor.TaxNumber?.FormatTaxID(1),
                            AccountNumber = investmentAccount.AccountNumber,
                            IsActive =
                                investmentAccount.StartDate <= DateTimeOffset.UtcNow
                                && (
                                    investmentAccount.EndDate is null
                                    || investmentAccount.EndDate > DateTimeOffset.UtcNow
                                ),
                            Type = investmentAccount.AccountInvestor.Type,
                            AccountType = investmentAccount.AccountType,
                            UserEmail = statements
                                .FirstOrDefault(x => x.AccountId == investmentAccount.Id)
                                ?.AssociatedUser.Email,
                            ClientEmail = investmentAccount.AccountInvestor.Email,
                            AccountEmail = investmentAccount.Email,
                            PhoneNumber =
                                investmentAccount.AccountInvestor.Mobile
                                ?? investmentAccount.AccountInvestor.OfficePhone
                                ?? investmentAccount.AccountInvestor.HomePhone
                                ?? investmentAccount.AccountInvestor.Fax,
                            Report1099Name = investmentAccount.Report1099Name,
                            Address = investmentAccount.AccountInvestor.Address,
                            Apartment = investmentAccount.AccountInvestor.Apartment,
                            City = investmentAccount.AccountInvestor.City,
                            State = investmentAccount.AccountInvestor.State,
                            Zip = investmentAccount.AccountInvestor.Zip,
                            Country = investmentAccount.AccountInvestor.Country,
                        }
                    );
                }

                return clientDetailReports;
            }
        }

        public async Task<Stream> GetClientReportStatementAsync(
            CancellationToken cancellationToken = default
        )
        {
            var clientDetailReports = await GetClientReportDetailsAsync(cancellationToken);
            var clientDetailReportsList = new List<ClientDetailReport>();
            using (var ctx = await _dbContextFactory.CreateDbContextAsync(cancellationToken))
            {
                var accounts = clientDetailReports.Select(x => x.AccountId).Distinct().ToList();
                // get all the partnerships
                var partnerships = await ctx
                    .PartnershipOwners.Include(x => x.Partnership)
                    .Where(x => accounts.Contains(x.AccountId))
                    .ToListAsync(cancellationToken);

                foreach (var clientDetailReport in clientDetailReports)
                {
                    var matchingPartnerships = partnerships.Where(x =>
                        x.AccountId == clientDetailReport.AccountId
                    );
                    var matchingCount = matchingPartnerships.Count();
                    if (matchingCount == 0)
                    {
                        clientDetailReportsList.Add(clientDetailReport);
                    }
                    else
                    {
                        // add duplicate records for each partnership matched
                        foreach (var partnership in matchingPartnerships)
                        {
                            var newClientDetailReport = new ClientDetailReport
                            {
                                AccountId = clientDetailReport.AccountId,
                                FirstName = clientDetailReport.FirstName,
                                LastName = clientDetailReport.LastName,
                                CompanyName = clientDetailReport.CompanyName,
                                TaxNumber = clientDetailReport.TaxNumber,
                                AccountNumber = clientDetailReport.AccountNumber,
                                IsActive = clientDetailReport.IsActive,
                                Type = clientDetailReport.Type,
                                AccountType = clientDetailReport.AccountType,
                                UserEmail = clientDetailReport.UserEmail,
                                ClientEmail = clientDetailReport.ClientEmail,
                                AccountEmail = clientDetailReport.AccountEmail,
                                PhoneNumber = clientDetailReport.PhoneNumber,
                                Report1099Name = clientDetailReport.Report1099Name,
                                Address = clientDetailReport.Address,
                                Apartment = clientDetailReport.Apartment,
                                City = clientDetailReport.City,
                                State = clientDetailReport.State,
                                Zip = clientDetailReport.Zip,
                                Country = clientDetailReport.Country,
                                AssociatedPartnership = partnership.Partnership.Name,
                            };
                            clientDetailReportsList.Add(newClientDetailReport);
                        }
                    }
                }
            }

            var stream = await _csvManager.WriteAsync(
                clientDetailReportsList.OrderBy(x => x.AccountId)
            );
            return stream;
        }

        public async Task<Stream> GetTransactionStatementAsync(
            int userId,
            DateOnly startDate,
            DateOnly endDate,
            ExportFormat format,
            CancellationToken cancellationToken = default
        )
        {
            if (format == ExportFormat.Pdf)
            {
                IReportBuilder reportBuilder = _reportBuilderFactory.GetReportBuilder(
                    ReportType.Transactions
                );
                var reportFilter = new TransactionFilter(userId, startDate, endDate);
                await reportBuilder.PreloadDataAsync(reportFilter, cancellationToken);
                SectionReport report = await reportBuilder.BuildAsync(
                    reportFilter,
                    cancellationToken
                );
                using (report)
                {
                    IExportEngine exportEngine = _exportProvider.GetExportEngine(format);
                    return exportEngine.Export(report);
                }
            }
            else if (format == ExportFormat.Csv)
            {
                var transactions = await _transactionDataProvider.GetTransactionWithBalancesAsync(
                    userId,
                    new DateTime(startDate, TimeOnly.MinValue),
                    new DateTime(endDate, TimeOnly.MaxValue),
                    true,
                    cancellationToken
                );

                return await _csvManager.WriteAsync(transactions);
            }
            throw new NotSupportedException($"Export format {format} not supported");
        }

        public async Task<OutOfBalanceReport> GetOutOfBalance(
            CancellationToken cancellationToken = default
        )
        {
            var loanBalance = await GetAccountBalanceAsync("Loan", cancellationToken);
            var investmentBalance = await GetAccountBalanceAsync("Investment", cancellationToken);

            decimal cashBalance = 0;
            using (var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken))
            {
                cashBalance =
                    await context
                        .Transactions.AsNoTracking()
                        .Where(transaction => transaction.AccountId == 1)
                        .Select(transaction => transaction.Amount)
                        .SumAsync(amount => (decimal?)amount, cancellationToken) ?? 0m;
            }

            var outOfBalance = cashBalance - (loanBalance + investmentBalance);

            return new OutOfBalanceReport()
            {
                LoanBalance = loanBalance,
                InvestmentBalance = investmentBalance,
                CashBalance = cashBalance,
                OutOfBalance = outOfBalance,
            };
        }

        private async Task<decimal> GetAccountBalanceAsync(
            string accountType,
            CancellationToken cancellationToken
        )
        {
            using (var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken))
            {
                var accountBalance =
                    await context
                        .Transactions.AsNoTracking()
                        .Where(transaction => transaction.AccountId != 1)
                        .Select(transaction => new { transaction.Amount, transaction.AccountId })
                        .Join(
                            context
                                .InvestmentAccounts.AsNoTracking()
                                .Where(account => account.AccountType == accountType),
                            transaction => transaction.AccountId,
                            investmentAccount => investmentAccount.Id,
                            (transaction, investmentAccount) => transaction.Amount
                        )
                        .SumAsync(amount => (decimal?)amount, cancellationToken) ?? 0m;

                return accountBalance;
            }
        }

        public async Task<CashAccountStatementReport> GetCashAccountStatement(
            CashAccountStatementRequest request,
            CancellationToken cancellationToken = default
        )
        {
            var additionsBalance = 0.0m;
            var subtractionsBalance = 0.0m;
            var beginningBalance = 0.0m;
            var endingBalance = 0.0m;
            using (var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken))
            {
                var transactions = await context
                    .Transactions.Include(transaction => transaction.TransactionStatus)
                    .AsNoTracking()
                    .Where(transaction => transaction.AccountId == 1)
                    .ToListAsync(cancellationToken);

                string[] bankTransactionTypes =
                {
                    Constants.DataConstants.BankTransactionTypeCredit,
                    Constants.DataConstants.BankTransactionTypeDebit,
                };

                var filteredTransactions = transactions
                    .Where(transaction =>
                        (
                            !string.IsNullOrWhiteSpace(transaction.BankTransactionType)
                            && bankTransactionTypes.Contains(transaction.BankTransactionType)
                        )
                        && transaction.Date >= request.BeginningDate.ToDateTime(TimeOnly.MinValue)
                        && transaction.Date <= request.EndingDate.ToDateTime(TimeOnly.MaxValue)
                    )
                    .ToList();

                additionsBalance = filteredTransactions
                    .Where(t =>
                        t.TransactionStatus.Description != Constants.DataConstants.VoidStatus
                        && t.BankTransactionType
                            == Constants.DataConstants.BankTransactionTypeCredit
                    )
                    .Sum(t => t.Amount);

                subtractionsBalance = filteredTransactions
                    .Where(t =>
                        t.TransactionStatus.Description != Constants.DataConstants.VoidStatus
                        && t.BankTransactionType == Constants.DataConstants.BankTransactionTypeDebit
                    )
                    .Sum(t => Math.Abs(t.Amount));

                beginningBalance = transactions
                    .Where(t => t.Date <= request.BeginningDate.ToDateTime(TimeOnly.MaxValue))
                    .Sum(t => t.Amount);

                endingBalance = transactions
                    .Where(t => t.Date <= request.EndingDate.ToDateTime(TimeOnly.MaxValue))
                    .Sum(t => t.Amount);
            }

            return new CashAccountStatementReport()
            {
                AdditionsBalance = additionsBalance,
                SubtractionsBalance = subtractionsBalance * -1,
                BeginningBalance = beginningBalance,
                EndingBalance = endingBalance,
            };
        }

        /// <inheritdoc/>
        public async Task<Stream> GetDistributionTransactionsPdfAsync(
            int distributionId, 
            CancellationToken cancellationToken = default)
        {
            // Use the existing report builder infrastructure with the new distribution transaction report
            IReportBuilder reportBuilder = _reportBuilderFactory.GetReportBuilder(ReportType.DistributionTransaction);
            var filter = new DistributionTransactionFilter(distributionId, ExportFormat.Pdf);
            
            await reportBuilder.PreloadDataAsync(filter, cancellationToken);
            using SectionReport report = await reportBuilder.BuildAsync(filter, cancellationToken);
            
            IExportEngine exportEngine = _exportProvider.GetExportEngine(ExportFormat.Pdf);
            return exportEngine.Export(report);
        }
    }
}
