﻿using AutoMapper;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using System.Text;
using TIQuest.Api.DTO.Request;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.Managers;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Services
{
    internal class UserService : IUserService
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;
        private readonly IHasher _hasher;
        private readonly IMapper _mapper;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="dbContextFactory">Instance of <see cref="IDbContextFactory{TContext}"/></param>
        /// <param name="hasher">Instance of <see cref="IHasher"/></param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        public UserService(IDbContextFactory<DataContext> dbContextFactory, IHasher hasher, IMapper mapper)
        {
            _contextFactory = dbContextFactory;
            _hasher = hasher;
            _mapper = mapper;
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<Poco.Entities.User>> GetAllAsync(bool includeInactive, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                IQueryable<Poco.Entities.User> queryable = GetUserQueryable(ctx);
                if (!includeInactive)
                {
                    queryable = queryable.Where(x => x.IsActive);
                }
                return await queryable.ToListAsync(cancellationToken);
            }
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.User?> FindByEmailAsync(string email, bool includeInactive, CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                IQueryable<Poco.Entities.User> queryable = GetUserQueryable(ctx).Where(x => x.Email == email);
                if (!includeInactive)
                {
                    queryable = queryable.Where(x => x.IsActive);
                }
                Poco.Entities.User? dbUser = await queryable.FirstOrDefaultAsync(cancellationToken);

                return dbUser;
            }
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.User?> FindByIdAsync(int id, bool includeInactive, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                IQueryable<Poco.Entities.User> queryable = GetUserQueryable(ctx).Where(x => x.Id == id);
                if (!includeInactive)
                {
                    queryable = queryable.Where(x => x.IsActive);
                }
                Poco.Entities.User? dbUser = await queryable.FirstOrDefaultAsync(cancellationToken);
                // keyless can not be retrieved together.
                // So, we need to make another call to get these details
                if (dbUser is not null)
                {
                    dbUser.Statements = await ctx.UserAccountStatements
                                                .Where(x => x.UserId == id)
                                                .Select(x => new Poco.Entities.UserStatement
                                                {
                                                    Account = x.AccountId,
                                                    Email = x.Email,
                                                    Mail = x.Mail
                                                })
                                                .ToListAsync(cancellationToken);

                    dbUser.Accounts = await ctx.UserAccountPermissions
                                                .Where(x => x.UserId == id)
                                                .Select(x => x.InvestmentAccountId)
                                                .ToListAsync(cancellationToken);
                }

                return dbUser;
            }
        }

        /// <inheritdoc/>
        public async Task UpdatePasswordAsync(int id, string password, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                User dbUser = (await ctx.Users.FindAsync(keyValues: new object?[] { id }, cancellationToken: cancellationToken))!;

                if (dbUser is not null)
                {
                    dbUser.PasswordHash = _hasher.CreateHash(password);
                    dbUser.LastModifiedOn = DateTime.UtcNow;
                    dbUser.LastModifiedBy = id;

                    ctx.Users.Update(dbUser);
                    await ctx.SaveChangesAsync(cancellationToken);
                }
            }
        }

        private static IQueryable<Poco.Entities.User> GetUserQueryable(DataContext ctx)
        {
            return (from user in ctx.Users
                    join role in ctx.UserRoles on user.UserRoleId equals role.Id
                    // do not respond with system user
                    where user.Id != 1
                    select new Poco.Entities.User
                    {
                        Id = user.Id,
                        Role = role.Description,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        Email = user.Email,
                        PasswordHash = user.PasswordHash,
                        IsActive = user.IsActive,
                    });
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.User> CreateUserAsync(NewUserRequest user, int modifierId, Enums.UserRole userRole, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                using (IDbContextTransaction transaction = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        // create the user
                        User userEntity = GetUserEntity(user, modifierId);
                        var entity = await ctx.Users.AddAsync(userEntity, cancellationToken);

                        await ctx.SaveChangesAsync(cancellationToken);
                        if (userRole == Enums.UserRole.Client)
                        {
                            await InsertSpecificAccountPermissionsAsync(ctx, entity.Entity.Id, user.Accounts, cancellationToken);

                        }
                        else
                        {
                            await InsertAllAccountPermissions(ctx, entity.Entity.Id, cancellationToken);
                        }

                        if (user.Statements is not null && user.Statements.Any())
                        {
                            await InsertUserStatementChoiceAsync(ctx, user.Statements, entity.Entity.Id, cancellationToken);
                        }

                        transaction.Commit();
                        return (await FindByIdAsync(entity.Entity.Id, false, cancellationToken))!;
                    }
                    catch (Exception)
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        public async Task<Poco.Entities.User> UpdateUserAsync(EditUserRequest user, int modifierId, Enums.UserRole userRole, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                using (IDbContextTransaction transaction = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        // create the user
                        User? userEntity = await ctx.Users.FindAsync(new object?[] { user.Id }, cancellationToken: cancellationToken);
                        if (userEntity is null)
                        {
                            throw new Exception($"User with id {user.Id} not found");
                        }

                        userEntity = _mapper.Map<UserRequest, User>(user, userEntity);

                        userEntity.LastModifiedBy = modifierId;
                        userEntity.LastModifiedOn = DateTime.UtcNow;

                        ctx.Users.Update(userEntity);

                        if (userRole == Enums.UserRole.Client)
                        {
                            await DeleteExistingPermissionsAsync(ctx, userEntity.Id, cancellationToken);
                            await InsertSpecificAccountPermissionsAsync(ctx, userEntity.Id, user.Accounts, cancellationToken);
                        }

                        if (user.Statements is not null)
                        {
                            var requestedAccounts = user.Statements.Select(x => x.Account).ToList();

                            // existing associations
                            var existingAccounts = await ctx.UserAccountStatements.Where(x => x.UserId == userEntity.Id).Select(x => x.AccountId).ToListAsync(cancellationToken);

                            // create a linq to identify the accounts which are not in the requested list but are in the existing list
                            var toDelete = existingAccounts.Except(requestedAccounts);

                            // create a linq to identify the accounts which are not in the existing list but are in the requested list
                            var toAdd = requestedAccounts.Except(existingAccounts);

                            var statementsToAdd = user.Statements.Where(x => toAdd.Contains(x.Account)).ToList();

                            // insert new associations
                            if (statementsToAdd.Count > 0)
                            {
                                await InsertUserStatementChoiceAsync(ctx, statementsToAdd, userEntity.Id, cancellationToken);
                            }

                            // delete all earlier associations
                            if (toDelete.Any())
                            {
                                await ctx.UserAccountStatements.Where(x => x.UserId == userEntity.Id && toDelete.Contains(x.AccountId)).ExecuteDeleteAsync(cancellationToken);
                            }
                        }
                        else
                        {
                            // if no statements are provided, delete all existing associations
                            await ctx.UserAccountStatements.Where(x => x.UserId == userEntity.Id).ExecuteDeleteAsync(cancellationToken);
                        }

                        await ctx.SaveChangesAsync();

                        transaction.Commit();
                        return (await FindByIdAsync(userEntity.Id, false, cancellationToken))!;
                    }
                    catch (Exception)
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        private Task DeleteExistingPermissionsAsync(DataContext ctx, int id, CancellationToken cancellationToken)
        {
            return ctx.Database.ExecuteSqlRawAsync($"DELETE FROM UserAccountPermission WHERE [User] = {id}", cancellationToken);
        }

        private async Task InsertUserStatementChoiceAsync(DataContext ctx, List<UserStatements> statements, int id, CancellationToken cancellationToken)
        {
            StringBuilder sb = new StringBuilder();
            foreach (UserStatements statement in statements)
            {
                sb.AppendLine($"INSERT INTO UserAccountStatement ([User], [Account], Email, Mail, AssociationDate) VALUES({id}, {statement.Account}, {(statement.Email ? 1 : 0)}, {(statement.Mail ? 1 : 0)}, GETUTCDATE())");
            }

            await ctx.Database.ExecuteSqlRawAsync(sb.ToString(), cancellationToken);
        }

        private async Task InsertAllAccountPermissions(DataContext ctx, int id, CancellationToken cancellationToken)
        {
            await ctx.Database.ExecuteSqlRawAsync(
                @$"INSERT INTO UserAccountPermission ([User], [Account])
                    SELECT @UserId, ID FROM InvestmentAccount
                    WHERE ID <> {Constants.DataConstants.CashAccountId} AND (EndDate IS NULL OR EndDate < GETUTCDATE())",
                new List<SqlParameter> { new SqlParameter { ParameterName = "UserId", Value = id } }, cancellationToken: cancellationToken);
        }

        private async Task InsertSpecificAccountPermissionsAsync(DataContext ctx, int id, ICollection<int>? accounts, CancellationToken cancellationToken)
        {
            if (accounts is not null && accounts.Count > 0)
            {
                StringBuilder sb = new StringBuilder();
                foreach (int accountId in accounts)
                {
                    sb.AppendLine($"INSERT INTO UserAccountPermission ([User], [Account]) VALUES({id}, {accountId})");
                }

                await ctx.Database.ExecuteSqlRawAsync(sb.ToString(), cancellationToken: cancellationToken);
            }
        }

        private User GetUserEntity(NewUserRequest user, int modifierId)
        {
            User userEntity = _mapper.Map<User>(user);

            userEntity.CreatedOn = DateTime.UtcNow;
            userEntity.LastModifiedOn = DateTime.UtcNow;
            userEntity.CreatedBy = modifierId;
            userEntity.LastModifiedBy = modifierId;
            return userEntity;
        }
    }
}
