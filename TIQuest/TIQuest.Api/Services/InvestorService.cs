﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using TIQuest.Api.DTO.Request.Investors;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Services
{
    internal class InvestorService : IInvestorService
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;
        private readonly IMapper _mapper;

        public InvestorService(IDbContextFactory<DataContext> dbContextFactory, IMapper mapper)
        {
            _contextFactory = dbContextFactory;
            _mapper = mapper;
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.Investor> CreateAsync(NewInvestorRequest investor, int modifierId, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                // create the user
                Investor investorEntity = _mapper.Map<Investor>(investor);

                // if the investor is a company, then the company name is required
                if (investor.Type != Constants.DataConstants.CompanyInvestorType)
                {
                    investorEntity.CompanyName = null;
                }

                investorEntity.Type = investor.Type;
                investorEntity.IsActive = true;
                investorEntity.LastModifiedBy = modifierId;
                investorEntity.CreatedBy = modifierId;
                investorEntity.CreatedOn = DateTime.UtcNow;
                investorEntity.LastModifiedOn = DateTime.UtcNow;

                EntityEntry<Investor> entity = await ctx.Investors.AddAsync(investorEntity, cancellationToken);

                await ctx.SaveChangesAsync(cancellationToken);
                return _mapper.Map<Poco.Entities.Investor>(entity.Entity);
            }
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.Investor> UpdateAsync(EditInvestorRequest investor, int modifierId, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                // create the user
                Investor? investorEntity = await ctx.Investors.FindAsync(new object?[] { investor.Id }, cancellationToken: cancellationToken);

                if (investorEntity is null)
                {
                    throw new InvalidOperationException($"Investor with id {investor.Id} not found.");
                }

                investorEntity = _mapper.Map<InvestorRequest, Investor>(investor, investorEntity);

                // if the investor is a company, then the company name is required
                if (investorEntity.Type != Constants.DataConstants.CompanyInvestorType)
                {
                    investorEntity.CompanyName = null;
                }
                investorEntity.IsActive = investor.IsActive;
                investorEntity.LastModifiedBy = modifierId;
                investorEntity.LastModifiedOn = DateTime.UtcNow;

                ctx.Investors.Update(investorEntity);

                await ctx.SaveChangesAsync(cancellationToken);
                return _mapper.Map<Poco.Entities.Investor>(investorEntity);
            }
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.Investor?> FindByIdAsync(int id, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                Investor? investor = await ctx.Investors.FindAsync(new object?[] { id }, cancellationToken: cancellationToken);
                if (investor is not null)
                {
                    return _mapper.Map<Poco.Entities.Investor>(investor);
                }
                return null;
            }
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.Investor?> FindByTaxNumberAsync(string taxNumber, CancellationToken cancellationToken = default)
        {
            ArgumentException.ThrowIfNullOrEmpty(taxNumber, nameof(taxNumber));

            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                Investor? investor = await ctx.Investors
                    .Where(x => x.TaxNumber == taxNumber)
                    .FirstOrDefaultAsync(cancellationToken);
                if (investor is not null)
                {
                    return _mapper.Map<Poco.Entities.Investor>(investor);
                }
                return null;
            }
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<Poco.Entities.Investor>> GetAllInvestorsAsync(CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                List<Poco.Entities.Investor> investors = await ctx.Investors
                    .Where(x => x.Id != Constants.DataConstants.SystemInvestorId)
                    .Select(investor => _mapper.Map<Poco.Entities.Investor>(investor))
                    .ToListAsync(cancellationToken);

                return investors;
            }
        }
    }
}
