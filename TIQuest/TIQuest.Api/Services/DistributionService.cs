﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TIQuest.Api.DTO.Request;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Services
{
    internal class DistributionService : IDistributionService
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;
        private readonly IMapper _mapper;

        public DistributionService(IDbContextFactory<DataContext> dbContextFactory, IMapper mapper)
        {
            _contextFactory = dbContextFactory;
            _mapper = mapper;
        }

        /// <inheritdoc/>
        public async Task CreateDistributionAsync(
            NewDistributionRequest request,
            int modifierId,
            CancellationToken cancellationToken = default
        )
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                using (
                    var transaction = await ctx.Database.BeginTransactionAsync(cancellationToken)
                )
                {
                    var distribution = _mapper.Map<Distribution>(request);
                    ctx.Distributions.Add(distribution);
                    // calculate distribution list
                    var distributableAccounts = await GetDistributableAccounts(
                        ctx,
                        request,
                        cancellationToken
                    );
                    if (!distributableAccounts.Any())
                    {
                        throw new InvalidOperationException(
                            "No active distributable accounts found for the partnership."
                        );
                    }
                    var depositCode = await ctx.TransactionCodes.FirstAsync(
                        x => x.Code == Constants.DataConstants.DepositCode,
                        cancellationToken
                    );
                    var outstandingStatus = await ctx.TransactionStatuses.FirstAsync(
                        x => x.Description == Constants.DataConstants.OutstandingStatus,
                        cancellationToken
                    );
                    var transactions = new List<Transaction>();
                    var cashAccount = await ctx.InvestmentAccounts.FirstAsync(
                        x => x.Id == Constants.DataConstants.CashAccountId,
                        cancellationToken
                    );

                    foreach (var partnerAccount in distributableAccounts)
                    {
                        // create base transaction
                        var distributionAmount = Math.Round(GetDistributionAmount(
                            partnerAccount.Percentage,
                            request.Amount
                        ), 2, MidpointRounding.AwayFromZero);
                        
                        var baseTransaction = new Transaction
                        {
                            InvestmentAccount = partnerAccount.InvestmentAccount,
                            Amount = distributionAmount,
                            Date = new DateTime(request.Date, TimeOnly.MinValue),
                            Description = request.Description,
                            TransactionCode = depositCode,
                            TransactionStatus = outstandingStatus,
                            CreatedBy = modifierId,
                            LastModifiedBy = modifierId,
                            CreatedOn = DateTime.UtcNow,
                            LastModifiedOn = DateTime.UtcNow,
                            BankTransactionType = Constants.DataConstants.BankTransactionTypeCredit,
                        };
                        partnerAccount.InvestmentAccount.Balance += baseTransaction.Amount;
                        transactions.Add(baseTransaction);

                        ctx.InvestmentAccounts.Update(partnerAccount.InvestmentAccount);

                        // create cash transaction
                        var secondTransaction = new Transaction
                        {
                            InvestmentAccount = cashAccount,
                            Amount = baseTransaction.Amount,
                            Date = new DateTime(request.Date, TimeOnly.MinValue),
                            Description = request.Description,
                            TransactionCode = depositCode,
                            TransactionStatus = outstandingStatus,
                            ParentTransaction = baseTransaction,
                            CreatedBy = modifierId,
                            LastModifiedBy = modifierId,
                            CreatedOn = DateTime.UtcNow,
                            LastModifiedOn = DateTime.UtcNow,
                            BankTransactionType = Constants.DataConstants.BankTransactionTypeCredit,
                        };
                        cashAccount.Balance += secondTransaction.Amount;
                        transactions.Add(secondTransaction);
                    }

                    ctx.InvestmentAccounts.Update(cashAccount);

                    await ctx.AddRangeAsync(transactions);

                    await ctx.SaveChangesAsync(cancellationToken);

                    await InsertDistributionTransactions(
                        ctx,
                        distribution.Id,
                        transactions.Select(x => x.Id),
                        cancellationToken
                    );

                    await transaction.CommitAsync(cancellationToken);
                }
            }
        }

        private async Task InsertDistributionTransactions(
            DataContext ctx,
            int distributionId,
            IEnumerable<int> transactionIds,
            CancellationToken cancellationToken
        )
        {
            await ctx.Database.ExecuteSqlRawAsync(
                $@"INSERT INTO [DistributionTransaction] ([Distribution], [Transaction]) VALUES 
                    {string.Join(",", transactionIds.Select(x => $"({distributionId}, {x})"))}",
                cancellationToken
            );
        }

        private static decimal GetDistributionAmount(decimal percentage, decimal totalAmount) =>
            (percentage / 100) * totalAmount;

        private async Task<IEnumerable<PartnershipOwner>> GetDistributableAccounts(
            DataContext ctx,
            NewDistributionRequest request,
            CancellationToken cancellationToken
        )
        {
            return await ctx
                .PartnershipOwners.Include(x => x.InvestmentAccount)
                .Where(x =>
                    x.PartnershipId == request.Partnership
                    && (
                        !x.InvestmentAccount.EndDate.HasValue
                        || x.InvestmentAccount.EndDate.Value.Date
                            >= new DateTime(request.Date, TimeOnly.MinValue)
                    )
                )
                .ToListAsync(cancellationToken);
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<Poco.Entities.Distribution>> GetPartnershipDistributionsAsync(
            int partnershipId,
            CancellationToken cancellationToken = default
        )
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var distributions = await ctx
                    .Distributions.Where(x => x.PartnershipId == partnershipId)
                    .ToListAsync(cancellationToken);
                return _mapper.Map<IEnumerable<Poco.Entities.Distribution>>(distributions);
            }
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.Distribution?> GetDistributionAsync(
            int distributionId,
            CancellationToken cancellationToken = default
        )
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var distribution = await ctx.Distributions.FirstOrDefaultAsync(
                    x => x.Id == distributionId,
                    cancellationToken
                );
                if (distribution is null)
                {
                    return null;
                }
                return _mapper.Map<Poco.Entities.Distribution>(distribution);
            }
        }

        /// <inheritdoc/>
        public async Task DeleteDistributionAsync(
            int distributionId,
            int modifierId,
            CancellationToken cancellationToken = default
        )
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                using (
                    var dbTransaction = await ctx.Database.BeginTransactionAsync(cancellationToken)
                )
                {
                    var distributionTransactions = await ctx
                        .DistributionTransactions.Include(x => x.Transaction)
                        .ThenInclude(t => t.InvestmentAccount)
                        .Where(x => x.DistributionId == distributionId)
                        .ToListAsync(cancellationToken);

                    var transactions = distributionTransactions.Select(x => x.Transaction).ToList();

                    // Update the balance of each InvestmentAccount
                    foreach (var transaction in transactions)
                    {
                        transaction.InvestmentAccount.Balance -= transaction.Amount;
                        ctx.InvestmentAccounts.Update(transaction.InvestmentAccount);
                    }

                    // Delete the transactions
                    ctx.Transactions.RemoveRange(transactions);

                    // Delete the distribution transactions
                    await ctx
                        .DistributionTransactions.Where(x => x.DistributionId == distributionId)
                        .ExecuteDeleteAsync(cancellationToken);

                    // Delete the distribution
                    await ctx
                        .Distributions.Where(x => x.Id == distributionId)
                        .ExecuteDeleteAsync(cancellationToken);

                    await ctx.SaveChangesAsync(cancellationToken);
                    await dbTransaction.CommitAsync(cancellationToken);
                }
            }
        }
    }
}
