﻿using Microsoft.EntityFrameworkCore;
using TIQuest.Api.DTO.Request;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Services
{
    internal class SettingService : ISettingService
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;

        public SettingService(IDbContextFactory<DataContext> dbContextFactory)
        {
            _contextFactory = dbContextFactory;
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<Poco.Entities.Setting>> GetSettingsAsync(CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                return await ctx.Settings
                    .Select(s => new Poco.Entities.Setting
                    {
                        Key = s.Key,
                        Value = s.Value
                    }).ToListAsync(cancellationToken);
            }
        }

        /// <inheritdoc/>
        public async Task<Poco.Entities.Setting?> GetSettingAsync(string key, CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                return await ctx.Settings
                    .Where(s => s.Key == key)
                    .Select(s => new Poco.Entities.Setting
                    {
                        Id = s.Id,
                        Key = s.Key,
                        Value = s.Value
                    })
                    .FirstOrDefaultAsync(cancellationToken);
            }
        }

        /// <inheritdoc/>
        public async Task UpdateSettingAsync(EditSettingRequest request, CancellationToken cancellationToken = default)
        {
            using (var ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                var setting = await ctx.Settings
                                        .Where(s => s.Key == request.Key)
                                        .FirstOrDefaultAsync(cancellationToken);
                if (setting is null)
                {
                    throw new KeyNotFoundException($"Setting with key {request.Key} not found");
                }

                setting.Value = request.Value;

                ctx.Settings.Update(setting);
                await ctx.SaveChangesAsync(cancellationToken);
            }
        }
    }
}
