﻿using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Drawing.Charts;
using TIQuest.Api.Entities;

namespace TIQuest.Api
{
    internal sealed class MonthlyBalanceCalculator
    {
        private readonly DateTime _monthStartDate;
        private readonly DateTime _monthEndDate;
        private readonly int _daysInMonth;
        private readonly int _daysInYear;
        public MonthlyBalanceCalculator(DateTime startDate, DateTime endDate)
        {
            _monthStartDate = startDate.Date;
            _monthEndDate = endDate.Date;
            _daysInMonth = (_monthEndDate - _monthStartDate).Days;
            _daysInYear = DateTime.IsLeapYear(_monthStartDate.Year) ? 365 : 365; //366
        }

        public decimal CalculateAverageMonthlyBalance(IEnumerable<Transaction> transactions, decimal startingBalance)
        {
            var orderedTransactions = transactions
                .GroupBy(x => new { x.AccountId, x.Date })
                .Select( g => new { AccountId = g.Key.AccountId, Date = g.Key.Date, Amount = g.Sum(s => s.Amount) })
                .OrderBy(x => x.Date);

            // calculate cumulative balance
            decimal cumulativeBalance = 0;
            decimal previousBalance = startingBalance;
            DateTime previousDate = _monthStartDate;
            DateTime montEndDate = _monthEndDate.AddDays(-1);
            var accountId = 0;
            foreach (var transaction in orderedTransactions)
            {
                accountId = transaction.AccountId;
                int days = (transaction.Date - previousDate).Days;
                cumulativeBalance += (previousBalance * days);
                cumulativeBalance += previousBalance + transaction.Amount;
                previousBalance += transaction.Amount;

                previousDate = transaction.Date.AddDays(1);
            }
            var daysForMonthEnd = (montEndDate - previousDate).Days;
            if ((montEndDate.AddDays(-1) - previousDate).Days > 0)
            {
                cumulativeBalance += previousBalance * daysForMonthEnd;
            }

            return cumulativeBalance / (_daysInMonth - 1);
        }

        public decimal CalculateMonthlyInterestDue(decimal averageBalance, decimal interestRate)
        {
            // due = balance * rate * period
            return decimal.Round(averageBalance * (interestRate / 100) * (_daysInMonth / (decimal)_daysInYear), 2);
        }

        public decimal CalculateDynamicInterestRate(decimal averageBalance, decimal interestDue)
        {
            if (averageBalance == 0)
            {
                return 0m;
            }
            // rate = due / (balance * period)
            var rate = Math.Round(((interestDue * _daysInYear) / _daysInMonth) / averageBalance, 5);
            var rate2 = decimal.Round((interestDue / (averageBalance * (_daysInMonth / (decimal)_daysInYear))) * 100, 5);
            return Math.Round(((interestDue * _daysInYear) / _daysInMonth) / averageBalance, 5) * 100;
        }
    }
}
