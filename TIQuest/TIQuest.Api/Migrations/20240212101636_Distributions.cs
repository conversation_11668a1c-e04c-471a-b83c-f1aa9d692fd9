﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TIQuest.Api.Migrations
{
    /// <inheritdoc />
    public partial class Distributions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Distribution",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Partnership = table.Column<int>(type: "int", nullable: false),
                    Date = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Amount = table.Column<decimal>(type: "money", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Distribution", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Distribution_Partnership_Partnership",
                        column: x => x.Partnership,
                        principalTable: "Partnership",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "DistributionTransaction",
                columns: table => new
                {
                    Distribution = table.Column<int>(type: "int", nullable: false),
                    Transaction = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_DistributionTransaction_Distribution_Distribution",
                        column: x => x.Distribution,
                        principalTable: "Distribution",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DistributionTransaction_Transaction_Transaction",
                        column: x => x.Transaction,
                        principalTable: "Transaction",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_Distribution_Partnership",
                table: "Distribution",
                column: "Partnership");

            migrationBuilder.CreateIndex(
                name: "IX_DistributionTransaction_Distribution",
                table: "DistributionTransaction",
                column: "Distribution");

            migrationBuilder.CreateIndex(
                name: "IX_DistributionTransaction_Transaction",
                table: "DistributionTransaction",
                column: "Transaction");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DistributionTransaction");

            migrationBuilder.DropTable(
                name: "Distribution");
        }
    }
}
