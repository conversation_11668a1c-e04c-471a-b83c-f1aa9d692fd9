﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TIQuest.Api.Migrations
{
    /// <inheritdoc />
    public partial class Transaction : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            DateOnly startDate = new DateOnly(2000, 1, 1);
            DateOnly endDate = new DateOnly(2051, 1, 1);
            var dates = new List<string>();
            for (var i = startDate; i < endDate;)
            {
                dates.Add(i.ToString("yyyyMMdd"));
                i = i.AddYears(1);
            }

            migrationBuilder.Sql($@"CREATE PARTITION FUNCTION YearPartitionFunction (datetime2) 
                                   AS RANGE RIGHT FOR VALUES ('{string.Join("', '", dates)}');");

            migrationBuilder.Sql(@"CREATE PARTITION SCHEME YearPartitionScheme AS PARTITION YearPartitionFunction ALL TO ([Primary]);");

            migrationBuilder.Sql(@"CREATE TABLE [Transaction] (
                                    [Id] int NOT NULL IDENTITY,
                                    [Account] int NOT NULL,
                                    [Date] datetime2 NOT NULL,
                                    [Amount] money NOT NULL,
                                    [Description] varchar(80) NULL,
                                    [Parent] int NULL,
                                    [WireNumber] varchar(50) NULL,
                                    [LastModifiedBy] int NOT NULL,
                                    [LastModifiedOn] datetime2 NOT NULL,
                                    [CreatedBy] int NOT NULL,
                                    [CreatedOn] datetime2 NOT NULL,
                                    CONSTRAINT [FK_Transaction_InvestmentAccount_Account] FOREIGN KEY ([Account]) REFERENCES [InvestmentAccount] ([Id]),
                                    CONSTRAINT [FK_Transaction_User_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [User] ([Id]),
                                    CONSTRAINT [FK_Transaction_User_LastModifiedBy] FOREIGN KEY ([LastModifiedBy]) REFERENCES [User] ([Id])
                                )ON YearPartitionScheme([Date]);");

            migrationBuilder.Sql(@"ALTER TABLE [dbo].[Transaction] ADD CONSTRAINT [PK_Transaction] PRIMARY KEY NONCLUSTERED ( [Id] ASC ) ON [PRIMARY]");

            migrationBuilder.Sql(@"ALTER TABLE [dbo].[Transaction] ADD CONSTRAINT [FK_Transaction_Transaction_Parent] FOREIGN KEY ([Parent]) REFERENCES [Transaction] ([Id]);");

            migrationBuilder.Sql(@"CREATE CLUSTERED INDEX [CIX_Transaction_Date] ON [Transaction] ([Date]) ON YearPartitionScheme([Date]);");

            migrationBuilder.CreateIndex(
                name: "IX_Transaction_Account",
                table: "Transaction",
                column: "Account");

            migrationBuilder.CreateIndex(
                name: "IX_Transaction_CreatedBy",
                table: "Transaction",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_Transaction_LastModifiedBy",
                table: "Transaction",
                column: "LastModifiedBy");

            migrationBuilder.CreateIndex(
                name: "IX_Transaction_Parent",
                table: "Transaction",
                column: "Parent");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Transaction");

            migrationBuilder.Sql(@"DROP PARTITION SCHEME YearPartitionScheme;");

            migrationBuilder.Sql(@"DROP PARTITION FUNCTION YearPartitionFunction;");
        }
    }
}
