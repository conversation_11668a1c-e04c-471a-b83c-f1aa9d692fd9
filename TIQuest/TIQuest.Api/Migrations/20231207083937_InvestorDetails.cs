﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TIQuest.Api.Migrations
{
    /// <inheritdoc />
    public partial class InvestorDetails : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Address",
                table: "Investor",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Apartment",
                table: "Investor",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "City",
                table: "Investor",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Country",
                table: "Investor",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Email",
                table: "Investor",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "HomePhone",
                table: "Investor",
                type: "varchar(30)",
                maxLength: 30,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Mobile",
                table: "Investor",
                type: "varchar(30)",
                maxLength: 30,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Note",
                table: "Investor",
                type: "varchar(400)",
                maxLength: 400,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OfficePhone",
                table: "Investor",
                type: "varchar(30)",
                maxLength: 30,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "State",
                table: "Investor",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Type",
                table: "Investor",
                type: "varchar(30)",
                maxLength: 30,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Zip",
                table: "Investor",
                type: "nvarchar(30)",
                maxLength: 30,
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Investor_TaxNumber",
                table: "Investor",
                column: "TaxNumber",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Investor_TaxNumber",
                table: "Investor");

            migrationBuilder.DropColumn(
                name: "Address",
                table: "Investor");

            migrationBuilder.DropColumn(
                name: "Apartment",
                table: "Investor");

            migrationBuilder.DropColumn(
                name: "City",
                table: "Investor");

            migrationBuilder.DropColumn(
                name: "Country",
                table: "Investor");

            migrationBuilder.DropColumn(
                name: "Email",
                table: "Investor");

            migrationBuilder.DropColumn(
                name: "HomePhone",
                table: "Investor");

            migrationBuilder.DropColumn(
                name: "Mobile",
                table: "Investor");

            migrationBuilder.DropColumn(
                name: "Note",
                table: "Investor");

            migrationBuilder.DropColumn(
                name: "OfficePhone",
                table: "Investor");

            migrationBuilder.DropColumn(
                name: "State",
                table: "Investor");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "Investor");

            migrationBuilder.DropColumn(
                name: "Zip",
                table: "Investor");
        }
    }
}
