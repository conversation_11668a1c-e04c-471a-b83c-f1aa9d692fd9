﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TIQuest.Api.Migrations
{
    /// <inheritdoc />
    public partial class MonthEndProcess : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserAccountPermission_InvestmentAccount_Account",
                table: "UserAccountPermission");

            migrationBuilder.DropForeignKey(
                name: "FK_UserAccountPermission_User_User",
                table: "UserAccountPermission");

            migrationBuilder.CreateTable(
                name: "MonthEndProcess",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Year = table.Column<int>(type: "int", nullable: false),
                    Month = table.Column<int>(type: "int", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    InvestmentRate = table.Column<decimal>(type: "decimal(7,5)", precision: 7, scale: 5, nullable: false),
                    LoanRate = table.Column<decimal>(type: "decimal(7,5)", precision: 7, scale: 5, nullable: false),
                    ManagementRate = table.Column<decimal>(type: "decimal(7,5)", precision: 7, scale: 5, nullable: false),
                    ManagementFee = table.Column<decimal>(type: "money", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MonthEndProcess", x => x.Id);
                });

            migrationBuilder.AddForeignKey(
                name: "FK_UserAccountPermission_InvestmentAccount_Account",
                table: "UserAccountPermission",
                column: "Account",
                principalTable: "InvestmentAccount",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_UserAccountPermission_User_User",
                table: "UserAccountPermission",
                column: "User",
                principalTable: "User",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserAccountPermission_InvestmentAccount_Account",
                table: "UserAccountPermission");

            migrationBuilder.DropForeignKey(
                name: "FK_UserAccountPermission_User_User",
                table: "UserAccountPermission");

            migrationBuilder.DropTable(
                name: "MonthEndProcess");

            migrationBuilder.AddForeignKey(
                name: "FK_UserAccountPermission_InvestmentAccount_Account",
                table: "UserAccountPermission",
                column: "Account",
                principalTable: "InvestmentAccount",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UserAccountPermission_User_User",
                table: "UserAccountPermission",
                column: "User",
                principalTable: "User",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
