﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TIQuest.Api.Migrations
{
    /// <inheritdoc />
    public partial class PartnershipOwner : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "PartnershipOwner",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Account = table.Column<int>(type: "int", nullable: false),
                    Partnership = table.Column<int>(type: "int", nullable: false),
                    Percentage = table.Column<decimal>(type: "decimal(7,5)", precision: 7, scale: 5, nullable: false),
                    LastModifiedBy = table.Column<int>(type: "int", nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<int>(type: "int", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PartnershipOwner", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PartnershipOwner_InvestmentAccount_Account",
                        column: x => x.Account,
                        principalTable: "InvestmentAccount",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PartnershipOwner_Partnership_Partnership",
                        column: x => x.Partnership,
                        principalTable: "Partnership",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PartnershipOwner_User_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "User",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PartnershipOwner_User_LastModifiedBy",
                        column: x => x.LastModifiedBy,
                        principalTable: "User",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_PartnershipOwner_Account_Partnership",
                table: "PartnershipOwner",
                columns: new[] { "Account", "Partnership" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PartnershipOwner_CreatedBy",
                table: "PartnershipOwner",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_PartnershipOwner_LastModifiedBy",
                table: "PartnershipOwner",
                column: "LastModifiedBy");

            migrationBuilder.CreateIndex(
                name: "IX_PartnershipOwner_Partnership",
                table: "PartnershipOwner",
                column: "Partnership");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PartnershipOwner");
        }
    }
}
