﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TIQuest.Api.Migrations
{
    /// <inheritdoc />
    public partial class UpdateInvestorEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON>",
                table: "Investor");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Email",
                table: "Investor",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");
        }
    }
}
