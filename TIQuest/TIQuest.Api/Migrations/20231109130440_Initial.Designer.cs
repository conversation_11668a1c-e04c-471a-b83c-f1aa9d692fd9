﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using TIQuest.Api.Entities;

#nullable disable

namespace TIQuest.Api.Migrations
{
    [DbContext(typeof(DataContext))]
    [Migration("20231109130440_Initial")]
    partial class Initial
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.9")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("TIQuest.Api.Entities.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(80)
                        .HasColumnType("nvarchar(80)");

                    b.Property<string>("HomePhone")
                        .HasMaxLength(30)
                        .HasColumnType("varchar");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("LastModifiedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(80)
                        .HasColumnType("nvarchar(80)");

                    b.Property<string>("Mobile")
                        .HasMaxLength(30)
                        .HasColumnType("varchar");

                    b.Property<string>("OfficePhone")
                        .HasMaxLength(30)
                        .HasColumnType("varchar");

                    b.Property<string>("PasswordHash")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)");

                    b.Property<int>("UserRoleId")
                        .HasColumnType("int")
                        .HasColumnName("UserRole");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("LastModifiedBy");

                    b.HasIndex("UserRoleId");

                    b.ToTable("User");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedBy = 1,
                            CreatedOn = new DateTime(2023, 11, 9, 13, 4, 40, 521, DateTimeKind.Utc).AddTicks(3737),
                            Email = "<EMAIL>",
                            FirstName = "System",
                            IsActive = false,
                            LastModifiedBy = 1,
                            LastModifiedOn = new DateTime(2023, 11, 9, 13, 4, 40, 521, DateTimeKind.Utc).AddTicks(3740),
                            LastName = "Admin",
                            UserRoleId = 1
                        });
                });

            modelBuilder.Entity("TIQuest.Api.Entities.UserRole", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(80)
                        .HasColumnType("varchar");

                    b.HasKey("Id");

                    b.ToTable("UserRole");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Description = "Administrator"
                        },
                        new
                        {
                            Id = 2,
                            Description = "Client"
                        },
                        new
                        {
                            Id = 3,
                            Description = "Auditor"
                        });
                });

            modelBuilder.Entity("TIQuest.Api.Entities.User", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.User", "UserCreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "UserModifiedBy")
                        .WithMany()
                        .HasForeignKey("LastModifiedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.UserRole", "UserRole")
                        .WithMany()
                        .HasForeignKey("UserRoleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("UserCreatedBy");

                    b.Navigation("UserModifiedBy");

                    b.Navigation("UserRole");
                });
#pragma warning restore 612, 618
        }
    }
}
