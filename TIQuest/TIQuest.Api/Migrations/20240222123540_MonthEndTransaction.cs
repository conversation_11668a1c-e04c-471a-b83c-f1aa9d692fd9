﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TIQuest.Api.Migrations
{
    /// <inheritdoc />
    public partial class MonthEndTransaction : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "ClosureBalance",
                table: "InvestmentAccount",
                type: "money",
                precision: 15,
                scale: 5,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.CreateTable(
                name: "MonthEndProcessTransaction",
                columns: table => new
                {
                    MonthEndProcess = table.Column<int>(type: "int", nullable: false),
                    Transaction = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_MonthEndProcessTransaction_MonthEndProcess_MonthEndProcess",
                        column: x => x.MonthEndProcess,
                        principalTable: "MonthEndProcess",
                        principalColumn: "Id");
                    table.Foreign<PERSON>ey(
                        name: "FK_MonthEndProcessTransaction_Transaction_Transaction",
                        column: x => x.Transaction,
                        principalTable: "Transaction",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_MonthEndProcessTransaction_MonthEndProcess",
                table: "MonthEndProcessTransaction",
                column: "MonthEndProcess");

            migrationBuilder.CreateIndex(
                name: "IX_MonthEndProcessTransaction_Transaction",
                table: "MonthEndProcessTransaction",
                column: "Transaction");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MonthEndProcessTransaction");

            migrationBuilder.DropColumn(
                name: "ClosureBalance",
                table: "InvestmentAccount");
        }
    }
}
