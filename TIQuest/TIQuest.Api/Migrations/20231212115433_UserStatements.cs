﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TIQuest.Api.Migrations
{
    /// <inheritdoc />
    public partial class UserStatements : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "UserAccountStatement",
                columns: table => new
                {
                    User = table.Column<int>(type: "int", nullable: false),
                    Account = table.Column<int>(type: "int", nullable: false),
                    Email = table.Column<bool>(type: "bit", nullable: false),
                    Mail = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_UserAccountStatement_InvestmentAccount_Account",
                        column: x => x.Account,
                        principalTable: "InvestmentAccount",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_UserAccountStatement_User_User",
                        column: x => x.User,
                        principalTable: "User",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_UserAccountStatement_Account",
                table: "UserAccountStatement",
                column: "Account");

            migrationBuilder.CreateIndex(
                name: "IX_UserAccountStatement_User",
                table: "UserAccountStatement",
                column: "User");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "UserAccountStatement");
        }
    }
}
