﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using TIQuest.Api.Entities;

#nullable disable

namespace TIQuest.Api.Migrations
{
    [DbContext(typeof(DataContext))]
    [Migration("20240112095421_SettingUpgrade")]
    partial class SettingUpgrade
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.9")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("TIQuest.Api.Entities.AchRegister", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("varchar");

                    b.Property<bool>("IsPrinted")
                        .HasColumnType("bit");

                    b.Property<int>("TransactionId")
                        .HasColumnType("int")
                        .HasColumnName("Transaction");

                    b.HasKey("Id");

                    b.HasIndex("TransactionId");

                    b.ToTable("AchRegister");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.CheckRegister", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Apartment")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsPrinted")
                        .HasColumnType("bit");

                    b.Property<string>("Memo")
                        .HasMaxLength(256)
                        .HasColumnType("varchar");

                    b.Property<int>("Number")
                        .HasColumnType("int");

                    b.Property<string>("Payee")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar");

                    b.Property<string>("State")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("TransactionId")
                        .HasColumnType("int")
                        .HasColumnName("Transaction");

                    b.Property<string>("Zip")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.HasKey("Id");

                    b.HasIndex("TransactionId");

                    b.ToTable("CheckRegister");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.InvestmentAccount", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AccountNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("AccountType")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("varchar");

                    b.Property<decimal>("Balance")
                        .HasPrecision(15, 5)
                        .HasColumnType("decimal(15,5)");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("InterestType")
                        .HasMaxLength(20)
                        .HasColumnType("varchar");

                    b.Property<int>("InvestorId")
                        .HasColumnType("int")
                        .HasColumnName("Investor");

                    b.Property<int>("LastModifiedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal?>("Rate")
                        .HasPrecision(7, 5)
                        .HasColumnType("decimal(7,5)");

                    b.Property<string>("Report1099Name")
                        .HasMaxLength(250)
                        .HasColumnType("varchar");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("AccountNumber")
                        .IsUnique();

                    b.HasIndex("CreatedBy");

                    b.HasIndex("InvestorId");

                    b.HasIndex("LastModifiedBy");

                    b.ToTable("InvestmentAccount");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            AccountNumber = "1",
                            AccountType = "Cash",
                            Balance = 0m,
                            CreatedBy = 1,
                            CreatedOn = new DateTime(2024, 1, 12, 9, 54, 21, 593, DateTimeKind.Utc).AddTicks(6031),
                            InterestType = "None",
                            InvestorId = 1,
                            LastModifiedBy = 1,
                            LastModifiedOn = new DateTime(2024, 1, 12, 9, 54, 21, 593, DateTimeKind.Utc).AddTicks(6032),
                            Name = "Cash Account",
                            Rate = 0m,
                            StartDate = new DateTime(2000, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        });
                });

            modelBuilder.Entity("TIQuest.Api.Entities.Investor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Apartment")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CompanyName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Fax")
                        .HasMaxLength(30)
                        .HasColumnType("varchar");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(80)
                        .HasColumnType("nvarchar(80)");

                    b.Property<string>("HomePhone")
                        .HasMaxLength(30)
                        .HasColumnType("varchar");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("LastModifiedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(80)
                        .HasColumnType("nvarchar(80)");

                    b.Property<string>("Mobile")
                        .HasMaxLength(30)
                        .HasColumnType("varchar");

                    b.Property<string>("Note")
                        .HasMaxLength(400)
                        .HasColumnType("varchar");

                    b.Property<string>("OfficePhone")
                        .HasMaxLength(30)
                        .HasColumnType("varchar");

                    b.Property<string>("State")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TaxNumber")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("varchar");

                    b.Property<string>("Zip")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("LastModifiedBy");

                    b.HasIndex("TaxNumber")
                        .IsUnique();

                    b.ToTable("Investor");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Address = "System",
                            CompanyName = "System",
                            Country = "USA",
                            CreatedBy = 1,
                            CreatedOn = new DateTime(2024, 1, 12, 9, 54, 21, 593, DateTimeKind.Utc).AddTicks(5980),
                            FirstName = "Triton",
                            IsActive = true,
                            LastModifiedBy = 1,
                            LastModifiedOn = new DateTime(2024, 1, 12, 9, 54, 21, 593, DateTimeKind.Utc).AddTicks(5983),
                            LastName = "Investments",
                            TaxNumber = "0",
                            Type = "Company"
                        });
                });

            modelBuilder.Entity("TIQuest.Api.Entities.MonthEndProcess", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("InvestmentRate")
                        .HasPrecision(7, 5)
                        .HasColumnType("decimal(7,5)");

                    b.Property<decimal>("LoanRate")
                        .HasPrecision(7, 5)
                        .HasColumnType("decimal(7,5)");

                    b.Property<decimal>("ManagementFee")
                        .HasColumnType("money");

                    b.Property<decimal>("ManagementRate")
                        .HasPrecision(7, 5)
                        .HasColumnType("decimal(7,5)");

                    b.Property<int>("Month")
                        .HasColumnType("int");

                    b.Property<int>("Year")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("MonthEndProcess");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Description = "Initial Seed",
                            InvestmentRate = 0m,
                            LoanRate = 0m,
                            ManagementFee = 0m,
                            ManagementRate = 0m,
                            Month = 1,
                            Year = 2000
                        });
                });

            modelBuilder.Entity("TIQuest.Api.Entities.Setting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar");

                    b.Property<string>("Value")
                        .HasMaxLength(256)
                        .HasColumnType("varchar");

                    b.HasKey("Id");

                    b.ToTable("Setting");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Key = "LastCheckNumber",
                            Value = "0"
                        });
                });

            modelBuilder.Entity("TIQuest.Api.Entities.Transaction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AccountId")
                        .HasColumnType("int")
                        .HasColumnName("Account");

                    b.Property<decimal>("Amount")
                        .HasColumnType("money");

                    b.Property<int>("CodeId")
                        .HasColumnType("int")
                        .HasColumnName("Code");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(80)
                        .HasColumnType("varchar");

                    b.Property<int>("LastModifiedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int")
                        .HasColumnName("Parent");

                    b.Property<int>("StatusId")
                        .HasColumnType("int")
                        .HasColumnName("Status");

                    b.Property<string>("WireNumber")
                        .HasMaxLength(50)
                        .HasColumnType("varchar");

                    b.HasKey("Id");

                    b.HasIndex("AccountId");

                    b.HasIndex("CodeId");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("LastModifiedBy");

                    b.HasIndex("ParentId");

                    b.HasIndex("StatusId");

                    b.ToTable("Transaction");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.TransactionCode", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(6)
                        .HasColumnType("varchar");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDebit")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSystem")
                        .HasColumnType("bit");

                    b.Property<bool>("IsTaxable")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.ToTable("TransactionCode");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "I-DEP",
                            Description = "Deposit",
                            IsActive = true,
                            IsDebit = false,
                            IsSystem = false,
                            IsTaxable = false
                        },
                        new
                        {
                            Id = 2,
                            Code = "I-WDL",
                            Description = "Withdrawal",
                            IsActive = true,
                            IsDebit = true,
                            IsSystem = false,
                            IsTaxable = false
                        },
                        new
                        {
                            Id = 3,
                            Code = "L-PMT",
                            Description = "Loan Payment",
                            IsActive = true,
                            IsDebit = false,
                            IsSystem = false,
                            IsTaxable = false
                        },
                        new
                        {
                            Id = 4,
                            Code = "L-ADV",
                            Description = "Loan Advance",
                            IsActive = true,
                            IsDebit = true,
                            IsSystem = false,
                            IsTaxable = false
                        },
                        new
                        {
                            Id = 5,
                            Code = "L-INT",
                            Description = "Loan Interest",
                            IsActive = true,
                            IsDebit = false,
                            IsSystem = false,
                            IsTaxable = true
                        },
                        new
                        {
                            Id = 6,
                            Code = "I-INT",
                            Description = "Investment Interest",
                            IsActive = true,
                            IsDebit = false,
                            IsSystem = false,
                            IsTaxable = true
                        },
                        new
                        {
                            Id = 7,
                            Code = "XFER",
                            Description = "Transfer",
                            IsActive = true,
                            IsDebit = false,
                            IsSystem = false,
                            IsTaxable = false
                        },
                        new
                        {
                            Id = 8,
                            Code = "VADJ",
                            Description = "Void Adjustment",
                            IsActive = true,
                            IsDebit = false,
                            IsSystem = false,
                            IsTaxable = false
                        },
                        new
                        {
                            Id = 9,
                            Code = "FEE",
                            Description = "Management Fee",
                            IsActive = true,
                            IsDebit = false,
                            IsSystem = false,
                            IsTaxable = false
                        },
                        new
                        {
                            Id = 10,
                            Code = "LXFER",
                            Description = "Loan Transfer",
                            IsActive = true,
                            IsDebit = false,
                            IsSystem = false,
                            IsTaxable = false
                        });
                });

            modelBuilder.Entity("TIQuest.Api.Entities.TransactionStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar");

                    b.HasKey("Id");

                    b.ToTable("TransactionStatus");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Description = "Outstanding"
                        },
                        new
                        {
                            Id = 2,
                            Description = "Reconciled"
                        },
                        new
                        {
                            Id = 3,
                            Description = "Void"
                        },
                        new
                        {
                            Id = 4,
                            Description = "Pending"
                        });
                });

            modelBuilder.Entity("TIQuest.Api.Entities.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(80)
                        .HasColumnType("nvarchar(80)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("LastModifiedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(80)
                        .HasColumnType("nvarchar(80)");

                    b.Property<string>("PasswordHash")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)");

                    b.Property<int>("UserRoleId")
                        .HasColumnType("int")
                        .HasColumnName("UserRole");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("LastModifiedBy");

                    b.HasIndex("UserRoleId");

                    b.ToTable("User");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedBy = 1,
                            CreatedOn = new DateTime(2023, 11, 9, 13, 4, 40, 521, DateTimeKind.Utc).AddTicks(3737),
                            Email = "<EMAIL>",
                            FirstName = "System",
                            IsActive = false,
                            LastModifiedBy = 1,
                            LastModifiedOn = new DateTime(2023, 11, 9, 13, 4, 40, 521, DateTimeKind.Utc).AddTicks(3737),
                            LastName = "Admin",
                            UserRoleId = 1
                        });
                });

            modelBuilder.Entity("TIQuest.Api.Entities.UserAccountPermission", b =>
                {
                    b.Property<int>("InvestmentAccountId")
                        .HasColumnType("int")
                        .HasColumnName("Account");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("User");

                    b.HasIndex("InvestmentAccountId");

                    b.HasIndex("UserId");

                    b.ToTable("UserAccountPermission");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.UserAccountStatement", b =>
                {
                    b.Property<int>("AccountId")
                        .HasColumnType("int")
                        .HasColumnName("Account");

                    b.Property<bool>("Email")
                        .HasColumnType("bit");

                    b.Property<bool>("Mail")
                        .HasColumnType("bit");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("User");

                    b.HasIndex("AccountId");

                    b.HasIndex("UserId");

                    b.ToTable("UserAccountStatement");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.UserRole", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(80)
                        .HasColumnType("varchar");

                    b.HasKey("Id");

                    b.ToTable("UserRole");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Description = "Administrator"
                        },
                        new
                        {
                            Id = 2,
                            Description = "Client"
                        },
                        new
                        {
                            Id = 3,
                            Description = "Auditor"
                        });
                });

            modelBuilder.Entity("TIQuest.Api.Entities.AchRegister", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.Transaction", "Transaction")
                        .WithMany()
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.CheckRegister", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.Transaction", "Transaction")
                        .WithMany()
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.InvestmentAccount", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.User", "UserCreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.Investor", "AccountInvestor")
                        .WithMany()
                        .HasForeignKey("InvestorId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "UserModifiedBy")
                        .WithMany()
                        .HasForeignKey("LastModifiedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("AccountInvestor");

                    b.Navigation("UserCreatedBy");

                    b.Navigation("UserModifiedBy");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.Investor", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.User", "UserCreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "UserModifiedBy")
                        .WithMany()
                        .HasForeignKey("LastModifiedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("UserCreatedBy");

                    b.Navigation("UserModifiedBy");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.Transaction", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.InvestmentAccount", "InvestmentAccount")
                        .WithMany()
                        .HasForeignKey("AccountId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.TransactionCode", "TransactionCode")
                        .WithMany()
                        .HasForeignKey("CodeId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "UserCreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "UserModifiedBy")
                        .WithMany()
                        .HasForeignKey("LastModifiedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.Transaction", "ParentTransaction")
                        .WithMany()
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("TIQuest.Api.Entities.TransactionStatus", "TransactionStatus")
                        .WithMany()
                        .HasForeignKey("StatusId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("InvestmentAccount");

                    b.Navigation("ParentTransaction");

                    b.Navigation("TransactionCode");

                    b.Navigation("TransactionStatus");

                    b.Navigation("UserCreatedBy");

                    b.Navigation("UserModifiedBy");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.User", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.User", "UserCreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "UserModifiedBy")
                        .WithMany()
                        .HasForeignKey("LastModifiedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.UserRole", "UserRole")
                        .WithMany()
                        .HasForeignKey("UserRoleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("UserCreatedBy");

                    b.Navigation("UserModifiedBy");

                    b.Navigation("UserRole");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.UserAccountPermission", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.InvestmentAccount", "InvestmentAccount")
                        .WithMany()
                        .HasForeignKey("InvestmentAccountId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("InvestmentAccount");

                    b.Navigation("User");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.UserAccountStatement", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.InvestmentAccount", "InvestmentAccount")
                        .WithMany()
                        .HasForeignKey("AccountId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "AssociatedUser")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("AssociatedUser");

                    b.Navigation("InvestmentAccount");
                });
#pragma warning restore 612, 618
        }
    }
}
