﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TIQuest.Api.Migrations
{
    /// <inheritdoc />
    public partial class EditAccount : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Report1099Name",
                table: "InvestmentAccount",
                type: "varchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_InvestmentAccount_AccountNumber",
                table: "InvestmentAccount",
                column: "AccountNumber",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_InvestmentAccount_AccountNumber",
                table: "InvestmentAccount");

            migrationBuilder.DropColumn(
                name: "Report1099Name",
                table: "InvestmentAccount");
        }
    }
}
