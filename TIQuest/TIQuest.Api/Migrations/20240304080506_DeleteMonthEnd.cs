﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TIQuest.Api.Migrations
{
    /// <inheritdoc />
    public partial class DeleteMonthEnd : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<decimal>(
                name: "Amount",
                table: "Transaction",
                type: "decimal(15,5)",
                precision: 15,
                scale: 5,
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "money");

            migrationBuilder.AlterColumn<decimal>(
                name: "ManagementFee",
                table: "MonthEndProcess",
                type: "decimal(15,5)",
                precision: 15,
                scale: 5,
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "money");

            migrationBuilder.AlterColumn<decimal>(
                name: "ClosureBalance",
                table: "InvestmentAccount",
                type: "decimal(15,5)",
                precision: 15,
                scale: 5,
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "money",
                oldPrecision: 15,
                oldScale: 5);

            migrationBuilder.AlterColumn<decimal>(
                name: "Amount",
                table: "Distribution",
                type: "decimal(15,5)",
                precision: 15,
                scale: 5,
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "money");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<decimal>(
                name: "Amount",
                table: "Transaction",
                type: "money",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(15,5)",
                oldPrecision: 15,
                oldScale: 5);

            migrationBuilder.AlterColumn<decimal>(
                name: "ManagementFee",
                table: "MonthEndProcess",
                type: "money",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(15,5)",
                oldPrecision: 15,
                oldScale: 5);

            migrationBuilder.AlterColumn<decimal>(
                name: "ClosureBalance",
                table: "InvestmentAccount",
                type: "money",
                precision: 15,
                scale: 5,
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(15,5)",
                oldPrecision: 15,
                oldScale: 5);

            migrationBuilder.AlterColumn<decimal>(
                name: "Amount",
                table: "Distribution",
                type: "money",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(15,5)",
                oldPrecision: 15,
                oldScale: 5);
        }
    }
}
