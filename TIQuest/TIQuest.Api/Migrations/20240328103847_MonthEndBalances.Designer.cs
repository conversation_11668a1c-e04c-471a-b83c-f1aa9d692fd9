﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using TIQuest.Api.Entities;

#nullable disable

namespace TIQuest.Api.Migrations
{
    [DbContext(typeof(DataContext))]
    [Migration("20240328103847_MonthEndBalances")]
    partial class MonthEndBalances
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.2")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("TIQuest.Api.Entities.AchRegister", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("varchar");

                    b.Property<bool>("IsPrinted")
                        .HasColumnType("bit");

                    b.Property<int>("TransactionId")
                        .HasColumnType("int")
                        .HasColumnName("Transaction");

                    b.HasKey("Id");

                    b.HasIndex("TransactionId");

                    b.ToTable("AchRegister");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.CheckRegister", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Apartment")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsPrinted")
                        .HasColumnType("bit");

                    b.Property<string>("Memo")
                        .HasMaxLength(256)
                        .HasColumnType("varchar");

                    b.Property<int?>("Number")
                        .HasColumnType("int");

                    b.Property<string>("Payee")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar");

                    b.Property<string>("State")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("TransactionId")
                        .HasColumnType("int")
                        .HasColumnName("Transaction");

                    b.Property<string>("Zip")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.HasKey("Id");

                    b.HasIndex("TransactionId");

                    b.ToTable("CheckRegister");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.Distribution", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Amount")
                        .HasPrecision(15, 5)
                        .HasColumnType("decimal(15,5)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<int>("PartnershipId")
                        .HasColumnType("int")
                        .HasColumnName("Partnership");

                    b.HasKey("Id");

                    b.HasIndex("PartnershipId");

                    b.ToTable("Distribution");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.DistributionTransaction", b =>
                {
                    b.Property<int>("DistributionId")
                        .HasColumnType("int")
                        .HasColumnName("Distribution");

                    b.Property<int>("TransactionId")
                        .HasColumnType("int")
                        .HasColumnName("Transaction");

                    b.HasIndex("DistributionId");

                    b.HasIndex("TransactionId");

                    b.ToTable("DistributionTransaction");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.InvestmentAccount", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AccountNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("AccountType")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("varchar");

                    b.Property<decimal>("Balance")
                        .HasPrecision(15, 5)
                        .HasColumnType("decimal(15,5)");

                    b.Property<decimal>("ClosureBalance")
                        .HasPrecision(15, 5)
                        .HasColumnType("decimal(15,5)");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("InterestType")
                        .HasMaxLength(20)
                        .HasColumnType("varchar");

                    b.Property<int>("InvestorId")
                        .HasColumnType("int")
                        .HasColumnName("Investor");

                    b.Property<int>("LastModifiedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal?>("Rate")
                        .HasPrecision(7, 5)
                        .HasColumnType("decimal(7,5)");

                    b.Property<string>("Report1099Name")
                        .HasMaxLength(250)
                        .HasColumnType("varchar");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("AccountNumber")
                        .IsUnique();

                    b.HasIndex("CreatedBy");

                    b.HasIndex("InvestorId");

                    b.HasIndex("LastModifiedBy");

                    b.ToTable("InvestmentAccount");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.Investor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Apartment")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CompanyName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Fax")
                        .HasMaxLength(30)
                        .HasColumnType("varchar");

                    b.Property<string>("FirstName")
                        .HasMaxLength(80)
                        .HasColumnType("nvarchar(80)");

                    b.Property<string>("HomePhone")
                        .HasMaxLength(30)
                        .HasColumnType("varchar");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("LastModifiedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastName")
                        .HasMaxLength(80)
                        .HasColumnType("nvarchar(80)");

                    b.Property<string>("Mobile")
                        .HasMaxLength(30)
                        .HasColumnType("varchar");

                    b.Property<string>("Note")
                        .HasMaxLength(400)
                        .HasColumnType("varchar");

                    b.Property<string>("OfficePhone")
                        .HasMaxLength(30)
                        .HasColumnType("varchar");

                    b.Property<string>("State")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TaxNumber")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("varchar");

                    b.Property<string>("Zip")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("LastModifiedBy");

                    b.HasIndex("TaxNumber")
                        .IsUnique();

                    b.ToTable("Investor");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.MonthEndBalance", b =>
                {
                    b.Property<int>("AccountId")
                        .HasColumnType("int")
                        .HasColumnName("Account");

                    b.Property<decimal>("EndingBalance")
                        .HasPrecision(15, 5)
                        .HasColumnType("decimal(15,5)");

                    b.Property<int>("MonthEndProcessId")
                        .HasColumnType("int")
                        .HasColumnName("MonthEndProcess");

                    b.Property<decimal>("StartingBalance")
                        .HasPrecision(15, 5)
                        .HasColumnType("decimal(15,5)");

                    b.HasIndex("AccountId");

                    b.HasIndex("MonthEndProcessId");

                    b.ToTable("MonthEndBalance");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.MonthEndProcess", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("InvestmentRate")
                        .HasPrecision(7, 5)
                        .HasColumnType("decimal(7,5)");

                    b.Property<decimal>("LoanRate")
                        .HasPrecision(7, 5)
                        .HasColumnType("decimal(7,5)");

                    b.Property<decimal>("ManagementFee")
                        .HasPrecision(15, 5)
                        .HasColumnType("decimal(15,5)");

                    b.Property<decimal>("ManagementRate")
                        .HasPrecision(7, 5)
                        .HasColumnType("decimal(7,5)");

                    b.Property<int>("Month")
                        .HasColumnType("int");

                    b.Property<int>("Year")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("MonthEndProcess");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.MonthEndProcessTransaction", b =>
                {
                    b.Property<int>("MonthEndProcessId")
                        .HasColumnType("int")
                        .HasColumnName("MonthEndProcess");

                    b.Property<int>("TransactionId")
                        .HasColumnType("int")
                        .HasColumnName("Transaction");

                    b.HasIndex("MonthEndProcessId");

                    b.HasIndex("TransactionId");

                    b.ToTable("MonthEndProcessTransaction");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.Partnership", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Apartment")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("varchar");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("LastModifiedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar");

                    b.Property<string>("State")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Zip")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("LastModifiedBy");

                    b.ToTable("Partnership");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.PartnershipOwner", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AccountId")
                        .HasColumnType("int")
                        .HasColumnName("Account");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<int>("LastModifiedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<int>("PartnershipId")
                        .HasColumnType("int")
                        .HasColumnName("Partnership");

                    b.Property<decimal>("Percentage")
                        .HasPrecision(7, 5)
                        .HasColumnType("decimal(7,5)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("LastModifiedBy");

                    b.HasIndex("PartnershipId");

                    b.HasIndex("AccountId", "PartnershipId")
                        .IsUnique();

                    b.ToTable("PartnershipOwner");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.Setting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar");

                    b.Property<string>("Value")
                        .HasMaxLength(256)
                        .HasColumnType("varchar");

                    b.HasKey("Id");

                    b.ToTable("Setting");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.Transaction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AccountId")
                        .HasColumnType("int")
                        .HasColumnName("Account");

                    b.Property<decimal>("Amount")
                        .HasPrecision(15, 5)
                        .HasColumnType("decimal(15,5)");

                    b.Property<int>("CodeId")
                        .HasColumnType("int")
                        .HasColumnName("Code");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(80)
                        .HasColumnType("varchar");

                    b.Property<int>("LastModifiedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int")
                        .HasColumnName("Parent");

                    b.Property<int>("StatusId")
                        .HasColumnType("int")
                        .HasColumnName("Status");

                    b.Property<string>("WireNumber")
                        .HasMaxLength(50)
                        .HasColumnType("varchar");

                    b.HasKey("Id");

                    b.HasIndex("AccountId");

                    b.HasIndex("CodeId");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("LastModifiedBy");

                    b.HasIndex("ParentId");

                    b.HasIndex("StatusId");

                    b.ToTable("Transaction");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.TransactionCode", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(6)
                        .HasColumnType("varchar");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDebit")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSystem")
                        .HasColumnType("bit");

                    b.Property<bool>("IsTaxable")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.ToTable("TransactionCode");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.TransactionStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar");

                    b.HasKey("Id");

                    b.ToTable("TransactionStatus");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(80)
                        .HasColumnType("nvarchar(80)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("LastModifiedBy")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(80)
                        .HasColumnType("nvarchar(80)");

                    b.Property<string>("PasswordHash")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)");

                    b.Property<int>("UserRoleId")
                        .HasColumnType("int")
                        .HasColumnName("UserRole");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("LastModifiedBy");

                    b.HasIndex("UserRoleId");

                    b.ToTable("User");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.UserAccountPermission", b =>
                {
                    b.Property<int>("InvestmentAccountId")
                        .HasColumnType("int")
                        .HasColumnName("Account");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("User");

                    b.HasIndex("InvestmentAccountId");

                    b.HasIndex("UserId");

                    b.ToTable("UserAccountPermission");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.UserAccountStatement", b =>
                {
                    b.Property<int>("AccountId")
                        .HasColumnType("int")
                        .HasColumnName("Account");

                    b.Property<bool>("Email")
                        .HasColumnType("bit");

                    b.Property<bool>("Mail")
                        .HasColumnType("bit");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("User");

                    b.HasIndex("AccountId");

                    b.HasIndex("UserId");

                    b.ToTable("UserAccountStatement");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.UserRole", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(80)
                        .HasColumnType("varchar");

                    b.HasKey("Id");

                    b.ToTable("UserRole");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.AchRegister", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.Transaction", "Transaction")
                        .WithMany()
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.CheckRegister", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.Transaction", "Transaction")
                        .WithMany()
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.Distribution", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.Partnership", "Partnership")
                        .WithMany()
                        .HasForeignKey("PartnershipId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Partnership");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.DistributionTransaction", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.Distribution", "Distribution")
                        .WithMany()
                        .HasForeignKey("DistributionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.Transaction", "Transaction")
                        .WithMany()
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Distribution");

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.InvestmentAccount", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.User", "UserCreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.Investor", "AccountInvestor")
                        .WithMany()
                        .HasForeignKey("InvestorId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "UserModifiedBy")
                        .WithMany()
                        .HasForeignKey("LastModifiedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("AccountInvestor");

                    b.Navigation("UserCreatedBy");

                    b.Navigation("UserModifiedBy");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.Investor", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.User", "UserCreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "UserModifiedBy")
                        .WithMany()
                        .HasForeignKey("LastModifiedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("UserCreatedBy");

                    b.Navigation("UserModifiedBy");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.MonthEndBalance", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.InvestmentAccount", "InvestmentAccount")
                        .WithMany()
                        .HasForeignKey("AccountId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.MonthEndProcess", "MonthEndProcess")
                        .WithMany()
                        .HasForeignKey("MonthEndProcessId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("InvestmentAccount");

                    b.Navigation("MonthEndProcess");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.MonthEndProcessTransaction", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.MonthEndProcess", "MonthEndProcess")
                        .WithMany()
                        .HasForeignKey("MonthEndProcessId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.Transaction", "Transaction")
                        .WithMany()
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("MonthEndProcess");

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.Partnership", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.User", "UserCreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "UserModifiedBy")
                        .WithMany()
                        .HasForeignKey("LastModifiedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("UserCreatedBy");

                    b.Navigation("UserModifiedBy");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.PartnershipOwner", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.InvestmentAccount", "InvestmentAccount")
                        .WithMany()
                        .HasForeignKey("AccountId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "UserCreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "UserModifiedBy")
                        .WithMany()
                        .HasForeignKey("LastModifiedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.Partnership", "Partnership")
                        .WithMany()
                        .HasForeignKey("PartnershipId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("InvestmentAccount");

                    b.Navigation("Partnership");

                    b.Navigation("UserCreatedBy");

                    b.Navigation("UserModifiedBy");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.Transaction", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.InvestmentAccount", "InvestmentAccount")
                        .WithMany("Transactions")
                        .HasForeignKey("AccountId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.TransactionCode", "TransactionCode")
                        .WithMany()
                        .HasForeignKey("CodeId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "UserCreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "UserModifiedBy")
                        .WithMany()
                        .HasForeignKey("LastModifiedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.Transaction", "ParentTransaction")
                        .WithMany()
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("TIQuest.Api.Entities.TransactionStatus", "TransactionStatus")
                        .WithMany()
                        .HasForeignKey("StatusId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("InvestmentAccount");

                    b.Navigation("ParentTransaction");

                    b.Navigation("TransactionCode");

                    b.Navigation("TransactionStatus");

                    b.Navigation("UserCreatedBy");

                    b.Navigation("UserModifiedBy");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.User", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.User", "UserCreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "UserModifiedBy")
                        .WithMany()
                        .HasForeignKey("LastModifiedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.UserRole", "UserRole")
                        .WithMany()
                        .HasForeignKey("UserRoleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("UserCreatedBy");

                    b.Navigation("UserModifiedBy");

                    b.Navigation("UserRole");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.UserAccountPermission", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.InvestmentAccount", "InvestmentAccount")
                        .WithMany()
                        .HasForeignKey("InvestmentAccountId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("InvestmentAccount");

                    b.Navigation("User");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.UserAccountStatement", b =>
                {
                    b.HasOne("TIQuest.Api.Entities.InvestmentAccount", "InvestmentAccount")
                        .WithMany()
                        .HasForeignKey("AccountId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("TIQuest.Api.Entities.User", "AssociatedUser")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("AssociatedUser");

                    b.Navigation("InvestmentAccount");
                });

            modelBuilder.Entity("TIQuest.Api.Entities.InvestmentAccount", b =>
                {
                    b.Navigation("Transactions");
                });
#pragma warning restore 612, 618
        }
    }
}
