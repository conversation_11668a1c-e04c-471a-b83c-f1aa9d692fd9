﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TIQuest.Api.Migrations
{
    /// <inheritdoc />
    public partial class InvestmentAccountDetails : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TaxNumber",
                table: "InvestmentAccount");

            migrationBuilder.AddColumn<string>(
                name: "Fax",
                table: "Investor",
                type: "varchar(30)",
                maxLength: 30,
                nullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "Rate",
                table: "InvestmentAccount",
                type: "decimal(7,5)",
                precision: 7,
                scale: 5,
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(7,5)",
                oldPrecision: 7,
                oldScale: 5);

            migrationBuilder.AddColumn<string>(
                name: "AccountType",
                table: "InvestmentAccount",
                type: "varchar(30)",
                maxLength: 30,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "InterestType",
                table: "InvestmentAccount",
                type: "varchar(20)",
                maxLength: 20,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Fax",
                table: "Investor");

            migrationBuilder.DropColumn(
                name: "AccountType",
                table: "InvestmentAccount");

            migrationBuilder.DropColumn(
                name: "InterestType",
                table: "InvestmentAccount");

            migrationBuilder.AlterColumn<decimal>(
                name: "Rate",
                table: "InvestmentAccount",
                type: "decimal(7,5)",
                precision: 7,
                scale: 5,
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(decimal),
                oldType: "decimal(7,5)",
                oldPrecision: 7,
                oldScale: 5,
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TaxNumber",
                table: "InvestmentAccount",
                type: "nvarchar(30)",
                maxLength: 30,
                nullable: false,
                defaultValue: "");
        }
    }
}
