﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TIQuest.Api.Migrations
{
    /// <inheritdoc />
    public partial class MonthEndBalances : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MonthEndBalance",
                columns: table => new
                {
                    MonthEndProcess = table.Column<int>(type: "int", nullable: false),
                    Account = table.Column<int>(type: "int", nullable: false),
                    StartingBalance = table.Column<decimal>(type: "decimal(15,5)", precision: 15, scale: 5, nullable: false),
                    EndingBalance = table.Column<decimal>(type: "decimal(15,5)", precision: 15, scale: 5, nullable: false)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_MonthEndBalance_InvestmentAccount_Account",
                        column: x => x.Account,
                        principalTable: "InvestmentAccount",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_MonthEndBalance_MonthEndProcess_MonthEndProcess",
                        column: x => x.MonthEndProcess,
                        principalTable: "MonthEndProcess",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_MonthEndBalance_Account",
                table: "MonthEndBalance",
                column: "Account");

            migrationBuilder.CreateIndex(
                name: "IX_MonthEndBalance_MonthEndProcess",
                table: "MonthEndBalance",
                column: "MonthEndProcess");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MonthEndBalance");
        }
    }
}
