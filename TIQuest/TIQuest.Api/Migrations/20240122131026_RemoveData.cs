﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace TIQuest.Api.Migrations
{
    /// <inheritdoc />
    public partial class RemoveData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "InvestmentAccount",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "MonthEndProcess",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "Setting",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "TransactionCode",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "TransactionCode",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "TransactionCode",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "TransactionCode",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "TransactionCode",
                keyColumn: "Id",
                keyValue: 5);

            migrationBuilder.DeleteData(
                table: "TransactionCode",
                keyColumn: "Id",
                keyValue: 6);

            migrationBuilder.DeleteData(
                table: "TransactionCode",
                keyColumn: "Id",
                keyValue: 7);

            migrationBuilder.DeleteData(
                table: "TransactionCode",
                keyColumn: "Id",
                keyValue: 8);

            migrationBuilder.DeleteData(
                table: "TransactionCode",
                keyColumn: "Id",
                keyValue: 9);

            migrationBuilder.DeleteData(
                table: "TransactionCode",
                keyColumn: "Id",
                keyValue: 10);

            migrationBuilder.DeleteData(
                table: "TransactionStatus",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "TransactionStatus",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "TransactionStatus",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "TransactionStatus",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "UserRole",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "UserRole",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "Investor",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "User",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "UserRole",
                keyColumn: "Id",
                keyValue: 1);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "MonthEndProcess",
                columns: new[] { "Id", "Description", "InvestmentRate", "LoanRate", "ManagementFee", "ManagementRate", "Month", "Year" },
                values: new object[] { 1, "Initial Seed", 0m, 0m, 0m, 0m, 1, 2000 });

            migrationBuilder.InsertData(
                table: "Setting",
                columns: new[] { "Id", "Key", "Value" },
                values: new object[] { 1, "LastCheckNumber", "0" });

            migrationBuilder.InsertData(
                table: "TransactionCode",
                columns: new[] { "Id", "Code", "Description", "IsActive", "IsDebit", "IsSystem", "IsTaxable" },
                values: new object[,]
                {
                    { 1, "I-DEP", "Deposit", true, false, false, false },
                    { 2, "I-WDL", "Withdrawal", true, true, false, false },
                    { 3, "L-PMT", "Loan Payment", true, false, false, false },
                    { 4, "L-ADV", "Loan Advance", true, true, false, false },
                    { 5, "L-INT", "Loan Interest", true, false, false, true },
                    { 6, "I-INT", "Investment Interest", true, false, false, true },
                    { 7, "XFER", "Transfer", true, false, false, false },
                    { 8, "VADJ", "Void Adjustment", true, false, false, false },
                    { 9, "FEE", "Management Fee", true, false, false, false },
                    { 10, "LXFER", "Loan Transfer", true, false, false, false }
                });

            migrationBuilder.InsertData(
                table: "TransactionStatus",
                columns: new[] { "Id", "Description" },
                values: new object[,]
                {
                    { 1, "Outstanding" },
                    { 2, "Reconciled" },
                    { 3, "Void" },
                    { 4, "Pending" }
                });

            migrationBuilder.InsertData(
                table: "UserRole",
                columns: new[] { "Id", "Description" },
                values: new object[,]
                {
                    { 1, "Administrator" },
                    { 2, "Client" },
                    { 3, "Auditor" }
                });

            migrationBuilder.InsertData(
                table: "User",
                columns: new[] { "Id", "CreatedBy", "CreatedOn", "Email", "FirstName", "IsActive", "LastModifiedBy", "LastModifiedOn", "LastName", "PasswordHash", "UserRole" },
                values: new object[] { 1, 1, new DateTime(2023, 11, 9, 13, 4, 40, 521, DateTimeKind.Utc).AddTicks(3737), "<EMAIL>", "System", false, 1, new DateTime(2023, 11, 9, 13, 4, 40, 521, DateTimeKind.Utc).AddTicks(3737), "Admin", null, 1 });

            migrationBuilder.InsertData(
                table: "Investor",
                columns: new[] { "Id", "Address", "Apartment", "City", "CompanyName", "Country", "CreatedBy", "CreatedOn", "Fax", "FirstName", "HomePhone", "IsActive", "LastModifiedBy", "LastModifiedOn", "LastName", "Mobile", "Note", "OfficePhone", "State", "TaxNumber", "Type", "Zip" },
                values: new object[] { 1, "System", null, null, "System", "USA", 1, new DateTime(2024, 1, 17, 9, 19, 53, 304, DateTimeKind.Utc).AddTicks(3744), null, "Triton", null, true, 1, new DateTime(2024, 1, 17, 9, 19, 53, 304, DateTimeKind.Utc).AddTicks(3746), "Investments", null, null, null, null, "0", "Company", null });

            migrationBuilder.InsertData(
                table: "InvestmentAccount",
                columns: new[] { "Id", "AccountNumber", "AccountType", "Balance", "CreatedBy", "CreatedOn", "EndDate", "InterestType", "Investor", "LastModifiedBy", "LastModifiedOn", "Name", "Rate", "Report1099Name", "StartDate" },
                values: new object[] { 1, "1", "Cash", 0m, 1, new DateTime(2024, 1, 17, 9, 19, 53, 304, DateTimeKind.Utc).AddTicks(3766), null, "None", 1, 1, new DateTime(2024, 1, 17, 9, 19, 53, 304, DateTimeKind.Utc).AddTicks(3767), "Cash Account", 0m, null, new DateTime(2000, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified) });
        }
    }
}
