﻿using System.ComponentModel.DataAnnotations;

namespace TIQuest.Api.Poco.Entities
{
    public class InvestmentAccount
    {
        public int Id { get; set; }
        public Investor Investor { get; set; }
        public string Name { get; set; }
        public string? Email { get; set; }
        public string? Report1099Name { get; set; }
        public string AccountNumber { get; set; }
        public decimal? Rate { get; set; }
        public decimal Balance { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string AccountType { get; set; }
        public string? InterestType { get; set; }
        public bool IsActive => EndDate is null || EndDate > DateTimeOffset.UtcNow;
        public bool? SendEmail { get; set; }
        public bool? SendMail { get; set; }
        public string? TaxNumber { get; set; }
    }
}
