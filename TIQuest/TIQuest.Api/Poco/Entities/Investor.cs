﻿namespace TIQuest.Api.Poco.Entities
{
    /// <summary>
    /// Poco class for Investor
    /// </summary>
    public class Investor
    {
        public int Id { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public string? CompanyName { get; set; }
        public string? TaxNumber { get; set; }
        public string Type { get; set; }
        public string? OfficePhone { get; set; }
        public string? HomePhone { get; set; }
        public string? Mobile { get; set; }
        public string? Fax { get; set; }
        public string? Apartment { get; set; }
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? Zip { get; set; }
        public string Country { get; set; }
        public string? Note { get; set; }
        public bool IsActive { get; set; }
    }
}
