﻿namespace TIQuest.Api.Poco.Entities
{
	public class Transaction
	{
		public int Id { get; set; }
		public string Code { get; set; }
		public decimal Amount { get; set; }
		public DateOnly Date { get; set; }
		public string? Description { get; set; }
		public InvestmentAccount Account { get; set; }
		public Transaction? ParentTransaction { get; set; }
		public TransactionStatus TransactionStatus { get; set; }
		public string? WireNumber { get; set; }
		public CheckRegister? CheckRegister { get; set; }
		public string? AchDetails { get; set; }
		public DateTime CreatedOn { get; set; }
		public string? BankTransactionType { get; set; }
	}
}
