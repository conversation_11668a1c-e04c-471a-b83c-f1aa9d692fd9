﻿namespace TIQuest.Api.Poco.Entities
{
    /// <summary>
    /// Poco class for User
    /// </summary>
    public class User
    {
        public int Id { get; set; }
        public string Role { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string? PasswordHash { get; set; }
        public bool IsActive { get; set; }
        public IEnumerable<int> Accounts { get; set; }
        public IEnumerable<UserStatement> Statements { get; set; }
    }
}
