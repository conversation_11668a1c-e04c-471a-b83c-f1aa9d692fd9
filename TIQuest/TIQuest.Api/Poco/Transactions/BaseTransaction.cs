﻿namespace TIQuest.Api.Poco.Transactions
{
    public abstract record BaseTransaction
    {
        public int Account { get; init; }
        public DateTime Date { get; init; }
        public int Code { get; init; }
        public int Status { get; init; }
        public decimal Amount { get; init; }
        public string? Description { get; init; }
        public int ModifierId { get; init; }
        public string? BankTransactionType { get; set; }
    }
}
