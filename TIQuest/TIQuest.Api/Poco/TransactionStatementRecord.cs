﻿namespace TIQuest.Api.Poco
{
    public class TransactionStatementRecord
    {
        public TransactionInvestor Investor { get; set; }
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? AccountNumber { get; set; }
        public decimal? StartingBalance { get; set; }
        public decimal? Debit { get; set; }
        public decimal? Credit { get; set; }
        public decimal? Interest { get; set; }
        public decimal? EndingBalance { get; set; }

        public string InvestorName => !string.IsNullOrWhiteSpace(Investor.CompanyName) ? Investor.CompanyName : $"{Investor.LastName} {Investor.FirstName}";
    }

    public class TransactionInvestor
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? CompanyName { get; set; }
    }
}
