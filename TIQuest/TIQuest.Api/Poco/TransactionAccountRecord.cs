﻿using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Poco
{
    public class TransactionAccountRecord
    {
        public int? Id { get; set; }
        public Investor? Investor { get; set; }
        public string? AccountNumber { get; set; }
        public string? Name { get; set; }
        public decimal? Balance { get; set; }
        public ICollection<Transaction> Transactions { get; set; }
    }
}
