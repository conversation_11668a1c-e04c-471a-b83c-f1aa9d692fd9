﻿using CsvHelper.Configuration.Attributes;

namespace TIQuest.Api.Poco
{
    public class TransactionRecordWithBalance
    {
        public string ClientName { get; set; }
        public string AccountName { get; set; }
        public string AccountNumber { get; set; }
        public DateOnly Date { get; set; }
        [Ignore]
        public DateTime CreatedDate { get; set; }
        public string? Description { get; set; }
        public string Status { get; set; }
        public decimal Amount { get; set; }
        public decimal Balance { get; set; }
		   public string? BankTransactionType { get; set; }
	}
}
