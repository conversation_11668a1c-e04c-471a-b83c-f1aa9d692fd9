﻿using System.Globalization;
using TIQuest.Api.Extensions;

namespace TIQuest.Api.Poco.Reports
{
    public record Check
    (
        int Id,
        int? Number,
        string? AccountNumber,
        string? Memo,
        decimal Amount,
        DateTime Date,
        string? Payee,
        string? Address,
        string? Apartment,
        string? City,
        string? State,
        string? Zip,
        string? Country
    )
    {
        public string CombinedAddress => $"{Apartment}, {Address}";
        public string CityStateZip => $"{City}, {State} {Zip}";
        public string DateText => Date.ToShortDateString();
        public string AmountText => Amount.ToString("C", CultureInfo.GetCultureInfo("en-US"));
        public string AmountInWords => GetAmountInWords(Amount);

        private static string GetAmountInWords(decimal amount)
        {
            var dataString = $"** {amount.ConvertToWords()}";
            return dataString.PadRight(130 - dataString.Length, '*');
        }
    }
}
