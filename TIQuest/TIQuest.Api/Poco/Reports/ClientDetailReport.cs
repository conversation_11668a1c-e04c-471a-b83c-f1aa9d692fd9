﻿using CsvHelper.Configuration.Attributes;

namespace TIQuest.Api.Poco.Reports
{
    public class ClientDetailReport
    {
        [Ignore]
        public int AccountId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? CompanyName { get; set; }
        public string? TaxNumber { get; set; }
        public string? AccountName { get; set; }
        public string? AccountNumber { get; set; }
        public string? UserEmail { get; set; }
        public string? ClientEmail { get; set; }
        public string? AccountEmail { get; set; }
        public string? Type { get; set; }
        public string? AccountType { get; set; }
        public bool? IsActive { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Report1099Name { get; set; }
        public string? Address { get; set; }
        public string? Apartment { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? Zip { get; set; }
        public string? Country { get; set; }
        public string? AssociatedPartnership { get; set; }
    }
}
