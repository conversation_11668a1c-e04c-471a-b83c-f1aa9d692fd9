﻿namespace TIQuest.Api.Poco.Reports
{
    public record CompanyReport
    (
        string? Name,
        string? FirstName,
        string? LastName,
        string? Fax,
        string? OfficePhone,
        string? Email,
        string? TaxNumber,
        string? Address1,
        string? Address2,
        string? City,
        string? State,
        string? PostalCode
    )
    {
        public string Address => $"{Address1}{(string.IsNullOrWhiteSpace(Address1) || string.IsNullOrWhiteSpace(Address2) ? "" : ", ")}{Address2}";
        public string CityStateZip => $"{City}{(string.IsNullOrWhiteSpace(City) ? "" : ", ")}{State} {PostalCode}";
    };
}
