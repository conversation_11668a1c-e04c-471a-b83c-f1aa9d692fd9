﻿using CsvHelper.Configuration.Attributes;

namespace TIQuest.Api.Poco.Reports
{
    public class Statement1099Row
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? ClientCompanyName { get; set; }
        [Ignore]
        public string? CompanyName { get; set; }
        public string? AccountName { get; set; }
        public string? Report1099Name { get; set; }
        public string? TaxID { get; set; }
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? PostalCode { get; set; }
        [Ignore]
        public string? CityStateZip => $"{City}, {State} {PostalCode}";
        [Ignore]
        public string? CompanyAddress { get; set; }
        [Ignore]
        public string? CompanyCityStateZip { get; set; }
        public decimal InterestTotal { get; set; }
        [Ignore]
        public string? PayerTaxID { get; set; }
        public decimal? FedIncomeTaxWithheld { get; set; }
        public string? AccountNumber { get; set; }
        [Ignore]
        public string? FullName { get; set; }
        [Ignore]
        public int ClientId { get; set; }
        [Ignore]
        public string? Country { get; set; }
    }
}
