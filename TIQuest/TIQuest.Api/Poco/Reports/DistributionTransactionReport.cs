namespace TIQuest.Api.Poco.Reports
{
    public class DistributionTransactionReport
    {
        public int DistributionId { get; set; }
        public string PartnershipName { get; set; } = string.Empty;
        public DateOnly DistributionDate { get; set; }
        public decimal TotalAmount { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public string CompanyAddress { get; set; } = string.Empty;
        public string CompanyCityStateZip { get; set; } = string.Empty;
        public string CompanyEmail { get; set; } = string.Empty;
        public string CompanyPhone { get; set; } = string.Empty;
        public string ReceiptNumber { get; set; } = string.Empty;
        public DateTime AsOfDate { get; set; }
        public string ClientAddress1 { get; set; } = string.Empty;
        public string ClientAddress2 { get; set; } = string.Empty;
        public string ClientCityStateZip { get; set; } = string.Empty;
        public string ClientCountry { get; set; } = string.Empty;
        public IEnumerable<DistributionTransactionDetail> Transactions { get; set; } = [];
    }

    public class DistributionTransactionDetail
    {
        public int Id { get; set; }
        public DateOnly Date { get; set; }
        public string Description { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string AccountName { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
        // Investor information for client details
        public string InvestorCompanyName { get; set; } = string.Empty;
        public string InvestorAddress1 { get; set; } = string.Empty;
        public string InvestorCityStateZip { get; set; } = string.Empty;
        public string InvestorCountry { get; set; } = string.Empty;
        public string InvestorAddress2 { get; set; } = string.Empty;
        // Transaction-specific AsOfDate
        public string TransactionAsOfDate { get; set; } = string.Empty;
    }
}
