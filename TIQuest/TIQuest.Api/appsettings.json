{"ConnectionStrings": {"TIQuest": ""}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Jwt": {"Secret": "", "Issuer": "", "Audience": "", "ExpirationMinutes": 0}, "ApplicationConfig": {"DomainBaseUrl": "", "ResetTokenExpirationHours": 24}, "EmailConfig": {"ContainerName": "", "QueueName": "", "EmailId": "", "Name": ""}, "Azure": {"Storage": {"ConnectionString": ""}}}