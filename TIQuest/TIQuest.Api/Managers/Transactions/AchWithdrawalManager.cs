﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TIQuest.Api.DTO.Request.Transactions;
using TIQuest.Api.Entities;

namespace TIQuest.Api.Managers.Transactions
{
    internal class AchWithdrawalManager : WithdrawalManager
    {
        public AchWithdrawalManager(IDbContextFactory<DataContext> dbContextFactory, IMapper mapper) : base(dbContextFactory, mapper)
        {
        }

        protected override async Task<Transaction> CreateFromEntitiesAsync(TransactionRequest request, int modifierId, DataContext ctx, CancellationToken cancellationToken)
        {
            Transaction transaction = await base.CreateFromEntitiesAsync(request, modifierId, ctx, cancellationToken);

            // ==> Removed by <PERSON> as part of TI-2000: Shouldn't be added ACH Batch
            //var achRegister = CreateAchRegister(((AchWithdrawalRequest)request).AchDetails, transaction);

            //await ctx.AchRegisters.AddAsync(achRegister, cancellationToken);

            return transaction;
        }

        private static AchRegister CreateAchRegister(string? achDetails, Transaction transaction)
        {
            AchRegister achRegister = new()
            {
                Description = achDetails,
                Transaction = transaction,
                Date = transaction.Date
            };

            return achRegister;
        }
    }
}
