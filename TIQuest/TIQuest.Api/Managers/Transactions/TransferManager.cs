﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TIQuest.Api.DTO.Request.Transactions;
using TIQuest.Api.Entities;

namespace TIQuest.Api.Managers.Transactions
{
    internal class TransferManager : TransactionManager
    {
        public TransferManager(IDbContextFactory<DataContext> dbContextFactory, IMapper mapper) : base(dbContextFactory, mapper)
        {
        }

        protected override async Task<Transaction> CreateFromEntitiesAsync(TransactionRequest request, int modifierId, DataContext ctx, CancellationToken cancellationToken)
        {
            Transaction transaction = await base.CreateFromEntitiesAsync(request, modifierId, ctx, cancellationToken);
            transaction.Amount = request.Amount * -1;

            return transaction;
        }

        protected override async Task UpdateFromAccountBalanceAsync(DataContext ctx, Transaction fromEntity, CancellationToken cancellationToken)
        {
            InvestmentAccount? account = await ctx.InvestmentAccounts.FindAsync(new object?[] { fromEntity.AccountId }, cancellationToken: cancellationToken);
            if (account is null)
            {
                throw new ArgumentException("Unable to find account.");
            }
            decimal amount = Math.Abs(fromEntity.Amount);
            account.Balance -= amount;
            ctx.InvestmentAccounts.Update(account);
        }

        protected override async Task<Transaction> CreateToEntitiesAsync(TransactionRequest request, Transaction fromEntity, DataContext ctx, CancellationToken cancellationToken)
        {
            Transaction toTransaction = await base.CreateToEntitiesAsync(request, fromEntity, ctx, cancellationToken);
            toTransaction.StatusId = fromEntity.StatusId;
            toTransaction.Amount = request.Amount;
            toTransaction.AccountId = ((TransferTransactionRequest)request).ToAccount;

            return toTransaction;
        }

        protected override async Task UpdateToAccountBalanceAsync(DataContext ctx, Transaction toEntity, CancellationToken cancellationToken)
        {
            InvestmentAccount? account = await ctx.InvestmentAccounts.FindAsync(new object?[] { toEntity.AccountId }, cancellationToken: cancellationToken);
            if (account is null)
            {
                throw new ArgumentException("Unable to find account.");
            }
            account.Balance += toEntity.Amount;
            ctx.InvestmentAccounts.Update(account);
        }
    }
}
