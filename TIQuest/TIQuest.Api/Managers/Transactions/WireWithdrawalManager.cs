﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TIQuest.Api.DTO.Request.Transactions;
using TIQuest.Api.Entities;

namespace TIQuest.Api.Managers.Transactions
{
    internal class WireWithdrawalManager : WithdrawalManager
    {
        public WireWithdrawalManager(IDbContextFactory<DataContext> dbContextFactory, IMapper mapper) : base(dbContextFactory, mapper)
        {
        }

        protected override async Task<Transaction> CreateToEntitiesAsync(TransactionRequest request, Transaction fromEntity, DataContext ctx, CancellationToken cancellationToken)
        {
            var tran = await base.CreateToEntitiesAsync(request, fromEntity, ctx, cancellationToken);
            tran.WireNumber = null;

            return tran;
        }
    }
}
