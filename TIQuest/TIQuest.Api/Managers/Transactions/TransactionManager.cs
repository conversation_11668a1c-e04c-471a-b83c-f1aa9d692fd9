﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TIQuest.Api.DTO.Request.Transactions;
using TIQuest.Api.Entities;
using TIQuest.Api.Interfaces.Managers;

namespace TIQuest.Api.Managers.Transactions
{
    internal abstract class TransactionManager : ITransactionManager
    {
        private readonly IDbContextFactory<DataContext> _contextFactory;
        private readonly IMapper _mapper;
        protected IMapper Mapper => _mapper;

        public TransactionManager(IDbContextFactory<DataContext> dbContextFactory, IMapper mapper)
        {
            _contextFactory = dbContextFactory;
            _mapper = mapper;
        }

        public async Task<int> CreateTransactionAsync(TransactionRequest request, int modifierId, CancellationToken cancellationToken = default)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            using (var dbTransaction = await ctx.Database.BeginTransactionAsync(cancellationToken))
            {
                // primary transaction
                Transaction fromTransaction = await InsertFromEntitiesAsync(request, modifierId, ctx, cancellationToken);
                await ctx.Transactions.AddAsync(fromTransaction, cancellationToken);
                // counter transaction
                Transaction toTransaction = await InsertToEntitiesAsync(request, modifierId, fromTransaction, ctx, cancellationToken);
                await ctx.Transactions.AddAsync(toTransaction, cancellationToken);

                await ctx.SaveChangesAsync(cancellationToken);

                await dbTransaction.CommitAsync(cancellationToken);

                return fromTransaction.Id;
            }
        }

        protected virtual decimal GetAmount(decimal amount) => amount;

        protected virtual async Task<Transaction> InsertFromEntitiesAsync(TransactionRequest request, int modifierId, DataContext ctx, CancellationToken cancellationToken)
        {
            Transaction fromEntity = await CreateFromEntitiesAsync(request, modifierId, ctx, cancellationToken);

            await UpdateFromAccountBalanceAsync(ctx, fromEntity, cancellationToken);

            return fromEntity;
        }

        protected abstract Task UpdateFromAccountBalanceAsync(DataContext ctx, Transaction fromEntity, CancellationToken cancellationToken);

        protected virtual async Task<Transaction> InsertToEntitiesAsync(TransactionRequest request, int modifierId, Transaction fromEntity, DataContext ctx, CancellationToken cancellationToken)
        {
            Transaction toEntity = await CreateToEntitiesAsync(request, fromEntity, ctx, cancellationToken);

            await UpdateToAccountBalanceAsync(ctx, toEntity, cancellationToken);

            return toEntity;
        }

        protected abstract Task UpdateToAccountBalanceAsync(DataContext ctx, Transaction toEntity, CancellationToken cancellationToken);

        protected virtual async Task<Transaction> CreateFromEntitiesAsync(TransactionRequest request, int modifierId, DataContext ctx, CancellationToken cancellationToken)
        {
            Transaction fromTransaction = _mapper.Map<Transaction>(request);
            fromTransaction.StatusId = await GetStatusIdAsync(cancellationToken);
            fromTransaction.Amount = GetAmount(request.Amount);
            fromTransaction.LastModifiedBy = modifierId;
            fromTransaction.CreatedBy = modifierId;

            if (request is AchWithdrawalRequest achWithdrawalRequest &&
                !string.IsNullOrWhiteSpace(achWithdrawalRequest.AchDetails))
            {
                fromTransaction.AchDetails = achWithdrawalRequest.AchDetails;
            }

            return fromTransaction;
        }

        protected virtual Task<Transaction> CreateToEntitiesAsync(TransactionRequest request, Transaction fromEntity, DataContext ctx, CancellationToken cancellationToken)
        {
            Transaction toTransaction = _mapper.Map<Transaction>(request);
            
            if (request is AchWithdrawalRequest achWithdrawalRequest &&
                !string.IsNullOrWhiteSpace(achWithdrawalRequest.AchDetails))
            {
                toTransaction.AchDetails = achWithdrawalRequest.AchDetails;
            }
            
            // cash account
            toTransaction.StatusId = fromEntity.StatusId;
            toTransaction.Amount = fromEntity.Amount;
            toTransaction.AccountId = Constants.DataConstants.CashAccountId;
            toTransaction.ParentTransaction = fromEntity;
            toTransaction.LastModifiedBy = fromEntity.LastModifiedBy;
            toTransaction.CreatedBy = fromEntity.CreatedBy;

            return Task.FromResult(toTransaction);
        }

        private async Task<int> GetStatusIdAsync(CancellationToken cancellationToken)
        {
            using (DataContext ctx = await _contextFactory.CreateDbContextAsync(cancellationToken))
            {
                TransactionStatus? status = await ctx.TransactionStatuses.FirstOrDefaultAsync(s => s.Description == Constants.DataConstants.OutstandingStatus, cancellationToken);
                if (status is null)
                {
                    throw new InvalidOperationException("Unable to find transaction status.");
                }
                return status.Id;
            }
        }

        protected virtual void ModifyAccountBalance(InvestmentAccount account, decimal difference, int modifierId)
        {
            account.Balance += difference;
            account.LastModifiedBy = modifierId;
            account.LastModifiedOn = DateTime.UtcNow;
        }
    }
}
