﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TIQuest.Api.DTO.Request.Transactions;
using TIQuest.Api.Entities;

namespace TIQuest.Api.Managers.Transactions
{
    internal class CheckWithdrawalManager : WithdrawalManager
    {
        public CheckWithdrawalManager(IDbContextFactory<DataContext> dbContextFactory, IMapper mapper) : base(dbContextFactory, mapper)
        {
        }

        protected override async Task<Transaction> CreateFromEntitiesAsync(TransactionRequest request, int modifierId, DataContext ctx, CancellationToken cancellationToken)
        {
            Transaction transaction = await base.CreateFromEntitiesAsync(request, modifierId, ctx, cancellationToken);
            CheckRegister checkRegister = CreateCheckRegister(request, transaction);

            await ctx.CheckRegisters.AddAsync(checkRegister, cancellationToken);

            return transaction;
        }

        private CheckRegister CreateCheckRegister(TransactionRequest request, Transaction transaction)
        {
            CheckRegister checkRegister = Mapper.Map<CheckRegister>(request);
            checkRegister.Transaction = transaction;
            return checkRegister;
        }
    }
}
