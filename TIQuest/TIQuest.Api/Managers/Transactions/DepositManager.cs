﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TIQuest.Api.Entities;

namespace TIQuest.Api.Managers.Transactions
{
    internal class DepositManager : TransactionManager
    {
        public DepositManager(IDbContextFactory<DataContext> dbContextFactory, IMapper mapper)
            : base(dbContextFactory, mapper)
        {
        }

        protected override Task UpdateFromAccountBalanceAsync(DataContext ctx, Transaction fromEntity, CancellationToken cancellationToken)
        {
            return UpdateAccountBalanceAsync(ctx, fromEntity, cancellationToken);
        }

        protected override Task UpdateToAccountBalanceAsync(DataContext ctx, Transaction toEntity, CancellationToken cancellationToken)
        {
            return UpdateAccountBalanceAsync(ctx, toEntity, cancellationToken);
        }

        private async Task UpdateAccountBalanceAsync(DataContext ctx, Transaction entity, CancellationToken cancellationToken)
        {
            InvestmentAccount? account = await ctx.InvestmentAccounts.FindAsync(new object?[] { entity.AccountId }, cancellationToken: cancellationToken);
            if (account is null)
            {
                throw new ArgumentException("Unable to find account.");
            }
            account.Balance += entity.Amount;
            ctx.InvestmentAccounts.Update(account);
        }
    }
}
