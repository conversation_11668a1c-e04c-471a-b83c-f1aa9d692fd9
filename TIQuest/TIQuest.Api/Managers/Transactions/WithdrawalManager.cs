﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TIQuest.Api.Entities;

namespace TIQuest.Api.Managers.Transactions
{
    internal abstract class WithdrawalManager : TransactionManager
    {
        protected WithdrawalManager(IDbContextFactory<DataContext> dbContextFactory, IMapper mapper) : base(dbContextFactory, mapper)
        {
        }

        protected override decimal GetAmount(decimal amount) => amount > 0 ? amount * -1 : amount;

        protected override async Task UpdateFromAccountBalanceAsync(DataContext ctx, Transaction fromEntity, CancellationToken cancellationToken)
        {
            InvestmentAccount? account = await ctx.InvestmentAccounts.FindAsync(new object?[] { fromEntity.AccountId }, cancellationToken: cancellationToken);
            if (account is null)
            {
                throw new ArgumentException("Unable to find account.");
            }
            decimal amount = Math.Abs(fromEntity.Amount);

            account.Balance -= amount;
            ctx.InvestmentAccounts.Update(account);
        }

        protected override async Task UpdateToAccountBalanceAsync(DataContext ctx, Transaction toEntity, CancellationToken cancellationToken)
        {
            InvestmentAccount? account = await ctx.InvestmentAccounts.FindAsync(new object?[] { toEntity.AccountId }, cancellationToken: cancellationToken);
            if (account is null)
            {
                throw new ArgumentException("Unable to find account.");
            }
            account.Balance += toEntity.Amount;
            ctx.InvestmentAccounts.Update(account);
        }
    }
}
