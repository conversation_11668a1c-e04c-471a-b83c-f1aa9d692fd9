﻿using System.Security.Claims;
using TIQuest.Api.Interfaces.Managers;

namespace TIQuest.Api.Managers
{
    internal sealed class ClaimsManager : IClaimsManager
    {
        private readonly ICollection<Claim> _claims;
        public ICollection<Claim> Claims => _claims;
        public ClaimsManager()
        {
            _claims = new List<Claim>();
        }

        public IClaimsManager AddClaim(string type, string value, string? valueType = ClaimValueTypes.String)
        {
            _claims.Add(new Claim(type, value, valueType));
            return this;
        }
    }
}
