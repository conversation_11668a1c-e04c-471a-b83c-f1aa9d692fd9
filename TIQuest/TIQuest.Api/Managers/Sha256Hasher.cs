﻿using System.Security.Cryptography;
using System.Text;
using TIQuest.Api.Interfaces.Managers;

namespace TIQuest.Api.Managers
{
    internal sealed class Sha256Hasher : IHasher
    {
        /// <inheritdoc/>
        public bool CompareHash(string plainText, string hashToCompare)
        {
            ArgumentException.ThrowIfNullOrEmpty(plainText);
            ArgumentException.ThrowIfNullOrEmpty(hashToCompare);

            string hashedText = CreateHash(plainText);
            return string.Equals(hashToCompare, hashedText, StringComparison.Ordinal);
        }

        /// <inheritdoc/>
        public string CreateHash(string plainText)
        {
            ArgumentException.ThrowIfNullOrEmpty(plainText);
            return Convert.ToBase64String(SHA256.HashData(Encoding.UTF8.GetBytes(plainText)));
        }
    }
}
