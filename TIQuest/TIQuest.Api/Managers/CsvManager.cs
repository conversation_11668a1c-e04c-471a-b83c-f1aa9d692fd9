﻿using CsvHelper;
using System.Globalization;
using TIQuest.Api.Interfaces.Managers;

namespace TIQuest.Api.Managers
{
    internal class CsvManager : ICsvManager
    {
        public async Task<Stream> WriteAsync<T>(IEnumerable<T> entitySet, CancellationToken cancellationToken = default)
        {
            using (var memoryStream = new MemoryStream())
            using (var writer = new StreamWriter(memoryStream))
            using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                await csv.WriteRecordsAsync(entitySet);
                await writer.FlushAsync();
                memoryStream.Seek(0, SeekOrigin.Begin);
                var cloneStream = new MemoryStream();
                await memoryStream.CopyToAsync(cloneStream);
                cloneStream.Seek(0, SeekOrigin.Begin);
                return cloneStream;
            }
        }
    }
}
