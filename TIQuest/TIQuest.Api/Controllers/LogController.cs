﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TIQuest.Api.DTO.Response;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Controllers
{
    [Route("logs")]
    [ApiController]
    [Authorize(Roles = Constants.RoleConstants.AdminRole)]
    public class LogController : ControllerBase
    {
        [Route("email")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<IEnumerable<EmailLogResponse>>> GetEmailLogs(
            [FromServices] IEmailLogService emailLogService,
            [FromServices] IMapper mapper)
        {
            var emailLogs = await emailLogService.GetAllAsync();
            if (!emailLogs.Any())
            {
                return Ok(new List<EmailLogResponse>());
            }
            return Ok(mapper.Map<IEnumerable<EmailLogResponse>>(emailLogs));
        }

        [Route("audit")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<IEnumerable<AuditLogResponse>>> GetAuditLogs(
            [FromServices] IAuditLogService auditLogService,
            [FromServices] IMapper mapper)
        {
            var auditLogs = await auditLogService.GetAllAsync();
            if (!auditLogs.Any())
            {
                return Ok(new List<AuditLogResponse>());
            }
            return Ok(mapper.Map<IEnumerable<AuditLogResponse>>(auditLogs));
        }
    }
}
