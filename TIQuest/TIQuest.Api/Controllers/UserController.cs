using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net.Mime;
using TIQuest.Api.DTO.Request;
using TIQuest.Api.DTO.Response;
using TIQuest.Api.Enums;
using TIQuest.Api.Extensions;
using TIQuest.Api.Interfaces.Managers;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Controllers
{
    [Route("users")]
    [ApiController]
    [Authorize]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;

        public UserController(IUserService userService)
        {
            _userService = userService;
        }

        /// <summary>
        /// Retrieves all user information
        /// </summary>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <returns>List of users</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /users/
        ///
        /// </remarks>
        /// <response code="200">List of users.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        [HttpGet]
        [Authorize(Roles = Constants.RoleConstants.AdminRole)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<IEnumerable<UserResponse>>> GetAll([FromServices] IMapper mapper)
        {
            var users = await _userService.GetAllAsync(true);

            return Ok(users.Select(mapper.Map<UserResponse>));
        }

        /// <summary>
        /// Retrieves user information based on the provided Id.
        /// </summary>
        /// <param name="id">The Id of the user to retrieve.</param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <returns>User information.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /users/1
        ///
        /// </remarks>
        /// <response code="200">User object with username, first name, last name, and email.</response>
        /// <response code="404">Response when the user not found.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        [HttpGet("{id}")]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<UserResponse>> Users(int id, [FromServices] IMapper mapper)
        {
            Enums.UserRole role = User.GetUserRoleFromClaims();

            if (role == Enums.UserRole.Client || role == Enums.UserRole.Auditor)
            {
                // disallow client and auditor from fetching other users
                if (User.GetUserIdFromClaims() != id)
                {
                    return Forbid();
                }
            }

            User? user = await _userService.FindByIdAsync(id, true);

            if (user is null)
            {
                return NotFound();
            }

            var response = mapper.Map<UserResponse>(user);

            return Ok(response);
        }

        /// <summary>
        /// Create a new user
        /// </summary>
        /// <param name="roleService">Instance of <see cref="IRoleService"/></param>
        /// <param name="claimsManager">Instance of <see cref="IClaimsManager"/></param>
        /// <param name="tokenMailService">Instance of <see cref="ITokenMailService"/></param>
        /// <param name="mapper"></param>
        /// <param name="user">Client provided user data object.</param>
        /// <returns>User information.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /users
        ///     {
        ///         "firstName": "string",
        ///         "lastName": "string",
        ///         "email": "string",
        ///         "role": 123,
        ///         "accounts": [123,456],
        ///         "isActive": true,
        ///         "statements": [
        ///             { "account": 123, "email": true, "mail": false },
        ///             { "account": 456, "email": true, "mail": false },
        ///         ]
        ///     }
        /// </remarks>
        /// <response code="201">Response when user is successfully created.</response>
        /// <response code="400">Response when input data is invalid.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        /// <response code="409">Response when the user already exists.</response>
        /// <response code="415">Response when the provided data is not of accepted type ie. json.</response>
        [HttpPost]
        [Authorize(Roles = Constants.RoleConstants.AdminRole)]
        [Consumes(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult<UserResponse>> Post([FromServices] IRoleService roleService,
            [FromServices] IClaimsManager claimsManager,
            [FromServices] ITokenMailService tokenMailService,
            [FromServices] IAuditLogService auditLogService,
            [FromServices] IMapper mapper,
            [FromBody] NewUserRequest user)
        {
            int? userId = User.GetUserIdFromClaims();

            if (userId is null)
            {
                return Unauthorized();
            }

            Poco.Entities.UserRole? role = await roleService.FindByIdAsync(user.Role);
            if (role is null)
            {
                ModelState.AddModelError(nameof(user.Role), $"{nameof(user.Role)} is not valid.");
            }
            else
            {
                Enums.UserRole userRole = role.Description.ToUserRoleEnum();

                if (userRole == Enums.UserRole.Client && (user.Accounts is null || user.Accounts.Count == 0))
                {
                    ModelState.AddModelError(nameof(user.Accounts), $"{nameof(user.Accounts)} must be provided for client roles.");
                }
            }
            if (!ModelState.IsValid)
            {
                return ValidationProblem(ModelState);
            }

            //find by email
            var existingUser = await _userService.FindByEmailAsync(user.Email, true);
            if (existingUser is not null)
            {
                return Conflict();
            }

            User createdUser = await _userService.CreateUserAsync(user, userId.Value, role!.Description.ToUserRoleEnum());
            var claims = claimsManager.GetDefaultClaims(createdUser.Id).AddRoleClaim(Constants.RoleConstants.PasswordResetRole).Claims;

            await tokenMailService.SendTokenAsync(claims, TemplateType.CreateUser, createdUser);

            await auditLogService.InsertAuditLogAsync("User Created", $"{createdUser.LastName}, {createdUser.FirstName}", userId.Value);

            return CreatedAtAction(nameof(Users), new { id = createdUser.Id }, mapper.Map<UserResponse>(createdUser));
        }


        /// <summary>
        /// Edit an existing user
        /// </summary>
        /// <param name="id"></param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <param name="userRequest">Client provided user data object.</param>
        /// <returns>User information.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /users/123
        ///     {
        ///         "id": 123,
        ///         "firstName": "string",
        ///         "lastName": "string",
        ///         "email": "string",
        ///         "officePhone": "string",
        ///         "homePhone": "string",
        ///         "mobilePhone": "string",
        ///         "accounts": [123,456],
        ///         "isActive": true,
        ///         "statements": [
        ///             { "account": 123, "email": true, "mail": false },
        ///             { "account": 456, "email": true, "mail": false },
        ///         ]
        ///     }
        /// </remarks>
        /// <response code="200">Response when user is successfully updated.</response>
        /// <response code="400">Response when input data is invalid.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        /// <response code="404">Response when requested resource is not found.</response>
        /// <response code="415">Response when the provided data is not of accepted type ie. json.</response>
        [HttpPut("{id}")]
        [Authorize(Roles = Constants.RoleConstants.AdminRole)]
        [Consumes(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult<UserResponse>> Put(int id,
            [FromServices] IMapper mapper,
            [FromBody] EditUserRequest userRequest)
        {
            int? userId = User.GetUserIdFromClaims();

            if (userId is null)
            {
                return Unauthorized();
            }

            if (id != userRequest.Id)
            {
                ModelState.AddModelError(nameof(userRequest.Id), $"Id in the request body does not match the id in the url.");
            }

            Poco.Entities.User? existingUser = await _userService.FindByIdAsync(id, true);

            if (existingUser is null)
            {
                return NotFound();
            }

            var existingUserEmail = await _userService.FindByEmailAsync(userRequest.Email, true);

            if (existingUserEmail is not null && existingUserEmail.Id != id)
            {
                return Conflict();
            }

            Enums.UserRole userRole = existingUser.Role.ToUserRoleEnum();

            if (userRole == Enums.UserRole.Client && (userRequest.Accounts is null || userRequest.Accounts.Count == 0))
            {
                ModelState.AddModelError(nameof(userRequest.Accounts), $"{nameof(userRequest.Accounts)} must be provided for client roles.");
            }

            if (!ModelState.IsValid)
            {
                return ValidationProblem(ModelState);
            }

            User createdUser = await _userService.UpdateUserAsync(userRequest, userId.Value, userRole);
            return Ok(mapper.Map<UserResponse>(createdUser));
        }
    }
}
