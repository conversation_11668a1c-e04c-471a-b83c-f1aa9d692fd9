﻿using AutoMapper;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Drawing.Charts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net.Mime;
using TIQuest.Api.DTO.Request;
using TIQuest.Api.DTO.Response;
using TIQuest.Api.Extensions;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Controllers
{
	[Route("processes/monthend")]
	[ApiController]
	[Authorize(Roles = Constants.RoleConstants.AdminRole)]
	public class MonthEndController : ControllerBase
	{
		/// <summary>
		/// Get all historical month end processes.
		/// </summary>
		/// <param name="monthEndService">Instance of <see cref="IMonthEndProcessService"/></param>
		/// <param name="mapper">Instance of <see cref="IMapper"/></param>
		/// <remarks>
		/// Sample request:
		/// 
		///     GET /processes/monthend
		///     
		/// </remarks>
		/// <response code="200">List of historical month end processes.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		[HttpGet]
		[Produces(MediaTypeNames.Application.Json)]
		[ProducesResponseType(StatusCodes.Status200OK)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		public async Task<ActionResult<IEnumerable<MonthEndProcessResponse>>> GetAll(
			 [FromServices] IMonthEndProcessService monthEndService,
			 [FromServices] IMapper mapper)
		{
			var processes = await monthEndService.GetAllAsync();
			return Ok(mapper.Map<IEnumerable<MonthEndProcessResponse>>(processes));
		}

		/// <summary>
		/// Get latest historical month end process.
		/// </summary>
		/// <param name="monthEndService">Instance of <see cref="IMonthEndProcessService"/></param>
		/// <param name="mapper">Instance of <see cref="IMapper"/></param>
		/// <remarks>
		/// Sample request:
		/// 
		///     GET /processes/monthend/latest
		///     
		/// </remarks>
		/// <response code="200">Latest historical month end process.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		[HttpGet("latest")]
		[Produces(MediaTypeNames.Application.Json)]
		[ProducesResponseType(StatusCodes.Status200OK)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		public async Task<ActionResult<MonthEndProcessResponse>> GetLatestMonthEndProcess(
			 [FromServices] IMonthEndProcessService monthEndService,
			 [FromServices] IMapper mapper)
		{
			var latestProcess = await monthEndService.GetLatestMonthEndProcessAsync();
			return Ok(mapper.Map<MonthEndProcessResponse>(latestProcess));
		}

		/// <summary>
		/// Get summary for the month end process
		/// </summary>
		/// <param name="request">New distribution request</param>
		/// <param name="monthEndProcessService">Instance of <see cref="IMonthEndProcessService"/></param>
		/// <remarks>
		/// Sample request:
		/// 
		///     POST /processes/monthend/summary/
		///     {
		///         "month": 2,
		///         "year": 2024,
		///         "investmentRate": 5.5,
		///         "managementRate": 15,
		///     }
		/// </remarks>
		/// <response code="200">Response on successful execution.</response>
		/// <response code="400">Response when input is missing or invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		/// <response code="415">Response when request type is invalid.</response>
		[HttpPost("summary")]
		[Consumes(MediaTypeNames.Application.Json)]
		[Produces(MediaTypeNames.Application.Json)]
		[ProducesResponseType(StatusCodes.Status200OK)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		[ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
		public async Task<ActionResult<MonthEndSummaryResponse>> GetSummary(
			 [FromBody] MonthEndSummaryRequest request,
			 [FromServices] IMonthEndProcessService monthEndProcessService)
		{
			var userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Unauthorized();
			}

			var requestedDate = await ValidateMonthEndRequestAsync(request, monthEndProcessService);

			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}

			var summaryResponse = new MonthEndSummaryResponse();

			var investmentInterest = await monthEndProcessService.GetInvestmentInterestSummaryAsync(requestedDate, requestedDate.AddMonths(1), request.InvestmentRate!.Value);
			// create investment details
			summaryResponse.Investment.Balance = decimal.Round(investmentInterest.AverageBalance, 0);
			summaryResponse.Investment.Due = investmentInterest.InterestDue;
			summaryResponse.Investment.Rate = request.InvestmentRate;

			// create management fee details
			summaryResponse.ManagementFee.Rate = request.ManagementRate;
			decimal managementFeeDue = decimal.Round(investmentInterest.InterestDue * (request.ManagementRate!.Value / 100), 2);
			summaryResponse.ManagementFee.Fee = managementFeeDue;
			// calculate offset

			decimal offset = 0;
			summaryResponse.ManagementFee.Offset = offset;

			// create fixed loan details
			var fixedLoanDues = await monthEndProcessService.GetFixedLoanInterestSummaryAsync(requestedDate, requestedDate.AddMonths(1));
			//summaryResponse.FixedLoan.Balance = decimal.Round(Math.Abs(fixedLoanDues.AverageBalance), 2);
			summaryResponse.FixedLoan.Balance = (decimal)Math.Round(Math.Abs(float.Parse(fixedLoanDues.AverageBalance.ToString())), 2);
			summaryResponse.FixedLoan.Due = Math.Abs(fixedLoanDues.InterestDue);

			// create variable loan details
			decimal variableInterestDue = investmentInterest.InterestDue + managementFeeDue + fixedLoanDues.InterestDue;

			var variableLoanDues = await monthEndProcessService.GetVariableLoanInterestSummaryAsync(requestedDate, requestedDate.AddMonths(1), variableInterestDue);
			summaryResponse.VariableLoan.Balance = decimal.Round(Math.Abs(variableLoanDues.AverageBalance), 2, MidpointRounding.AwayFromZero);
			summaryResponse.VariableLoan.Due = variableLoanDues.ApplicableDue;
			summaryResponse.VariableLoan.Rate = variableLoanDues.ApplicableRate;

			decimal totalout = investmentInterest.InterestDue + managementFeeDue;
			decimal totalin = (decimal)summaryResponse.VariableLoan.Due + (decimal)summaryResponse.FixedLoan.Due;

			summaryResponse.ManagementFee.Offset = totalin - totalout;
			summaryResponse.ManagementFee.Total = managementFeeDue + summaryResponse.ManagementFee.Offset;

			return Ok(summaryResponse);
		}

		/// <summary>
		/// Execute the month end process
		/// </summary>
		/// <param name="request">New distribution request</param>
		/// <param name="monthEndProcessService">Instance of <see cref="IMonthEndProcessService"/></param>
		/// <remarks>
		/// Sample request:
		/// 
		///     POST /processes/monthend/
		///     {
		///         "month": 2,
		///         "year": 2024,
		///         "investmentRate": 5.5,
		///         "managementRate": 15,
		///     }
		/// </remarks>
		/// <response code="200">Response on successful execution.</response>
		/// <response code="400">Response when input is missing or invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		/// <response code="415">Response when request type is invalid.</response>
		[HttpPost]
		[Consumes(MediaTypeNames.Application.Json)]
		[Produces(MediaTypeNames.Application.Json)]
		[ProducesResponseType(StatusCodes.Status204NoContent)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		[ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
		public async Task<ActionResult> RunMonthEnd(
			 [FromBody] MonthEndSummaryRequest request,
			 [FromServices] IMonthEndProcessService monthEndProcessService,
			 [FromServices] IAuditLogService auditLogService)
		{
			var userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Unauthorized();
			}

			var requestedDate = await ValidateMonthEndRequestAsync(request, monthEndProcessService);

			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}

			// run investment interest
			await monthEndProcessService.RunMonthEndProcessAsync(requestedDate, requestedDate.AddMonths(1), request.InvestmentRate!.Value, request.Description, request.ManagementRate!.Value, userId.Value);

			await auditLogService.InsertAuditLogAsync("Month End Process", $"Month end process executed for {requestedDate:MMMM, yyyy}", userId.Value);

			return NoContent();
		}

		/// <summary>
		/// Delete the month end process
		/// </summary>
		/// <param name="id">Id of month end process</param>
		/// <param name="monthEndProcessService">Instance of <see cref="IMonthEndProcessService"/></param>
		/// <remarks>
		/// Sample request:
		/// 
		///     DELETE /processes/monthend/123
		///     
		/// </remarks>
		/// <response code="200">Response on successful execution.</response>
		/// <response code="400">Response when input is missing or invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		/// <response code="404">Response when requested resource is not found.</response>
		[HttpDelete("{id}")]
		[Produces(MediaTypeNames.Application.Json)]
		[ProducesResponseType(StatusCodes.Status204NoContent)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		[ProducesResponseType(StatusCodes.Status404NotFound)]
		public async Task<ActionResult> DeleteMonthEnd(
			 int id,
			 [FromServices] IMonthEndProcessService monthEndProcessService,
			 [FromServices] IAuditLogService auditLogService)
		{
			var userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Unauthorized();
			}

			var monthEndProcess = await monthEndProcessService.GetByIdAsync(id);

			if (monthEndProcess is null)
			{
				return NotFound();
			}

			// Calculate the end of the month relative to monthEndProcess
			DateTime monthEndDate = new DateTime(monthEndProcess.Year, monthEndProcess.Month, 1).AddMonths(1).AddTicks(-1);

			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}

			// run investment interest
			await monthEndProcessService.DeleteMonthEndProcessAsync(id, userId.Value);

			await auditLogService.InsertAuditLogAsync("Deleted Month End",
				 $"Deleted month end process for {new DateTime(monthEndProcess.Year, monthEndProcess.Month, 1):MMMM, yyyy}",
				 userId.Value);

			return NoContent();
		}

		private async Task<DateTime> ValidateMonthEndRequestAsync(MonthEndSummaryRequest request, IMonthEndProcessService monthEndProcessService, CancellationToken cancellationToken = default)
		{
			if (request.Year < 2000)
			{
				ModelState.AddModelError(nameof(request.Year), "Year is invalid or must be after 2000.");
				return DateTime.MinValue;
			}
			var requestedDate = new DateTime(request.Year, request.Month, 1);
			if (requestedDate > DateTime.UtcNow)
			{
				ModelState.AddModelError(nameof(request.Month), "Can not process for a future date.");
			}

			var lastProcessedMonth = await monthEndProcessService.GetLastProcessedMonthAsync(cancellationToken);
			if (requestedDate <= lastProcessedMonth.Date)
			{
				ModelState.AddModelError(nameof(request.Month), "Can not process for a date that has been processed.");
			}

			if (request.InvestmentRate < 0 || request.InvestmentRate > 100)
			{
				ModelState.AddModelError(nameof(request.InvestmentRate), "Investment rate must be between 0 and 100.");
			}

			if (request.ManagementRate < 0 || request.ManagementRate > 100)
			{
				ModelState.AddModelError(nameof(request.ManagementRate), "Management rate must be between 0 and 100.");
			}
			return requestedDate;
		}
	}
}
