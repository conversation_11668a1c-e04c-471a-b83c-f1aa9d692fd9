﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TIQuest.Api.DTO.Request;
using TIQuest.Api.Extensions;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Controllers
{
    [Route("settings")]
    [ApiController]
    [Authorize(Roles = Constants.RoleConstants.AdminRole)]
    public class SettingController : ControllerBase
    {
        /// <summary>
        /// Get the value for setting
        /// </summary>
        /// <param name="key">Key to search for</param>
        /// <param name="settingService">Instance of <see cref="ISettingService"/></param>
        /// <returns>Value of setting.</returns>
        /// <remarks>
        /// Sample request:
        ///     GET /settings?key=TestKey
        /// </remarks>
        /// <response code="200">User's transactions details.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        /// <response code="404">Response when key is not found</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IDictionary<string, string?>>> Get(
            [FromServices] ISettingService settingService,
            [FromQuery] string? key = null)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                var settings = await settingService.GetSettingsAsync();
                return Ok(settings.ToDictionary(x => x.Key, x => x.Value));
            }
            else
            {
                var setting = await settingService.GetSettingAsync(key);
                if (setting is null)
                {
                    return NotFound();
                }
                return Ok(new Dictionary<string, string?> { { setting.Key, setting.Value } });
            }
        }

        /// <summary>
        /// Get the welcome banner setting value (public endpoint)
        /// </summary>
        /// <param name="settingService">Instance of <see cref="ISettingService"/></param>
        /// <returns>Welcome banner setting value.</returns>
        /// <remarks>
        /// Sample request:
        ///     GET /settings/welcome-banner
        /// </remarks>
        /// <response code="200">Welcome banner setting value.</response>
        /// <response code="404">Response when welcome banner setting is not found</response>
        [HttpGet("welcome-banner")]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IDictionary<string, string?>>> GetWelcomeBanner(
            [FromServices] ISettingService settingService)
        {
            var setting = await settingService.GetSettingAsync("WelcomeBanner");
            if (setting is null)
            {
                return NotFound();
            }
            return Ok(new Dictionary<string, string?> { { setting.Key, setting.Value } });
        }

        /// <summary>
        /// Update the setting value
        /// </summary>
        /// <param name="request">Input request</param>
        /// <param name="settingService">Instance of <see cref="ISettingService"/></param>
        /// <remarks>
        /// Sample request:
        ///     PUT /settings
        ///     {
        ///         "key": "TestKey",
        ///         "value": "TestValue"
        ///     }
        /// </remarks>
        /// <response code="200">User's transactions details.</response>
        /// <response code="400">Invalid input request.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<string>> Put(
            [FromBody] EditSettingRequest request,
            [FromServices] ISettingService settingService,
            [FromServices] IAuditLogService auditLogService)
        {
            var existingSetting = await settingService.GetSettingAsync(request.Key);
            if (existingSetting is null)
            {
                return NotFound();
            }
            await settingService.UpdateSettingAsync(request);
            await auditLogService.InsertAuditLogAsync("Setting Updated", $"Updated {request.Key} to {request.Value} from {existingSetting.Value}", User.GetUserIdFromClaims().Value);
            return NoContent();
        }
    }
}
