﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net.Mime;
using TIQuest.Api.DTO.Request.Partnerships;
using TIQuest.Api.DTO.Response;
using TIQuest.Api.Extensions;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Controllers
{
    [Route("partnerships")]
    [ApiController]
    [Authorize(Roles = Constants.RoleConstants.AdminRole)]
    public class PartnershipController : ControllerBase
    {
        /// <summary>
        /// Get all partnerships
        /// </summary>
        /// <param name="partnershipService">Instance of <see cref="IPartnershipService"/></param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     GET /partnerships
        ///     
        /// </remarks>
        /// <response code="200">User's transactions details.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<PartnershipResponse>>> GetAll(
            [FromServices] IPartnershipService partnershipService,
            [FromServices] IMapper mapper)
        {
            var partnerships = await partnershipService.GetAllAsync();

            return Ok(mapper.Map<IEnumerable<PartnershipResponse>>(partnerships));
        }

        /// <summary>
        /// Get partnership by id
        /// </summary>
        /// <param name="id">Id of partnership</param>
        /// <param name="partnershipService">Instance of <see cref="IPartnershipService"/></param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     GET /partnerships/123
        ///     
        /// </remarks>
        /// <response code="200">User's transactions details.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        /// <response code="404">Response when partnership is not found.</response>
        [HttpGet("{id}")]
        public async Task<ActionResult<PartnershipDetailResponse>> GetById(
            int id,
            [FromServices] IPartnershipService partnershipService,
            [FromServices] IMapper mapper)
        {
            var partnership = await partnershipService.GetByIdAsync(id);
            if (partnership is null)
            {
                return NotFound();
            }

            return Ok(mapper.Map<PartnershipDetailResponse>(partnership));
        }


        /// <summary>
        /// Create a partnership
        /// </summary>
        /// <param name="request">Create partnership request</param>
        /// <param name="partnershipService">Instance of <see cref="IPartnershipService"/></param>
        /// <param name="mapper"></param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     POST /partnerships
        ///     {
        ///         "name": "Test Name",
        ///         "description": "Test Value",
        ///         "apartment": "string",
        ///         "address": "string",
        ///         "city": "string",
        ///         "state": "string",
        ///         "zip": "string",
        ///         "country": "string"
        ///     }
        /// </remarks>
        /// <response code="200">User's transactions details.</response>
        /// <response code="400">Invalid input request.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        [HttpPost]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult> Post(
            [FromBody] PartnershipRequest request,
            [FromServices] IPartnershipService partnershipService,
            [FromServices] IAuditLogService auditLogService,
            [FromServices] IMapper mapper)
        {
            int? userId = User.GetUserIdFromClaims();

            if (userId is null)
            {
                return Unauthorized();
            }
            var partnership = await partnershipService.CreateAsync(request, userId.Value);

            await auditLogService.InsertAuditLogAsync("Partnership Created", $"Created partnership {request.Name}", userId.Value);

            return CreatedAtAction(nameof(GetById), new { id = partnership.Id }, mapper.Map<PartnershipDetailResponse>(partnership));
        }

        /// <summary>
        /// Edit a partnership
        /// </summary>
        /// <param name="request">Create partnership request</param>
        /// <param name="partnershipService">Instance of <see cref="IPartnershipService"/></param>
        /// <param name="mapper"></param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     PUT /partnerships
        ///     {
        ///         "id": 1,
        ///         "name": "Test Name",
        ///         "description": "Test Value",
        ///         "apartment": "string",
        ///         "address": "string",
        ///         "city": "string",
        ///         "state": "string",
        ///         "zip": "string",
        ///         "country": "string"
        ///     }
        /// </remarks>
        /// <response code="200">User's transactions details.</response>
        /// <response code="400">Invalid input request.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        [HttpPut]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult<PartnershipDetailResponse>> Put(
            [FromBody] EditPartnershipRequest request,
            [FromServices] IPartnershipService partnershipService,
            [FromServices] IMapper mapper)
        {
            int? userId = User.GetUserIdFromClaims();

            if (userId is null)
            {
                return Unauthorized();
            }

            var existingPartnership = await partnershipService.GetByIdAsync(request.Id);
            if (existingPartnership is null)
            {
                return NotFound();
            }

            var partnership = await partnershipService.UpdateAsync(request, userId.Value);

            return Ok(mapper.Map<PartnershipDetailResponse>(partnership));
        }

        /// <summary>
        /// Get all investors of a partnership
        /// </summary>
        /// <param name="id"></param>
        /// <param name="partnershipService">Instance of <see cref="IPartnershipService"/></param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     Get /partnerships/123/investors
        ///     
        /// </remarks>
        /// <response code="200">Response on successful execution.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        /// <response code="404">Response when partnership is not found.</response>
        [HttpGet("{id}/investors")]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult<IEnumerable<PartnershipOwnerResponse>>> GetPartnershipInvestors(
            int id,
            [FromServices] IPartnershipService partnershipService,
            [FromServices] IMapper mapper)
        {
            var partnershipOwners = await partnershipService.GetPartnershipInvestorsAsync(id);

            return Ok(mapper.Map<IEnumerable<PartnershipOwnerResponse>>(partnershipOwners));
        }

        /// <summary>
        /// Add investor to a partnership
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request">Create partnership request</param>
        /// <param name="partnershipService">Instance of <see cref="IPartnershipService"/></param>
        /// <param name="accountService">Instance of <see cref="IInvestmentAccountService"/></param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     POST /partnerships/123/investors
        ///     {
        ///         "Account": 456,
        ///         "percentage": 1.2345,
        ///     }
        /// </remarks>
        /// <response code="204">Response on successful execution.</response>
        /// <response code="400">Invalid input request.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        /// <response code="404">Response when partnership is not found.</response>
        /// <response code="415">Response when request has invalid content type.</response>
        [HttpPost("{id}/investors")]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult> AddPartnershipInvestor(
            int id,
            [FromBody] AddPartnershipInvestorRequest request,
            [FromServices] IPartnershipService partnershipService,
            [FromServices] IInvestmentAccountService accountService)
        {
            int? userId = User.GetUserIdFromClaims();

            if (userId is null)
            {
                return Unauthorized();
            }
            var existingPartnership = await partnershipService.GetByIdAsync(id);
            if (existingPartnership is null)
            {
                return NotFound();
            }

            var existingAssociation = await partnershipService.GetPartnershipInvestorsAsync(id);
            if (existingAssociation.Any(x => x.Account.Id == request.Account))
            {
                ModelState.AddModelError(nameof(request.Account), $"Account {request.Account} is already associated to partnership {id}");
            }

            var account = await accountService.FindByIdAsync(request.Account);
            if (account is null || !account.IsActive())
            {
                ModelState.AddModelError(nameof(request.Account), $"Account {request.Account} is not valid");
            }
            if (request.Percentage == 0 || request.Percentage >= 100)
            {
                ModelState.AddModelError(nameof(request.Percentage), "Percentage must be greater than 0 and less than 100");
            }
            if (existingAssociation.Sum(x => x.Percentage) + request.Percentage > 100)
            {
                ModelState.AddModelError(nameof(request.Percentage), "Total percentage of ownership cannot be greater than 100");
            }
            if (!ModelState.IsValid)
            {
                return ValidationProblem(ModelState);
            }

            await partnershipService.AddInvestorAsync(request, id, userId.Value);

            return NoContent();
        }

        /// <summary>
        /// Remove an investor from a partnership
        /// </summary>
        /// <param name="id">Partnership id</param>
        /// <param name="accountId">Investor Id</param>
        /// <param name="partnershipService">Instance of <see cref="IPartnershipService"/></param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     DELETE /partnerships/123/investors/456
        ///     
        /// </remarks>
        /// <response code="204">Response on successful execution.</response>
        /// <response code="400">Invalid input request.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        /// <response code="404">Response when partnership is not found.</response>
        [HttpDelete("{id}/investors/{accountId}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult> RemovePartnershipInvestor(
            int id,
            int accountId,
            [FromServices] IPartnershipService partnershipService,
            [FromServices] IAuditLogService auditLogService)
        {
            var partnershipInvestors = await partnershipService.GetPartnershipInvestorsAsync(id);

            if (!partnershipInvestors.Any(x => x.Account.Id == accountId))
            {
                return NotFound();
            }

            await partnershipService.RemoveInvestorAsync(id, accountId);

            var account = partnershipInvestors.Where(x => x.Account.Id == accountId).Select(x => x.Account).First();

            var partnership = await partnershipService.GetByIdAsync(id);

            await auditLogService.InsertAuditLogAsync("Partner Removed", $"Removed {account.Name} as partner from {partnership.Name}", User.GetUserIdFromClaims().Value);

            return NoContent();
        }

        /// <summary>
        /// Edits an investor entry of a partnership
        /// </summary>
        /// <param name="id">partnership id</param>
        /// <param name="request">Edit investor partnership request</param>
        /// <param name="partnershipService">Instance of <see cref="IPartnershipService"/></param>
        /// <param name="accountService">Instance of <see cref="IInvestmentAccountService"/></param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     PUT /partnerships/123/investors
        ///     {
        ///         "id": 1,
        ///         "Account": 456,
        ///         "percentage": 1.2345,
        ///     }
        /// </remarks>
        /// <response code="204">Response on successful execution.</response>
        /// <response code="400">Invalid input request.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        /// <response code="404">Response when partnership is not found.</response>
        /// <response code="415">Response when request has invalid content type.</response>
        [HttpPut("{id}/investors")]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult> EditPartnershipInvestor(
            int id,
            [FromBody] EditPartnershipInvestorRequest request,
            [FromServices] IPartnershipService partnershipService,
            [FromServices] IInvestmentAccountService accountService)
        {
            int? userId = User.GetUserIdFromClaims();

            if (userId is null)
            {
                return Unauthorized();
            }
            var existingPartnership = await partnershipService.GetByIdAsync(id);
            if (existingPartnership is null)
            {
                return NotFound();
            }

            var account = await accountService.FindByIdAsync(request.Account);
            if (account is null || !account.IsActive())
            {
                ModelState.AddModelError(nameof(request.Account), $"Account {request.Account} is not valid");
            }
            else
            {
                var investmentAccounts = await accountService.GetAccountsByInvestorAsync(account.Investor.Id, userId.Value);
                if (!investmentAccounts.Any(x => x.Id == request.Account))
                {
                    ModelState.AddModelError(nameof(request.Account), $"Can not associate with an account belonging to a different investor.");
                }
            }

            var existingInvestor = await partnershipService.GetPartnershipInvestorAsync(request.Id);
            if (existingInvestor is null || existingInvestor.PartnershipId != id)
            {
                ModelState.AddModelError(nameof(request.Account), $"Account {request.Account} is not associated to partnership {id}");
            }
            else
            {
                var existingAssociations = await partnershipService.GetPartnershipInvestorsAsync(id);
                if ((existingAssociations.Sum(x => x.Percentage) - existingInvestor.Percentage) + request.Percentage > 100)
                {
                    ModelState.AddModelError(nameof(request.Percentage), "Total percentage of ownership cannot be greater than 100");
                }
            }

            if (!ModelState.IsValid)
            {
                return ValidationProblem(ModelState);
            }

            await partnershipService.EditInvestorAsync(request, userId.Value);

            return NoContent();
        }

        /// <summary>
        /// Get all distributions of a partnership
        /// </summary>
        /// <param name="id">Partnership Id</param>
        /// <param name="distributionService">Instance of <see cref="IDistributionService"/></param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     Get /partnerships/123/distributions
        ///     
        /// </remarks>
        /// <response code="200">Response on successful execution.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        [HttpGet("{id}/distributions")]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<IEnumerable<DistributionResponse>>> GetPartnershipDistributions(
            int id,
            [FromServices] IDistributionService distributionService,
            [FromServices] IMapper mapper)
        {
            var partnershipOwners = await distributionService.GetPartnershipDistributionsAsync(id);

            return Ok(mapper.Map<IEnumerable<DistributionResponse>>(partnershipOwners));
        }
    }
}
