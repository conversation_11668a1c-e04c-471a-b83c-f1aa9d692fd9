﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TIQuest.Api.DTO.Response;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Controllers
{
    [Route("registers")]
    [ApiController]
    [Authorize(Roles = Constants.RoleConstants.AdminRole)]
    public class RegisterController : ControllerBase
    {
        /// <summary>
        /// Get all unprinted check details
        /// </summary>
        /// <param name="checkRegisterService">Instance of <see cref="ICheckRegisterService"/></param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <returns>List of check detail registers.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET registers/check
        ///
        /// </remarks>
        /// <response code="200">User object with username, first name, last name, and email.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user lacks authorization for the endpoint.</response>
        [Route("check")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<IEnumerable<CheckDetailResponse>>> GetCheckRegister(
            [FromServices] ICheckRegisterService checkRegisterService,
            [FromServices] IMapper mapper)
        {
            var checkRegisters = await checkRegisterService.GetAllAsync();
            if (!checkRegisters.Any())
            {
                return Ok(new List<CheckDetailResponse>());
            }
            return Ok(mapper.Map<IEnumerable<CheckDetailResponse>>(checkRegisters));
        }

        /// <summary>
        /// Updates the check number for all the unprinted checks.
        /// </summary>
        /// <param name="nextCheckNumber">Next check number</param>
        /// <param name="checkRegisterService">Instance of <see cref="ICheckRegisterService"/></param>
        /// <returns>List of check detail registers.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST registers/check
        ///     
        ///     12345
        ///
        /// </remarks>
        /// <response code="204">Response when operation completed successfully.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user lacks authorization for the endpoint.</response>
        [Route("check")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<IEnumerable<CheckDetailResponse>>> UpdateCheckNumber(
            [FromBody] int nextCheckNumber,
            [FromServices] ICheckRegisterService checkRegisterService)
        {
            if (nextCheckNumber <= 0)
            {
                ModelState.AddModelError(nameof(nextCheckNumber), "Next check number must be greater than 0.");
            }
            if (!ModelState.IsValid)
            {
                return ValidationProblem(ModelState);
            }

            await checkRegisterService.UpdateCheckNumberAsync(nextCheckNumber);
            return NoContent();
        }

        /// <summary>
        /// Get all unprinted check details
        /// </summary>
        /// <param name="achRegisterService">Instance of <see cref="IAchRegisterService"/></param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <returns>List of check detail registers.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET registers/ach
        ///
        /// </remarks>
        /// <response code="200">User object with username, first name, last name, and email.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user lacks authorization for the endpoint.</response>
        [Route("ach")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<IEnumerable<AchDetailResponse>>> GetAchRegister(
            [FromServices] IAchRegisterService achRegisterService,
            [FromServices] IMapper mapper)
        {
            var achRegisters = await achRegisterService.GetAllAsync();
            if (!achRegisters.Any())
            {
                return Ok(new List<AchDetailResponse>());
            }
            return Ok(mapper.Map<IEnumerable<AchDetailResponse>>(achRegisters));
        }
    }
}
