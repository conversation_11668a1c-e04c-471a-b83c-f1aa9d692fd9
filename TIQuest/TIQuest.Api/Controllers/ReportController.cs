﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Net.Mime;
using TIQuest.Api.DTO.Request.Reports;
using TIQuest.Api.DTO.Response;
using TIQuest.Api.Enums;
using TIQuest.Api.Extensions;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Reports.Filters;

namespace TIQuest.Api.Controllers
{
	[Route("reports")]
	[ApiController]
	public class ReportController : ControllerBase
	{
		private readonly IReportService _reportService;

		public ReportController(IReportService reportService)
		{
			_reportService = reportService;
		}

		/// <summary>
		/// Retrieves Statement 1099 file for the provided year and in provided format.
		/// </summary>
		/// <param name="request">Input request</param>
		/// <returns>Output file stream.</returns>
		/// <remarks>
		/// Sample request:
		///
		///     POST reports/1099/
		///     {
		///         year: 2023,
		///         client: 123,
		///         account: 456,
		///         format: "pdf"
		///     }
		///
		/// </remarks>
		/// <response code="200">File stream.</response>
		/// <response code="400">Response when input is invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		[Route("1099")]
		[HttpPost]
		[Authorize(Roles = $"{Constants.RoleConstants.AdminRole}, {Constants.RoleConstants.AuditorRole}")]
		[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Stream))]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		public async Task<ActionResult<Stream>> Statement1099(Statement1099Request request)
		{
			if (request.Year <= 0 || request.Year > DateTime.UtcNow.Year || DateTime.UtcNow.Year - request.Year > 10)
			{
				ModelState.AddModelError(nameof(request.Year), $"{nameof(request.Year)} {request.Year} is not a valid value.");
			}
			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}

			Stream reportStatement = await _reportService.GetStatement1099Async(new Statement1099Filter(request.Year, request.Client, request.Accounts));

			return File(reportStatement, MediaTypeNames.Text.Csv, $"{nameof(Statement1099)}.csv");
		}

		/// <summary>
		/// Retrieves ACH report.
		/// </summary>
		/// <param name="ids">Ids to retrieve</param>
		/// <param name="achRegisterService">Instance of <see cref="IAchRegisterService"/></param>
		/// <returns>Output file stream.</returns>
		/// <remarks>
		/// Sample request:
		///
		///     POST reports/ach/
		///         
		///     [123, 456, 789]
		///
		/// </remarks>
		/// <response code="200">File stream.</response>
		/// <response code="400">Response when input is invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		[Route("ach")]
		[HttpPost]
		[Authorize(Roles = Constants.RoleConstants.AdminRole)]
		[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Stream))]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		public async Task<ActionResult<Stream>> Ach([FromBody] List<int> ids,
			 [FromServices] IAchRegisterService achRegisterService)
		{
			if (ids.Count == 0)
			{
				ModelState.AddModelError(nameof(ids), "At least one id is required.");
			}

			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}

			var fileStream = await achRegisterService.GetAchReportAsync(ids);

			return File(fileStream, MediaTypeNames.Text.Csv, "AchRecords.csv");
		}

		/// <summary>
		/// Prints checks.
		/// </summary>
		/// <param name="ids">Ids to retrieve</param>
		/// <param name="checkRegisterService">Instance of <see cref="ICheckRegisterService"/></param>
		/// <returns>Output file stream.</returns>
		/// <remarks>
		/// Sample request:
		///
		///     POST reports/check/
		///         
		///     [123, 456, 789]
		///
		/// </remarks>
		/// <response code="200">File stream.</response>
		/// <response code="400">Response when input is invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user doesnt have authorization to access.</response>
		[Route("check")]
		[HttpPost]
		[Authorize(Roles = Constants.RoleConstants.AdminRole)]
		[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Stream))]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		public async Task<ActionResult<Stream>> Check([FromBody] List<int> ids,
			 [FromServices] ICheckRegisterService checkRegisterService)
		{
			if (ids.Count == 0)
			{
				ModelState.AddModelError(nameof(ids), "At least one id is required.");
			}

			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}

			var fileStream = await checkRegisterService.GetChecksAsync(ids);

			return File(fileStream, MediaTypeNames.Application.Pdf, "Checks.pdf");
		}


		/// <summary>
		/// Retrieves client accounts
		/// </summary>
		/// <param name="request">Input request</param>
		/// <returns>Output file stream.</returns>
		/// <remarks>
		/// Sample request:
		///
		///     POST reports/accounts/
		///     {
		///         Client : {
		///             Filter: [123, 456],
		///             IsActive: true
		///         },
		///         Account : {
		///             Filter: [123, 456],
		///             IsActive: true
		///         },
		///         Month: 5,
		///         Year: 2023,
		///         Email: false
		///     }
		///
		/// </remarks>
		/// <response code="200">User object with username, first name, last name, and email.</response>
		/// <response code="400">Response when input is invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user has no access.</response>
		/// <response code="404">Response when report detail is not found for user.</response>
		[Route("accounts")]
		[HttpPost]
		[Authorize]
		[ProducesResponseType(StatusCodes.Status200OK)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		[ProducesResponseType(StatusCodes.Status404NotFound)]
		public async Task<ActionResult<Stream?>> ClientAccount(ClientAccountRequest request)
		{
			var userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Forbid();
			}

			if (request.Year <= 0 || request.Year > DateTime.UtcNow.Year || DateTime.UtcNow.Year - request.Year > 10)
			{
				ModelState.AddModelError(nameof(request.Year), $"{request.Year} is not a valid value.");
			}

			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}
			var userRole = User.GetUserRoleFromClaims();
			var reportStatement = await _reportService.GetClientAccountStatementAsync(new ClientAccountFilter(request.Client.Filter, request.Client.IsActive,
					  request.Account.Filter, request.Account.IsActive, request.Month, request.Year, request.Email, request.AccountType, userId.Value), userRole);

			if (reportStatement is null)
			{
				return NotFound();
			}

			return File(reportStatement, MediaTypeNames.Application.Pdf, $"ClientAccountStatement.pdf");
		}

		/// <summary>
		/// Get Loan statement details
		/// </summary>
		/// <remarks>
		/// Sample request:
		///
		///     GET reports/loan/?month=5&amp;year=2023
		///
		/// </remarks>
		/// <response code="200">List of accounts with loan summary details.</response>
		/// <response code="400">Response when input is invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user has no access.</response>
		[Route("loan")]
		[HttpGet]
		[Authorize(Roles = $"{Constants.RoleConstants.AdminRole}, {Constants.RoleConstants.AuditorRole}")]
		[ProducesResponseType(StatusCodes.Status200OK)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		public async Task<ActionResult<List<StatementDetailResponse>>> LoanStatementDetails([Required][Range(1, 12)] int month, [Required] int year, [FromServices] IMapper mapper)
		{
			var userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Forbid();
			}

			if (year <= 0 || year > DateTime.UtcNow.Year || DateTime.UtcNow.Year - year > 10)
			{
				ModelState.AddModelError(nameof(year), $"{year} is not a valid value.");
			}

			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}

			var loanStatementDetails = await _reportService.GetStatementDetailsAsync(month, year, Constants.DataConstants.LoanAccountType);

			return Ok(mapper.Map<List<StatementDetailResponse>>(loanStatementDetails));
		}

		/// <summary>
		/// Get loan statement report pdf
		/// </summary>
		/// <param name="request">Input request</param>
		/// <returns>Output file stream.</returns>
		/// <remarks>
		/// Sample request:
		///
		///     POST reports/loan/
		///     {
		///         "month": 5,
		///         "year": 2023
		///     }
		///
		/// </remarks>
		/// <response code="200">File stream with requested data.</response>
		/// <response code="400">Response when input is invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user has no access.</response>
		[Route("loan")]
		[HttpPost]
		[Authorize(Roles = $"{Constants.RoleConstants.AdminRole}, {Constants.RoleConstants.AuditorRole}")]
		[ProducesResponseType(StatusCodes.Status200OK)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		[ProducesResponseType(StatusCodes.Status404NotFound)]
		public async Task<ActionResult<Stream?>> LoanStatementReport([FromBody] AccountStatementRequest request)
		{
			var userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Forbid();
			}

			if (request.Year <= 0 || request.Year > DateTime.UtcNow.Year || DateTime.UtcNow.Year - request.Year > 10)
			{
				ModelState.AddModelError(nameof(request.Year), $"{request.Year} is not a valid value.");
			}

			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}

			var fileStream = await _reportService.GetStatementReportAsync(request.Month, request.Year, Constants.DataConstants.LoanAccountType);
			if (fileStream is null)
			{
				return NotFound();
			}

			return File(fileStream, MediaTypeNames.Application.Pdf, "Loan Statement.pdf");
		}

		/// <summary>
		/// Get investment statement details
		/// </summary>
		/// <remarks>
		/// Sample request:
		///
		///     GET reports/investment/?month=5&amp;year=2023
		///
		/// </remarks>
		/// <response code="200">List of accounts with investment summary details.</response>
		/// <response code="400">Response when input is invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user has no access.</response>
		[Route("investment")]
		[HttpGet]
		[Authorize(Roles = $"{Constants.RoleConstants.AdminRole}, {Constants.RoleConstants.AuditorRole}")]
		[ProducesResponseType(StatusCodes.Status200OK)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		public async Task<ActionResult<List<StatementDetailResponse>>> InvestmentStatementDetails([Required][Range(1, 12)] int month, [Required] int year, [FromServices] IMapper mapper)
		{
			var userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Forbid();
			}

			if (year <= 0 || year > DateTime.UtcNow.Year || DateTime.UtcNow.Year - year > 10)
			{
				ModelState.AddModelError(nameof(year), $"{year} is not a valid value.");
			}

			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}

			var investmentStatementDetails = await _reportService.GetStatementDetailsAsync(month, year, Constants.DataConstants.InvestmentAccountType);

			return Ok(mapper.Map<List<StatementDetailResponse>>(investmentStatementDetails));
		}

		/// <summary>
		/// Get investment statement report pdf
		/// </summary>
		/// <param name="request">Input request</param>
		/// <returns>Output file stream.</returns>
		/// <remarks>
		/// Sample request:
		///
		///     POST reports/investment/
		///     {
		///         "month": 5,
		///         "year": 2023
		///     }
		///
		/// </remarks>
		/// <response code="200">File stream with requested data.</response>
		/// <response code="400">Response when input is invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user has no access.</response>
		[Route("investment")]
		[HttpPost]
		[Authorize(Roles = $"{Constants.RoleConstants.AdminRole}, {Constants.RoleConstants.AuditorRole}")]
		[ProducesResponseType(StatusCodes.Status200OK)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		[ProducesResponseType(StatusCodes.Status404NotFound)]
		public async Task<ActionResult<Stream?>> InvestmentStatementReport([FromBody] AccountStatementRequest request)
		{
			var userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Forbid();
			}

			if (request.Year <= 0 || request.Year > DateTime.UtcNow.Year || DateTime.UtcNow.Year - request.Year > 10)
			{
				ModelState.AddModelError(nameof(request.Year), $"{request.Year} is not a valid value.");
			}

			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}

			var fileStream = await _reportService.GetStatementReportAsync(request.Month, request.Year, Constants.DataConstants.InvestmentAccountType);
			if (fileStream is null)
			{
				return NotFound();
			}

			return File(fileStream, MediaTypeNames.Application.Pdf, "Loan Statement.pdf");
		}

		/// <summary>
		/// Get client report details
		/// </summary>
		/// <remarks>
		/// Sample request:
		///
		///     GET reports/client-detail/
		///
		/// </remarks>
		/// <response code="200">List of accounts with loan summary details.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		[Route("client-detail")]
		[HttpGet]
		[Authorize(Roles = Constants.RoleConstants.AdminRole)]
		[ProducesResponseType(StatusCodes.Status200OK)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		public async Task<ActionResult<List<ClientReportResponse>>> ClientReportDetails([FromServices] IMapper mapper)
		{
			var reportDetail = await _reportService.GetClientReportDetailsAsync();


			return Ok(mapper.Map<List<ClientReportResponse>>(reportDetail));
		}

		/// <summary>
		/// Get client detail report csv
		/// </summary>
		/// <returns>Output file stream.</returns>
		/// <remarks>
		/// Sample request:
		///
		///     POST reports/client-detail/
		///
		/// </remarks>
		/// <response code="200">File stream with requested data.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		[Route("client-detail")]
		[HttpPost]
		[Authorize(Roles = Constants.RoleConstants.AdminRole)]
		[ProducesResponseType(StatusCodes.Status200OK)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		public async Task<ActionResult<Stream?>> ClientReportFile()
		{
			var fileStream = await _reportService.GetClientReportStatementAsync();

			return File(fileStream, MediaTypeNames.Text.Csv, "ClientReport.csv");
		}

		[Route("transactions")]
		[HttpPost]
		[Authorize(Roles = $"{Constants.RoleConstants.AdminRole}, {Constants.RoleConstants.AuditorRole}")]
		[ProducesResponseType(StatusCodes.Status200OK)]
		public async Task<ActionResult<Stream?>> Transactions([FromBody] TransactionReportRequest request)
		{
			var userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Forbid();
			}
			if (request.StartDate == default)
			{
				ModelState.AddModelError(nameof(request.StartDate), $"{nameof(request.StartDate)} must be provided");
			}
			if (request.EndDate == default)
			{
				ModelState.AddModelError(nameof(request.EndDate), $"{nameof(request.EndDate)} must be provided");
			}
			if (request.StartDate > request.EndDate)
			{
				ModelState.AddModelError(nameof(request.StartDate), $"{nameof(request.StartDate)} must be less than or equal to {nameof(request.EndDate)}");
			}
			if (request.EndDate < request.StartDate)
			{
				ModelState.AddModelError(nameof(request.EndDate), $"{nameof(request.EndDate)} must be greater than or equal to {nameof(request.StartDate)}");
			}
			if (request.EndDate.ToDateTime(TimeOnly.MaxValue) - request.StartDate.ToDateTime(TimeOnly.MinValue) > TimeSpan.FromDays(365 * 5))
			{
				ModelState.AddModelError(nameof(request.EndDate), $"Date range can not be more than 5 years");
			}
			if (!Enum.TryParse(request.Format, true, out ExportFormat exportFormat))
			{
				ModelState.AddModelError(nameof(request.Format), $"{request.Format} is not a valid format.");
			}
			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}

			var fileStream = await _reportService.GetTransactionStatementAsync(userId.Value, request.StartDate, request.EndDate, exportFormat);

			return exportFormat switch
			{
				ExportFormat.Pdf => File(fileStream, MediaTypeNames.Application.Pdf, $"{nameof(Transactions)}.pdf"),
				ExportFormat.Csv => File(fileStream, MediaTypeNames.Text.Csv, $"{nameof(Transactions)}.csv"),
				_ => ValidationProblem(ModelState)
			};
		}

		/// <summary>
		/// Retrieves Statement 1096 file for the provided year and in provided format.
		/// </summary>
		/// <param name="request">Input request</param>
		/// <returns>Output file stream.</returns>
		/// <remarks>
		/// Sample request:
		///
		///     POST reports/1096/
		///     {
		///         year: 2023,
		///         format: "pdf"
		///     }
		///
		/// </remarks>
		/// <response code="200">File stream.</response>
		/// <response code="400">Response when input is invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		[Route("1096")]
		[HttpPost]
		[Authorize(Roles = $"{Constants.RoleConstants.AdminRole}, {Constants.RoleConstants.AuditorRole}")]
		[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Stream))]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		public async Task<ActionResult<Stream>> Form1096(Form1096Request request)
		{
			if (request.Year <= 0 || request.Year > DateTime.UtcNow.Year || DateTime.UtcNow.Year - request.Year > 10)
			{
				ModelState.AddModelError(nameof(request.Year), $"{nameof(request.Year)} {request.Year} is not a valid value.");
			}

			if (!Enum.TryParse(request.Format, true, out ExportFormat exportFormat))
			{
				ModelState.AddModelError(nameof(request.Format), $"{request.Format} is not a valid format.");
			}

			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}

			Stream fileStream = await _reportService.GetForm1096Async(new Form1096Filter(request.Year, exportFormat));

			return exportFormat switch
			{
				ExportFormat.Pdf => File(fileStream, MediaTypeNames.Application.Pdf, $"{nameof(Form1096)}.pdf"),
				ExportFormat.Excel => File(fileStream, MediaTypeNames.Application.Octet, $"{nameof(Form1096)}.xls"),
				_ => ValidationProblem(ModelState)
			};
		}




		/// <summary>
		/// Get out of balance
		/// </summary>
		/// <returns>Out of balance</returns>
		/// <remarks>
		/// Sample request:
		///
		///     GET reports/out-of-balance
		///
		/// </remarks>
		/// <response code="200">Out of balance</response>
		/// <response code="400">Response when input is invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		[Route("out-of-balance")]
		[HttpGet]
		[Authorize(Roles = $"{Constants.RoleConstants.AdminRole}, {Constants.RoleConstants.AuditorRole}")]
		[ProducesResponseType(StatusCodes.Status200OK)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		public async Task<ActionResult<OutOfBalanceResponse>> GetOutOfBalance([FromServices] IMapper mapper)
		{
			var outOfBalance = await _reportService.GetOutOfBalance();
			return Ok(mapper.Map<OutOfBalanceResponse>(outOfBalance));
		}

		/// <summary>
		/// Get cash account statement
		/// </summary>
		/// <returns>Cash account statement</returns>
		/// <remarks>
		/// Sample request:
		///
		///     GET reports/cash-account-statement
		///
		/// </remarks>
		/// <response code="200">Cash account statement</response>
		/// <response code="400">Response when input is invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		[Route("cash-account-statement")]
		[HttpGet]
		[Authorize(Roles = $"{Constants.RoleConstants.AdminRole}, {Constants.RoleConstants.AuditorRole}")]
		[ProducesResponseType(StatusCodes.Status200OK)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		public async Task<ActionResult<CashAccountStatementResponse>> GetCashAccountStatement([FromQuery]CashAccountStatementRequest request, [FromServices] IMapper mapper)
		{
			var cashAccountStatement = await _reportService.GetCashAccountStatement(request);
			return Ok(mapper.Map<CashAccountStatementResponse>(cashAccountStatement));
		}

		/// <summary>
		/// Downloads a PDF report of transactions associated with a distribution, excluding cash/system account transactions.
		/// </summary>
		/// <param name="distributionId">The ID of the distribution</param>
		/// <returns>PDF file stream containing transaction details</returns>
		/// <remarks>
		/// Sample request:
		///
		///     GET reports/distribution-transactions/123
		///
		/// The PDF will contain:
		/// - Transaction Date
		/// - Description  
		/// - Amount
		/// 
		/// Excludes transactions from cash/system account #1.
		/// </remarks>
		/// <response code="200">PDF file stream</response>
		/// <response code="400">Response when distribution ID is invalid</response>
		/// <response code="401">Response when JWT token is missing or invalid</response>
		/// <response code="404">Response when distribution is not found</response>
		[Route("distribution-transactions/{distributionId:int}")]
		[HttpGet]
		[Authorize(Roles = $"{Constants.RoleConstants.AdminRole}, {Constants.RoleConstants.AuditorRole}")]
		[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FileStreamResult))]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status404NotFound)]
		public async Task<IActionResult> GetDistributionTransactionsPdf([FromRoute] int distributionId)
		{
			if (distributionId <= 0)
			{
				return BadRequest("Distribution ID must be greater than 0");
			}

			try
			{
				var pdfStream = await _reportService.GetDistributionTransactionsPdfAsync(distributionId);
				
				var fileName = $"Investment_Transactions_{distributionId}_{DateTime.UtcNow:yyyyMMdd}.pdf";
				
				return File(pdfStream, MediaTypeNames.Application.Pdf, fileName);
			}
			catch (ArgumentException ex)
			{
				return NotFound(ex.Message);
			}
			catch (Exception ex)
			{
				return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while generating the PDF report");
			}
		}
	}
}
