﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net.Mime;
using TIQuest.Api.DTO.Request;
using TIQuest.Api.DTO.Request.Transactions;
using TIQuest.Api.DTO.Response;
using TIQuest.Api.Enums;
using TIQuest.Api.Extensions;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Poco;
using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Controllers
{
	[Route("transactions")]
	[ApiController]
	[Authorize]
	public class TransactionController : ControllerBase
	{
		private readonly ITransactionService _transactionService;

		public TransactionController(ITransactionService transactionService)
		{
			_transactionService = transactionService;
		}

		/// <summary>
		/// Retrieves all transactions or distribution-specific transactions
		/// </summary>
		/// <param name="mapper">Instance of <see cref="IMapper"/></param>
		/// <param name="request">Instance of <see cref="TransactionQueryRequest"/></param>
		/// <returns>Transactions information.</returns>
		/// <remarks>
		/// Sample request:
		///     GET /transactions?startDate=01-01-2023&amp;endDate=12-31-2023
		///     GET /transactions?distributionId=123 (dates optional when distributionId provided)
		///     GET /transactions?distributionId=123&amp;startDate=01-01-2023&amp;endDate=12-31-2023
		/// </remarks>
		/// <response code="200">User's transactions details.</response>
		/// <response code="400">Invalid input request.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		[HttpGet]
		[Produces(typeof(IEnumerable<TransactionAccountResponse>))]
		[ProducesResponseType(StatusCodes.Status200OK)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		public async Task<ActionResult<IEnumerable<TransactionAccountResponse>>> GetAllTransactions(
			 [FromServices] IMapper mapper,
			 [FromQuery] TransactionQueryRequest request)
		{
			int? userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Unauthorized();
			}

			// Validate input based on whether distribution ID is provided
			if (!request.DistributionId.HasValue)
			{
				// Standard validation - dates are required when no distribution ID
				if (request.StartDate == default)
				{
					ModelState.AddModelError(nameof(request.StartDate), $"{nameof(request.StartDate)} must be provided");
				}
				if (request.EndDate == default)
				{
					ModelState.AddModelError(nameof(request.EndDate), $"{nameof(request.EndDate)} must be provided");
				}
				if (request.StartDate.HasValue && request.EndDate.HasValue)
				{
					if (request.StartDate > request.EndDate)
					{
						ModelState.AddModelError(nameof(request.StartDate), $"{nameof(request.StartDate)} must be less than or equal to {nameof(request.EndDate)}");
					}
					if (request.EndDate.Value.ToDateTime(TimeOnly.MaxValue) - request.StartDate.Value.ToDateTime(TimeOnly.MinValue) > TimeSpan.FromDays(365 * 5))
					{
						ModelState.AddModelError(nameof(request.EndDate), $"Date range can not be more than 5 years");
					}
				}
			}
			else
			{
				// Distribution ID provided - dates are optional but validate if provided
				if (request.StartDate.HasValue && request.EndDate.HasValue && request.StartDate > request.EndDate)
				{
					ModelState.AddModelError(nameof(request.StartDate), $"{nameof(request.StartDate)} must be less than or equal to {nameof(request.EndDate)}");
				}
			}
			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}

			bool isClientUser = User.GetUserRoleFromClaims() == Enums.UserRole.Client;

			IEnumerable<TransactionAccountRecord> transactionDetails;

			// If distribution ID is provided, get distribution-specific transactions (excluding cash account)
			if (request.DistributionId.HasValue)
			{
				transactionDetails = await _transactionService.GetDistributionTransactionsAsync(
					userId.Value, 
					isClientUser, 
					request.DistributionId.Value, 
					request.StartDate, 
					request.EndDate, 
					request.ExcludeTransactionCodes);
			}
			else
			{
				// Standard transaction retrieval - dates are guaranteed to be non-null here
				transactionDetails = await _transactionService.GetUserTransactionsAsync(
					userId.Value, 
					isClientUser, 
					request.StartDate!.Value, 
					request.EndDate!.Value, 
					request.ExcludeTransactionCodes);
			}

			return Ok(transactionDetails.Select(mapper.Map<TransactionAccountResponse>));
		}

		/// <summary>
		/// Retrieves transaction details by id
		/// </summary>
		/// <param name="id">Transaction Id</param>
		/// <param name="mapper">Instance of <see cref="IMapper"/></param>
		/// <returns>Transactions information.</returns>
		/// <remarks>
		/// Sample request:
		///     GET /transactions/123
		/// </remarks>
		/// <response code="200">User's transactions details.</response>
		/// <response code="400">Invalid input request.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		[HttpGet("{id}")]
		[Authorize(Roles = Constants.RoleConstants.AdminRole)]
		[ProducesResponseType(StatusCodes.Status200OK)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		public async Task<ActionResult<TransactionDetailResponse>> GetById(
			 int id,
			 [FromServices] IMapper mapper)
		{
			Transaction? transactionDetail = await _transactionService.GetByIdAsync(id);
			if (transactionDetail is null)
			{
				return NotFound();
			}

			return Ok(mapper.Map<TransactionDetailResponse>(transactionDetail));
		}

		/// <summary>
		/// Deposit to account
		/// </summary>
		/// <param name="accountService">Instance of <see cref="IInvestmentAccountService"/></param>
		/// <param name="transactionCodeService">Instance of <see cref="ITransactionCodeService"/></param>
		/// <param name="processingControlService">Instance of <see cref="IMonthEndProcessService"/></param>
		/// <param name="request">Instance of <see cref="TransactionRequest"/></param>
		/// <remarks>
		/// Sample request:
		///
		///     POST /transactions/deposit
		///     {
		///         "account": 1,
		///         "date": "2023-09-12T08:35:24+00:00",
		///         "transactionType": 1,
		///         "amount": 100,
		///         "description": "Test"
		///     }
		/// </remarks>
		/// <response code="201">Response when request is successfully processed.</response>
		/// <response code="400">Response when input data is invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		/// <response code="415">Response when the provided data is not of accepted type ie. json.</response>
		[HttpPost]
		[Route("deposit")]
		[Authorize(Roles = Constants.RoleConstants.AdminRole)]
		[Consumes(MediaTypeNames.Application.Json)]
		[ProducesResponseType(StatusCodes.Status201Created)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		[ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
		public async Task<ActionResult> Deposit(
				  [FromServices] IInvestmentAccountService accountService,
				  [FromServices] ITransactionCodeService transactionCodeService,
				  [FromServices] IMonthEndProcessService processingControlService,
				  [FromServices] IAuditLogService auditLogService,
				  [FromBody] TransactionRequest request)
		{
			await ValidateTransactionRequestAsync(processingControlService, request);
			var transCode = await transactionCodeService.FindByIdAsync(request.TransactionType);
			if (transCode is null || transCode.Code != Constants.DataConstants.DepositCode)
			{
				ModelState.AddModelError(nameof(request.TransactionType), $"{nameof(request.TransactionType)} is not valid.");
			}
			var account = await accountService.FindByIdAsync(request.Account);
			ValidateAccount(account, request);
			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}
			int? userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Unauthorized();
			}
			var transactionId = await _transactionService.RecordTransactionAsync(request, userId.Value, TransactionType.Deposit);

			await auditLogService.InsertAuditLogAsync("Transaction Created", $"Deposited {Math.Abs(request.Amount)} to {account.Name}", userId.Value);

			return Created($"/transactions/{transactionId}", new { transactionId });
		}


		/// <summary>
		/// Withdraw from account via wire transfer
		/// </summary>
		/// <param name="accountService">Instance of <see cref="IInvestmentAccountService"/></param>
		/// <param name="transactionCodeService">Instance of <see cref="ITransactionCodeService"/></param>
		/// <param name="processingControlService">Instance of <see cref="IMonthEndProcessService"/></param>
		/// <param name="request">Instance of <see cref="TransactionRequest"/></param>
		/// <remarks>
		/// Sample request:
		///
		///     POST /transactions/withdraw/wire
		///     {
		///         "account": 1,
		///         "date": "2023-09-12T08:35:24+00:00",
		///         "transactionType": 1,
		///         "amount": 100,
		///         "wireNumber": "test Wire Number"
		///         "description": "Test"
		///     }
		/// </remarks>
		/// <response code="201">Response when request is successfully processed.</response>
		/// <response code="400">Response when input data is invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		/// <response code="415">Response when the provided data is not of accepted type ie. json.</response>
		[HttpPost]
		[Route("withdraw/wire")]
		[Authorize(Roles = Constants.RoleConstants.AdminRole)]
		[Consumes(MediaTypeNames.Application.Json)]
		[ProducesResponseType(StatusCodes.Status201Created)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		[ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
		public async Task<ActionResult> WireWithdrawal(
			 [FromServices] IInvestmentAccountService accountService,
			 [FromServices] ITransactionCodeService transactionCodeService,
			 [FromServices] IMonthEndProcessService processingControlService,
			 [FromServices] IAuditLogService auditLogService,
			 [FromBody] WireTransactionRequest request)
		{
			var response = await ProcessWithdrawalAsync(accountService, transactionCodeService, processingControlService, auditLogService,
				 request, TransactionType.Wire);

			return response;
		}

		/// <summary>
		/// Withdraw from account via ach
		/// </summary>
		/// <param name="accountService">Instance of <see cref="IInvestmentAccountService"/></param>
		/// <param name="transactionCodeService">Instance of <see cref="ITransactionCodeService"/></param>
		/// <param name="processingControlService">Instance of <see cref="IMonthEndProcessService"/></param>
		/// <param name="request">Instance of <see cref="TransactionRequest"/></param>
		/// <remarks>
		/// Sample request:
		///
		///     POST /transactions/withdraw/ach
		///     {
		///         "account": 1,
		///         "date": "2023-09-12T08:35:24+00:00",
		///         "transactionType": 1,
		///         "amount": 100,
		///         "achDetails": "test ACH Details"
		///         "description": "Test"
		///     }
		/// </remarks>
		/// <response code="201">Response when request is successfully processed.</response>
		/// <response code="400">Response when input data is invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		/// <response code="415">Response when the provided data is not of accepted type ie. json.</response>
		[HttpPost]
		[Route("withdraw/ach")]
		[Authorize(Roles = Constants.RoleConstants.AdminRole)]
		[Consumes(MediaTypeNames.Application.Json)]
		[ProducesResponseType(StatusCodes.Status201Created)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		[ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
		public async Task<ActionResult> ACHWithdrawal(
			 [FromServices] IInvestmentAccountService accountService,
			 [FromServices] ITransactionCodeService transactionCodeService,
			 [FromServices] IMonthEndProcessService processingControlService,
			 [FromServices] IAuditLogService auditLogService,
			 [FromBody] AchWithdrawalRequest request)
		{
			return await ProcessWithdrawalAsync(accountService, transactionCodeService, processingControlService, auditLogService, request, TransactionType.ACH);
		}

		/// <summary>
		/// Withdraw from account via check
		/// </summary>
		/// <param name="accountService">Instance of <see cref="IInvestmentAccountService"/></param>
		/// <param name="transactionCodeService">Instance of <see cref="ITransactionCodeService"/></param>
		/// <param name="processingControlService">Instance of <see cref="IMonthEndProcessService"/></param>
		/// <param name="request">Instance of <see cref="TransactionRequest"/></param>
		/// <remarks>
		/// Sample request:
		///
		///     POST /transactions/withdraw/check
		///     {
		///         "account": 1,
		///         "date": "2023-09-12T08:35:24+00:00",
		///         "transactionType": 1,
		///         "amount": 100,
		///         "description": "Test",
		///         "payee": "Test",
		///         "address": "Test",
		///         "apartment": "Test",
		///         "city": "Test",
		///         "state": 123,
		///         "zip": "Test",
		///         "country": "Test",
		///         "memo": "Test"
		///     }
		/// </remarks>
		/// <response code="201">Response when request is successfully processed.</response>
		/// <response code="400">Response when input data is invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		/// <response code="415">Response when the provided data is not of accepted type ie. json.</response>
		[HttpPost]
		[Route("withdraw/check")]
		[Authorize(Roles = Constants.RoleConstants.AdminRole)]
		[Consumes(MediaTypeNames.Application.Json)]
		[ProducesResponseType(StatusCodes.Status201Created)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		[ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
		public async Task<ActionResult> CheckWithdrawal(
			 [FromServices] IInvestmentAccountService accountService,
			 [FromServices] ITransactionCodeService transactionCodeService,
			 [FromServices] IMonthEndProcessService processingControlService,
			 [FromServices] IAuditLogService auditLogService,
			 [FromBody] CheckWithdrawalRequest request)
		{
			return await ProcessWithdrawalAsync(accountService, transactionCodeService, processingControlService, auditLogService, request, TransactionType.Check);
		}

		/// <summary>
		/// Transfer between accounts
		/// </summary>
		/// <param name="accountService">Instance of <see cref="IInvestmentAccountService"/></param>
		/// <param name="transactionCodeService">Instance of <see cref="ITransactionCodeService"/></param>
		/// <param name="processingControlService">Instance of <see cref="IMonthEndProcessService"/></param>
		/// <param name="request">Instance of <see cref="TransactionRequest"/></param>
		/// <remarks>
		/// Sample request:
		///
		///     POST /transactions/transfer
		///     {
		///         "account": 1,
		///         "date": "2023-09-12T08:35:24+00:00",
		///         "transactionType": 1,
		///         "amount": 100,
		///         "description": "Test",
		///         "toAccount": 1234
		///     }
		/// </remarks>
		/// <response code="201">Response when request is successfully processed.</response>
		/// <response code="400">Response when input data is invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		/// <response code="415">Response when the provided data is not of accepted type ie. json.</response>
		[HttpPost]
		[Route("transfer")]
		[Authorize(Roles = Constants.RoleConstants.AdminRole)]
		[Consumes(MediaTypeNames.Application.Json)]
		[ProducesResponseType(StatusCodes.Status201Created)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		[ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
		public async Task<ActionResult> Transfer(
			 [FromServices] IInvestmentAccountService accountService,
			 [FromServices] ITransactionCodeService transactionCodeService,
			 [FromServices] IMonthEndProcessService processingControlService,
			 [FromServices] IAuditLogService auditLogService,
			 [FromBody] TransferTransactionRequest request)
		{
			await ValidateTransactionRequestAsync(processingControlService, request);
			if (request.Account == request.ToAccount)
			{
				ModelState.AddModelError(nameof(request.ToAccount), $"{nameof(request.ToAccount)} can not be same as {nameof(request.Account)}.");
			}
			var fromAccount = await accountService.FindByIdAsync(request.Account);
			ValidateAccount(fromAccount, request);
			var toAccount = await accountService.FindByIdAsync(request.ToAccount);
			if (toAccount is null || (toAccount.EndDate is not null && toAccount.EndDate <= new DateTime(request.Date, TimeOnly.MinValue)))
			{
				ModelState.AddModelError(nameof(request.ToAccount), $"{nameof(request.ToAccount)} is not valid.");
			}
			var transCode = await transactionCodeService.FindByIdAsync(request.TransactionType);
			if (transCode is null || transCode.Code != Constants.DataConstants.TransferCode)
			{
				ModelState.AddModelError(nameof(request.TransactionType), $"{nameof(request.TransactionType)} is not valid.");
			}

			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}

			int? userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Unauthorized();
			}

			var transactionId = await _transactionService.RecordTransactionAsync(request, userId.Value, TransactionType.Transfer);

			await auditLogService.InsertAuditLogAsync("Transaction Created", $"Transferred {request.Amount} from {fromAccount.Name} to {toAccount.Name}", userId.Value);

			return Created($"/transactions/{transactionId}", new { transactionId });
		}

		/// <summary>
		/// Void a transaction
		/// </summary>
		/// <param name="id">id of transaction to be voided</param>
		/// <param name="transactionService">Instance of <see cref="ITransactionService"/></param>
		/// <param name="processingControlService">Instance of <see cref="IMonthEndProcessService"/></param>
		/// <remarks>
		/// Sample request:
		///
		///     POST /transactions/1/void
		///     
		/// </remarks>
		/// <response code="204">Response when request is successfully processed.</response>        
		/// <response code="400">Response when transaction is not valid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		/// <response code="404">Response when transaction is not found.</response>
		[HttpPost("{id}/void")]
		[Authorize(Roles = Constants.RoleConstants.AdminRole)]
		[ProducesResponseType(StatusCodes.Status204NoContent)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		[ProducesResponseType(StatusCodes.Status404NotFound)]
		public async Task<ActionResult> Void(
			 int id,
			 [FromServices] ITransactionService transactionService,
			 [FromServices] IMonthEndProcessService processingControlService)
		{
			int? userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Unauthorized();
			}
			var transaction = await transactionService.FindByIdAsync(id);
			if (transaction is null)
			{
				return NotFound();
			}
			bool isDistributionTransaction = await transactionService.IsDistributionTransactionAsync(id);
			if (isDistributionTransaction)
			{
				ModelState.AddModelError(nameof(id), $"Transaction created via Distribution process can not be voided.");
			}
			bool isMonthEndTransaction = await transactionService.IsMonthEndTransactionAsync(id);
			if (isMonthEndTransaction)
			{
				ModelState.AddModelError(nameof(id), $"Transaction created via Month end process can not be voided.");
			}
			if (transaction.TransactionStatus.Description != Constants.DataConstants.OutstandingStatus)
			{
				ModelState.AddModelError(nameof(id), $"Transaction {id} can not be voided.");
			}

			var previousProcessedPeriod = await processingControlService.GetLastProcessedMonthAsync();
			if (new DateTime(transaction.Date, TimeOnly.MinValue) <= previousProcessedPeriod)
			{
				ModelState.AddModelError(nameof(transaction.Date), $"{nameof(transaction.Date)} can not be for a period that has already been processed.");
			}

			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}

			await transactionService.VoidTransactionAsync(id, userId.Value);

			return NoContent();
		}

		/// <summary>
		/// Reconcile a transaction
		/// </summary>
		/// <param name="id">id of transaction to be voided</param>
		/// <param name="transactionService">Instance of <see cref="ITransactionService"/></param>
		/// <remarks>
		/// Sample request:
		///
		///     POST /transactions/1/reconcile
		///     
		/// </remarks>
		/// <response code="204">Response when request is successfully processed.</response>        
		/// <response code="400">Response when transaction is not valid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		/// <response code="404">Response when transaction is not found.</response>
		[HttpPost("{id}/reconcile")]
		[Authorize(Roles = Constants.RoleConstants.AdminRole)]
		[ProducesResponseType(StatusCodes.Status204NoContent)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		[ProducesResponseType(StatusCodes.Status404NotFound)]
		public async Task<ActionResult> Reconcile(
			 int id,
			 [FromServices] ITransactionService transactionService)
		{
			int? userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Unauthorized();
			}
			var transaction = await transactionService.FindByIdAsync(id);
			if (transaction is null)
			{
				return NotFound();
			}
			if (transaction.TransactionStatus.Description != Constants.DataConstants.OutstandingStatus)
			{
				ModelState.AddModelError(nameof(id), $"Transaction {id} can not be reconciled.");
				return ValidationProblem(ModelState);
			}

			await transactionService.ReconcileTransactionAsync(id, userId.Value);

			return NoContent();
		}

		/// <summary>
		/// Unreconcile a transaction
		/// </summary>
		/// <param name="id">id of transaction to be voided</param>
		/// <param name="transactionService">Instance of <see cref="ITransactionService"/></param>
		/// <remarks>
		/// Sample request:
		///
		///     POST /transactions/1/Unreconcile
		///     
		/// </remarks>
		/// <response code="204">Response when request is successfully processed.</response>        
		/// <response code="400">Response when transaction is not valid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		/// <response code="404">Response when transaction is not found.</response>
		[HttpPost("{id}/Unreconcile")]
		[Authorize(Roles = Constants.RoleConstants.AdminRole)]
		[ProducesResponseType(StatusCodes.Status204NoContent)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		[ProducesResponseType(StatusCodes.Status404NotFound)]
		public async Task<ActionResult> Unreconcile(
			 int id,
			 [FromServices] ITransactionService transactionService)
		{
			int? userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Unauthorized();
			}
			var transaction = await transactionService.FindByIdAsync(id);
			if (transaction is null)
			{
				return NotFound();
			}
			if (transaction.TransactionStatus.Description != Constants.DataConstants.ReconciledStatus)
			{
				ModelState.AddModelError(nameof(id), $"Transaction {id} is not reconciled.");
				return ValidationProblem(ModelState);
			}

			await transactionService.UnreconcileTransactionAsync(id, userId.Value);

			return NoContent();
		}



		/// <summary>
		/// Handles the loan payment transaction
		/// </summary>
		/// <param name="accountService"></param>
		/// <param name="transactionCodeService"></param>
		/// <param name="processingControlService"></param>
		/// <param name="auditLogService"></param>
		/// <param name="request"></param>
		/// <returns></returns>
		/// <remarks>
		/// Sample request:
		///
		///     POST /transactions/loan-payment
		///     {
		///         "account": 1,
		///         "date": "2023-09-12T08:35:24+00:00",
		///         "transactionType": 3,
		///         "amount": 100,
		///         "description": "Test"
		///     }
		/// </remarks>
		/// <response code="201">Response when request is successfully processed.</response>        
		/// <response code="400">Response when transaction is not valid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		/// <response code="415">Response when the provided data is not of accepted type ie. json.</response>
		[HttpPost]
		[Route("loan-payment")]
		[Authorize(Roles = Constants.RoleConstants.AdminRole)]
		[Consumes(MediaTypeNames.Application.Json)]
		[ProducesResponseType(StatusCodes.Status201Created)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		[ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
		public async Task<ActionResult> LoanPayment(
			 [FromServices] IInvestmentAccountService accountService,
			 [FromServices] ITransactionCodeService transactionCodeService,
			 [FromServices] IMonthEndProcessService processingControlService,
			 [FromServices] IAuditLogService auditLogService,
			 [FromBody] TransactionRequest request)
		{
			await ValidateTransactionRequestAsync(processingControlService, request);
			var transCode = await transactionCodeService.FindByIdAsync(request.TransactionType);
			if (transCode is null || transCode.Code != Constants.DataConstants.LoanPaymentCode)
			{
				ModelState.AddModelError(nameof(request.TransactionType), $"{nameof(request.TransactionType)} is not valid.");
			}
			var account = await accountService.FindByIdAsync(request.Account);
			ValidateAccount(account, request);
			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}
			int? userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Unauthorized();
			}
			var transactionId = await _transactionService.RecordTransactionAsync(request, userId.Value, TransactionType.LoanPayment);

			await auditLogService.InsertAuditLogAsync("Transaction Created", $"Loan payment {Math.Abs(request.Amount)} to {account?.Name}", userId.Value);

			return Created($"/transactions/{transactionId}", new { transactionId });
		}

		/// <summary>
		/// Handles the loan advance transaction
		/// </summary>
		/// <param name="accountService"></param>
		/// <param name="transactionCodeService"></param>
		/// <param name="processingControlService"></param>
		/// <param name="auditLogService"></param>
		/// <param name="request"></param>
		/// <returns></returns>
		/// <remarks>
		/// Sample request:
		///
		///     POST /transactions/loan-advance
		///     {
		///         "account": 1,
		///         "date": "2023-09-12T08:35:24+00:00",
		///         "transactionType": 4,
		///         "amount": 100,
		///         "description": "Test"
		///     }
		/// </remarks>
		/// <response code="201">Response when request is successfully processed.</response>        
		/// <response code="400">Response when transaction is not valid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		/// <response code="415">Response when the provided data is not of accepted type ie. json.</response>
		[HttpPost]
		[Route("loan-advance")]
		[Authorize(Roles = Constants.RoleConstants.AdminRole)]
		[Consumes(MediaTypeNames.Application.Json)]
		[ProducesResponseType(StatusCodes.Status201Created)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		[ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
		public async Task<ActionResult> LoanAdvance(
			 [FromServices] IInvestmentAccountService accountService,
			 [FromServices] ITransactionCodeService transactionCodeService,
			 [FromServices] IMonthEndProcessService processingControlService,
			 [FromServices] IAuditLogService auditLogService,
			 [FromBody] TransactionRequest request)
		{
			await ValidateTransactionRequestAsync(processingControlService, request);
			var transCode = await transactionCodeService.FindByIdAsync(request.TransactionType);
			if (transCode is null || transCode.Code != Constants.DataConstants.LoanAdvanceCode)
			{
				ModelState.AddModelError(nameof(request.TransactionType), $"{nameof(request.TransactionType)} is not valid.");
			}
			var account = await accountService.FindByIdAsync(request.Account);
			ValidateAccount(account, request);
			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}
			int? userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Unauthorized();
			}
			var transactionId = await _transactionService.RecordTransactionAsync(request, userId.Value, TransactionType.LoanAdvance);

			await auditLogService.InsertAuditLogAsync("Transaction Created", $"Loan advance {Math.Abs(request.Amount)} from {account?.Name}", userId.Value);

			return Created($"/transactions/{transactionId}", new { transactionId });
		}

		/// <summary>
		/// Updates an existing transaction
		/// </summary>
		/// <param name="id">Transaction Id</param>
		/// <param name="request">Instance of <see cref="UpdateTransactionRequest"/></param>
		/// <param name="transactionService">Instance of <see cref="ITransactionService"/></param>
		/// <returns>No content if the update is successful</returns>
		/// <response code="200">Response when request is successfully processed.</response>
		/// <response code="400">Response when input data is invalid.</response>
		/// <response code="401">Response when JWT token is missing or invalid.</response>
		/// <response code="403">Response when user does not have valid claims.</response>
		/// <response code="404">Response when transaction is not found.</response>
		[HttpPut("{id}")]
		[Authorize(Roles = Constants.RoleConstants.AdminRole)]
		[Consumes(MediaTypeNames.Application.Json)]
		[ProducesResponseType(StatusCodes.Status204NoContent)]
		[ProducesResponseType(StatusCodes.Status400BadRequest)]
		[ProducesResponseType(StatusCodes.Status401Unauthorized)]
		[ProducesResponseType(StatusCodes.Status403Forbidden)]
		[ProducesResponseType(StatusCodes.Status404NotFound)]
		public async Task<ActionResult> UpdateTransaction(
		int id,
		[FromBody] UpdateTransactionRequest request,
		[FromServices] ITransactionService transactionService)
		{
			var userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Unauthorized();
			}

			int transactionId;
			try
			{
				transactionId = await transactionService.UpdateTransactionAsync(id, request, (int)userId);
			}
			catch (KeyNotFoundException ex)
			{
				return NotFound(ex.Message);
			}

			return Ok(transactionId);
		}

		private async Task<ActionResult> ProcessWithdrawalAsync(IInvestmentAccountService accountService,
				ITransactionCodeService transactionCodeService, IMonthEndProcessService processingControlService,
				IAuditLogService auditLogService, TransactionRequest request, TransactionType transactionType)
		{
			await ValidateWithdrawalAsync(transactionCodeService, processingControlService, request);

			var account = await accountService.FindByIdAsync(request.Account);
			ValidateAccount(account, request);
			if (!ModelState.IsValid)
			{
				return ValidationProblem(ModelState);
			}

			int? userId = User.GetUserIdFromClaims();
			if (userId is null)
			{
				return Unauthorized();
			}

			var transactionId = await _transactionService.RecordTransactionAsync(request, userId.Value, transactionType);

			string message = transactionType switch
			{
				TransactionType.Wire => $"Withdrawn {Math.Abs(request.Amount)} from {account.Name} via wire {((WireTransactionRequest)request).WireNumber}",
				TransactionType.ACH => $"Withdrawn {Math.Abs(request.Amount)} from {account.Name} via ACH",
				TransactionType.Check => $"Withdrawn {Math.Abs(request.Amount)} from {account.Name} via check",
				_ => throw new ArgumentOutOfRangeException(nameof(transactionType), transactionType, null)
			};

			await auditLogService.InsertAuditLogAsync("Transaction Created", message, userId.Value);

			return Created($"/transactions/{transactionId}", new { transactionId });
		}

		private async Task ValidateWithdrawalAsync(ITransactionCodeService transactionCodeService,
			 IMonthEndProcessService processingControlService, TransactionRequest request)
		{
			await ValidateTransactionRequestAsync(processingControlService, request);

			var transCode = await transactionCodeService.FindByIdAsync(request.TransactionType);
			if (transCode is null || transCode.Code != Constants.DataConstants.WithdrawalCode)
			{
				ModelState.AddModelError(nameof(request.TransactionType), $"{nameof(request.TransactionType)} is not valid.");
			}
		}

		private async Task ValidateTransactionRequestAsync(
			 IMonthEndProcessService processingControlService,
			 TransactionRequest request)
		{
			if (request.Amount <= 0)
			{
				ModelState.AddModelError(nameof(request.Amount), $"{nameof(request.Amount)} should be more than 0.");
			}
			if (request.Account == Constants.DataConstants.CashAccountId)
			{
				ModelState.AddModelError(nameof(request.Account), $"{nameof(request.Account)} is not valid.");
			}
			DateTime previousProcessedPeriod = await processingControlService.GetLastProcessedMonthAsync();
			if (new DateTime(request.Date, TimeOnly.MinValue) <= previousProcessedPeriod)
			{
				ModelState.AddModelError(nameof(request.Date), $"{nameof(request.Date)} can not be for a period that has already been processed.");
			}
		}

		private void ValidateAccount(InvestmentAccount? account, TransactionRequest request)
		{
			if (account is null || (account.EndDate is not null && account.EndDate?.Date <= new DateTime(request.Date, TimeOnly.MinValue)))
			{
				ModelState.AddModelError(nameof(request.Account), $"{nameof(request.Account)} is not valid.");
			}
		}
	}
}