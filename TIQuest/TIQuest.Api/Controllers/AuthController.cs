﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using System.ComponentModel.DataAnnotations;
using System.Net.Mime;
using System.Text;
using TIQuest.Api.DTO.Request;
using TIQuest.Api.DTO.Response;
using TIQuest.Api.Enums;
using TIQuest.Api.Extensions;
using TIQuest.Api.Interfaces.Managers;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Options;

namespace TIQuest.Api.Controllers
{
    [Route("auth")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly ITokenService _tokenService;

        public AuthController(IUserService userService, ITokenService tokenService)
        {
            _userService = userService;
            _tokenService = tokenService;
        }

        /// <summary>
        /// Creates a JWT token based on the provided username and password.
        /// </summary>
        /// <param name="claimsManager">Instance of <see cref="IClaimsManager"/></param>
        /// <param name="jwtConfig">JWT configuration</param>
        /// <param name="hasher">Instance of <see cref="IHasher"/></param>
        /// <param name="request">The model containing the username and base 64 encoded password.</param>
        /// <returns>The generated JWT token.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /auth/login
        ///     {
        ///        "email": "<EMAIL>",
        ///        "password": "Base64EncodedPassword"
        ///     }
        ///
        /// </remarks>
        /// <response code="200">Created JWT token.</response>
        /// <response code="400">Response when the username and/or password is not valid.</response>
        /// <response code="401">Response when the provided username and password do not match any user records.</response>
        [HttpPost]
        [Route("login")]
        [AllowAnonymous]
        [Produces(MediaTypeNames.Application.Json)]
        [Consumes(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<LoginResponse>> Login([FromServices] IClaimsManager claimsManager,
            [FromServices] IOptions<JwtConfigOptions> jwtConfig,
            [FromServices] IHasher hasher,
            [FromServices] IAuditLogService auditLogService,
            [FromBody] LoginRequest request)
        {
            string? password;
            try
            {
                password = Encoding.UTF8.GetString(Convert.FromBase64String(request.Password));
            }
            catch (FormatException)
            {
                ModelState.AddModelError(nameof(request.Password), "Format is invalid.");
                return ValidationProblem(ModelState);
            }
            var user = await _userService.FindByEmailAsync(request.Email, false);
            if (user is not null
                && !string.IsNullOrWhiteSpace(user.PasswordHash)
                && hasher.CompareHash(password, user.PasswordHash))
            {
                var claims = claimsManager.GetDefaultUserClaims(user).Claims;
                DateTime expiryTime = DateTime.UtcNow.AddMinutes(jwtConfig.Value.ExpirationMinutes);
                string token = _tokenService.GetToken(claims, expiryTime);

                await auditLogService.InsertAuditLogAsync("User Login", user.Email, user.Id);

                return Ok(new LoginResponse { Token = token });
            }
            return Unauthorized();
        }


        /// <summary>
        /// Submit a Change password request
        /// </summary>
        /// <param name="claimsManager">Instance of <see cref="IClaimsManager"/></param>
        /// <param name="tokenMailService">Instance of <see cref="ITokenMailService"/></param>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /auth/change-password
        ///
        /// </remarks>
        /// <response code="204">Updated Password.</response>
        /// <response code="401">Response when the username is not Unauthorized.</response>
        [HttpPost]
        [Authorize]
        [Route("change-password")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult> ChangePassword([FromServices] IClaimsManager claimsManager,
            [FromServices] ITokenMailService tokenMailService,
            [FromServices] IAuditLogService auditLogService)
        {

            int? userId = User.GetUserIdFromClaims();
            if (userId is not null)
            {
                var user = await _userService.FindByIdAsync(userId.Value, false);
                if (user is not null)
                {
                    var claims = claimsManager.GetDefaultClaims(user.Id).AddRoleClaim(Constants.RoleConstants.PasswordResetRole).Claims;

                    await tokenMailService.SendTokenAsync(claims, TemplateType.ChangePassword, user);

                    await auditLogService.InsertAuditLogAsync("Password change request", user.Email, user.Id);

                    return NoContent();
                }
            }

            return Unauthorized();
        }


        /// <summary>
        /// Send reset password link to user email to reset the password
        /// </summary>
        /// <param name="claimsManager">Instance of <see cref="IClaimsManager"/></param>
        /// <param name="tokenMailService">Instance of <see cref="ITokenMailService"/></param>
        /// <param name="email">Email address of the user</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /auth/forgot-password
        ///         
        ///         "<EMAIL>"
        ///
        /// </remarks>
        /// <response code="204">success response and send email.</response>
        /// <response code="400">Response when the username is not valid.</response>
        /// <response code="415">Response when the provided data is not a valid input type.</response>
        [HttpPost]
        [AllowAnonymous]
        [Route("forgot-password")]
        [Consumes(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult> ForgotPassword(
            [FromServices] IClaimsManager claimsManager,
            [FromServices] ITokenMailService tokenMailService,
            [FromBody][Required][EmailAddress] string email)
        {
            var user = await _userService.FindByEmailAsync(email, false);

            if (user is null)
            {
                return NoContent();
            }

            var claims = claimsManager.GetDefaultClaims(user.Id).AddRoleClaim(Constants.RoleConstants.PasswordResetRole).Claims;

            await tokenMailService.SendTokenAsync(claims, TemplateType.ResetPassword, user);

            return NoContent();
        }

        /// <summary>
        /// Reset password based on new password with JWT token.
        /// </summary>
        /// <param name="password">base 64 encoded password string</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /auth/reset-password
        ///
        ///         "Base64EncodedPassword"
        ///
        /// </remarks>
        /// <response code="204">Updated Password.</response>
        /// <response code="400">Response when the password is not valid.</response>
        /// <response code="401">Unauthorized when token is not valid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        /// <response code="415">Response when the provided data is not a valid input type.</response>
        [HttpPost]
        [Authorize(Roles = Constants.RoleConstants.PasswordResetRole)]
        [Route("reset-password")]
        [Consumes(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult> ResetPassword([FromBody][Required] string password,
            [FromServices] IAuditLogService auditLogService)
        {
            string? passwordString;
            try
            {
                passwordString = Encoding.UTF8.GetString(Convert.FromBase64String(password));
            }
            catch (FormatException)
            {
                ModelState.AddModelError(nameof(password), "Format is invalid.");
                return ValidationProblem(ModelState);
            }
            int? userId = User.GetUserIdFromClaims();

            if (userId is not null)
            {
                var user = await _userService.FindByIdAsync(userId.Value, false);

                if (user is not null)
                {
                    await _userService.UpdatePasswordAsync(user.Id, passwordString);
                    await auditLogService.InsertAuditLogAsync("Password Reset", user.Email, user.Id);
                    return NoContent();
                }
            }

            return Unauthorized();
        }

        /// <summary>
        /// Logout a user and invalidate the token for future use
        /// </summary>
        /// <param name="cache">Instance of <see cref="IMemoryCache"/></param>
        /// <param name="jwtConfig">JWT configuration</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /auth/logout
        ///
        /// </remarks>
        /// <response code="204">Response when request is successful.</response>
        /// <response code="401">Response when the authorization token is missing or invalid.</response>
        [HttpPost]
        [Authorize]
        [Route("logout")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult> Logout([FromServices] IMemoryCache cache,
            [FromServices] IOptions<JwtConfigOptions> jwtConfig)
        {
            string token = Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
            var expirationMinutes = jwtConfig.Value.ExpirationMinutes * 1.5;
            cache.Set(token, token, TimeSpan.FromMinutes(expirationMinutes));

            return NoContent();
        }
    }
}
