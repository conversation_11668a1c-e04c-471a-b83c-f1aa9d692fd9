﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net.Mime;
using TIQuest.Api.DTO.Request;
using TIQuest.Api.DTO.Response;
using TIQuest.Api.Extensions;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Controllers
{
    [ApiController]
    [Route("accounts")]
    public class AccountController : ControllerBase
    {
        /// <summary>
        /// Retrieves list of all accounts.
        /// </summary>
        /// <param name="accountService">Instance of <see cref="IInvestmentAccountService"/></param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <returns>List of all accounts.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /accounts
        ///
        /// </remarks>
        /// <response code="200">List of accounts.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        [HttpGet]
        [Authorize]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<IEnumerable<InvestmentAccountResponse>>> Accounts([FromServices] IInvestmentAccountService accountService,
            [FromServices] IMapper mapper)
        {
            var userId = User.GetUserIdFromClaims();
            if (userId is null)
            {
                return Forbid();
            }
            var accounts = await accountService.GetInvestmentAccountsAsync(userId.Value);

            return Ok(accounts.Select(mapper.Map<InvestmentAccountResponse>));
        }

        /// <summary>
        /// Create a new account
        /// </summary>
        /// <param name="accountService">Instance of <see cref="IInvestmentAccountService"/></param>
        /// <param name="investorService">Instance of <see cref="IInvestorService"/></param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <param name="accountRequest">Instance of <see cref="AccountRequest"/></param>
        /// <returns>Account information.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /accounts
        ///     {
        ///         "Name": "string",
        ///         "Investor": 123,
        ///         "StartDate": "",
        ///         "EndDate": "",
        ///         "Rate": 0.12345,
        ///         "AccountType": "Investment",
        ///         "InterestType": "Ach",
        ///     }
        /// </remarks>
        /// <response code="201">Response when user is successfully created.</response>
        /// <response code="400">Response when input data is invalid.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        /// <response code="415">Response when the provided data is not of accepted type ie. json.</response>
        [HttpPost]
        [Authorize(Roles = Constants.RoleConstants.AdminRole)]
        [Consumes(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult> Post([FromServices] IInvestmentAccountService accountService,
            [FromServices] IInvestorService investorService,
            [FromServices] IMapper mapper,
            [FromServices] IAuditLogService auditLogService,
            [FromBody] AccountRequest accountRequest)
        {
            int? userId = User.GetUserIdFromClaims();

            if (userId is null)
            {
                return Unauthorized();
            }
            await ValidateAccountAsync(investorService, accountRequest);

            if (!ModelState.IsValid)
            {
                return ValidationProblem(ModelState);
            }

            var account = await accountService.CreateAsync(accountRequest, userId.Value);

            await auditLogService.InsertAuditLogAsync("Account Created", accountRequest.Name, userId.Value);
            return CreatedAtAction(nameof(GetAccount), new { id = account.Id }, mapper.Map<InvestmentAccountResponse>(account));
        }

        /// <summary>
        /// Edit an existing account
        /// </summary>
        /// <param name="id">Account Id to edit</param>
        /// <param name="accountService">Instance of <see cref="IInvestmentAccountService"/></param>
        /// <param name="investorService">Instance of <see cref="IInvestorService"/></param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <param name="accountRequest">Instance of <see cref="EditAccountRequest"/></param>
        /// <returns>Account information.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /accounts/123
        ///     {
        ///         "id": 123,
        ///         "Name": "string",
        ///         "Investor": 123,
        ///         "StartDate": "",
        ///         "EndDate": "",
        ///         "Rate": 0.12345,
        ///         "AccountType": "Investment",
        ///         "InterestType": "Ach",
        ///     }
        /// </remarks>
        /// <response code="200">Response when user is successfully created.</response>
        /// <response code="400">Response when input data is invalid.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        /// <response code="404">Response when requested resource is not found.</response>
        /// <response code="415">Response when the provided data is not of accepted type ie. json.</response>
        [HttpPut("{id}")]
        [Authorize(Roles = Constants.RoleConstants.AdminRole)]
        [Consumes(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult<InvestmentAccountResponse>> Put(int id,
            [FromServices] IInvestmentAccountService accountService,
            [FromServices] IInvestorService investorService,
            [FromServices] IMapper mapper,
            [FromBody] EditAccountRequest accountRequest)
        {
            int? userId = User.GetUserIdFromClaims();
            if (userId is null)
            {
                return Unauthorized();
            }
            if (id != accountRequest.Id)
            {
                ModelState.AddModelError(nameof(accountRequest.Id), $"Id in the request body does not match the id in the url.");
            }
            await ValidateAccountAsync(investorService, accountRequest);

            if (!ModelState.IsValid)
            {
                return ValidationProblem(ModelState);
            }

            var existingAccount = await accountService.FindByIdAsync(id);
            if (existingAccount is null)
            {
                return NotFound();
            }

            var updatedAccount = await accountService.UpdateAsync(accountRequest, userId.Value);

            return Ok(mapper.Map<InvestmentAccountResponse>(updatedAccount));
        }

        /// <summary>
        /// Get an account details by Id
        /// </summary>
        /// <param name="id">Account Id to edit</param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <param name="accountService">Instance of <see cref="IInvestmentAccountService"/></param>
        /// <returns>Account information.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     Get /accounts/123
        ///     
        /// </remarks>
        /// <response code="200">Investment account details.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        /// <response code="404">Response when requested resource is not found.</response>
        [HttpGet("{id}")]
        [Authorize(Roles = $"{Constants.RoleConstants.AdminRole}, {Constants.RoleConstants.AuditorRole}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<InvestmentAccountResponse>> GetAccount(int id,
            [FromServices] IMapper mapper,
            [FromServices] IInvestmentAccountService accountService)
        {
            var account = await accountService.FindByIdAsync(id);

            if (account is null)
            {
                return NotFound();
            }

            return Ok(mapper.Map<InvestmentAccountResponse>(account));
        }

        private async Task ValidateAccountAsync(IInvestorService investorService, AccountRequest accountRequest)
        {
            if (accountRequest.StartDate.UtcDateTime < Constants.DataConstants.SystemMinimumDate)
            {
                ModelState.AddModelError(nameof(accountRequest.StartDate), $"{nameof(accountRequest.StartDate)} must be greater than {Constants.DataConstants.SystemMinimumDate}.");
            }
            if (accountRequest.EndDate is not null && accountRequest.EndDate < accountRequest.StartDate)
            {
                ModelState.AddModelError(nameof(accountRequest.EndDate), $"{nameof(accountRequest.EndDate)} must be greater than {nameof(accountRequest.StartDate)}.");
            }
            if (accountRequest.Rate is not null && (accountRequest.Rate <= 0 || accountRequest.Rate >= 100))
            {
                ModelState.AddModelError(nameof(accountRequest.Rate), $"{nameof(accountRequest.Rate)} must be between 0 and 100, both exclusive.");
            }
            if (accountRequest.AccountType == Constants.DataConstants.InvestmentAccountType && accountRequest.InterestType is null)
            {
                ModelState.AddModelError(nameof(accountRequest.InterestType), $"{nameof(accountRequest.InterestType)} must be provided for investment accounts.");
            }
            var investor = await investorService.FindByIdAsync(accountRequest.Investor);
            if (investor is null || !investor.IsActive || investor.Id == Constants.DataConstants.SystemInvestorId)
            {
                ModelState.AddModelError(nameof(accountRequest.Investor), $"Investor with id {accountRequest.Investor} does not exist or is inactive.");
            }
        }
    }
}
