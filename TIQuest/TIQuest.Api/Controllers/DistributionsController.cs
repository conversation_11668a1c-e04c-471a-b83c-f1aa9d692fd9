﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net.Mime;
using TIQuest.Api.DTO.Request;
using TIQuest.Api.Extensions;
using TIQuest.Api.Interfaces.Services;

namespace TIQuest.Api.Controllers
{
    [Route("distributions")]
    [ApiController]
    [Authorize(Roles = Constants.RoleConstants.AdminRole)]
    public class DistributionsController : ControllerBase
    {
        /// <summary>
        /// Create new distribution
        /// </summary>
        /// <param name="request">New distribution request</param>
        /// <param name="distributionService">Instance of <see cref="IDistributionService"/></param>
        /// <param name="partnershipService">Instance of <see cref="IPartnershipService"/></param>
        /// <param name="monthEndProcessService">Instance of <see cref="IMonthEndProcessService"/></param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     POST /distributions
        ///     {
        ///         "partnership": 123,
        ///         "amount": 123.456,
        ///         "date": "2023-09-12T08:35:24+00:00",
        ///         "description": "string",
        ///     }
        /// </remarks>
        /// <response code="200">Response on successful execution.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        /// <response code="404">Response when partnership is not found.</response>
        [HttpPost]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult> CreateDistribution(
            [FromBody] NewDistributionRequest request,
            [FromServices] IDistributionService distributionService,
            [FromServices] IPartnershipService partnershipService,
            [FromServices] IMonthEndProcessService monthEndProcessService,
            [FromServices] IAuditLogService auditLogService)
        {
            var userId = User.GetUserIdFromClaims();
            if (userId is null)
            {
                return Unauthorized();
            }

            if (request.Amount <= 0)
            {
                ModelState.AddModelError(nameof(request.Amount), "Amount should be greater than 0.");
            }

            var partnership = await partnershipService.GetByIdAsync(request.Partnership);
            if (partnership is null)
            {
                ModelState.AddModelError(nameof(request.Partnership), "Partnership is not valid.");
            }
            else if (!partnership.IsActive)
            {
                ModelState.AddModelError(nameof(request.Partnership), "Can not distribute to inactive partnership.");
            }
            var investors = await partnershipService.GetPartnershipInvestorsAsync(request.Partnership);
            if (!investors.Any(x => x.Account.EndDate is null || x.Account.EndDate?.Date >= new DateTime(request.Date, TimeOnly.MinValue)))
            {
                ModelState.AddModelError(nameof(request.Partnership), "Partnership does not have any active investors on date of distribution.");
            }

            var lastProcessedMonth = await monthEndProcessService.GetLastProcessedMonthAsync();
            if (new DateTime(request.Date, TimeOnly.MinValue) <= lastProcessedMonth)
            {
                ModelState.AddModelError(nameof(request.Date), "Can not distribute to a month that has been processed.");
            }

            if (!ModelState.IsValid)
            {
                return ValidationProblem(ModelState);
            }

            await distributionService.CreateDistributionAsync(request, userId.Value);

            await auditLogService.InsertAuditLogAsync("Distribution Created", $"Created distribution of {request.Amount} for {partnership.Name}", userId.Value);

            return NoContent();
        }

        /// <summary>
        /// Delete a distribution
        /// </summary>
        /// <param name="id">Id of distribution</param>
        /// <param name="distributionService">Instance of <see cref="IDistributionService"/></param>
        /// <param name="monthEndProcessService">Instance of <see cref="IMonthEndProcessService"/></param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     DELETE /distributions/123
        /// </remarks>
        /// <response code="204">Response on successful execution.</response>
        /// <response code="400">Response when invalid input is provided.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        /// <response code="404">Response when partnership is not found.</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult> DeleteDistribution(
            int id,
            [FromServices] IDistributionService distributionService,
            [FromServices] IMonthEndProcessService monthEndProcessService,
            [FromServices] IPartnershipService partnershipService,
            [FromServices] IAuditLogService auditLogService)
        {
            var userId = User.GetUserIdFromClaims();
            if (userId is null)
            {
                return Unauthorized();
            }
            var existingDistribution = await distributionService.GetDistributionAsync(id);
            if (existingDistribution is null)
            {
                return NotFound();
            }
            var lastProcessedMonth = await monthEndProcessService.GetLastProcessedMonthAsync();
            if (existingDistribution.Date <= DateOnly.FromDateTime(lastProcessedMonth.Date))
            {
                ModelState.AddModelError(nameof(id), "Can not delete a distribution for the month that has been processed.");
            }
            var existingDistributions = await distributionService.GetPartnershipDistributionsAsync(existingDistribution.PartnershipId);
            if (existingDistributions.Any())
            {
                var latestDistribution = existingDistributions.OrderByDescending(x => x.Date).First();
                if (latestDistribution.Id != id)
                {
                    ModelState.AddModelError(nameof(id), "Can not delete a distribution that is not the latest.");
                }
            }
            if (!ModelState.IsValid)
            {
                return ValidationProblem(ModelState);
            }

            await distributionService.DeleteDistributionAsync(id, userId.Value);

            var partnership = await partnershipService.GetByIdAsync(existingDistribution.PartnershipId);

            await auditLogService.InsertAuditLogAsync("Distribution Deleted", $"Deleted distribution of {existingDistribution.Amount} for {partnership.Name}", userId.Value);

            return NoContent();
        }
    }
}
