﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net.Mime;
using TIQuest.Api.DTO.Request.Investors;
using TIQuest.Api.DTO.Response;
using TIQuest.Api.Extensions;
using TIQuest.Api.Interfaces.Services;
using TIQuest.Api.Poco.Entities;

namespace TIQuest.Api.Controllers
{
    [Route("investors")]
    [ApiController]
    public class InvestorController : ControllerBase
    {
        /// <summary>
        /// Retrieves list of all investors.
        /// </summary>
        /// <param name="investorService">Instance of <see cref="IInvestorService"/></param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <returns>List of all available investors.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /investors
        ///
        /// </remarks>
        /// <response code="200">List of investors.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        [HttpGet]
        [Authorize(Roles = $"{Constants.RoleConstants.AdminRole}, {Constants.RoleConstants.AuditorRole}")]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<IEnumerable<InvestorResponse>>> Investors([FromServices] IInvestorService investorService,
            [FromServices] IMapper mapper)
        {
            IEnumerable<Investor> investors = await investorService.GetAllInvestorsAsync();

            return Ok(investors.Select(mapper.Map<InvestorResponse>));
        }

        /// <summary>
        /// Get an investor by id.
        /// </summary>
        /// <param name="id">Id to find</param>
        /// <param name="investorService">Instance of <see cref="IInvestorService"/></param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <returns>List of all available investors.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /investors/123
        ///
        /// </remarks>
        /// <response code="200">List of investors.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        /// <response code="404">Response when id is not found.</response>
        [HttpGet("{id}")]
        [Authorize(Roles = Constants.RoleConstants.AdminRole)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<InvestorResponse>> GetById(int id, [FromServices] IInvestorService investorService,
            [FromServices] IMapper mapper)
        {
            Investor? investor = await investorService.FindByIdAsync(id);
            if (investor is null)
            {
                return NotFound();
            }

            return Ok(mapper.Map<InvestorResponse>(investor));
        }

        /// <summary>
        /// Create an investor entity.
        /// </summary>
        /// <param name="investorService">Instance of <see cref="IInvestorService"/></param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <param name="investorRequest">Instance of <see cref="NewInvestorRequest"/></param>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /investors
        ///     {
        ///         "firstName": "string",
        ///         "lastName": "string",
        ///         "companyName": "string",
        ///         "taxNumber": "string",
        ///         "type": "string",
        ///         "email": "string",
        ///         "officePhone": "string",
        ///         "homePhone": "string",
        ///         "mobile": "string",
        ///         "apartment": "string",
        ///         "address": "string",
        ///         "city": "string",
        ///         "state": "string",
        ///         "zip": "string",
        ///         "country": "string",
        ///         "note": "string",
        ///     }
        /// </remarks>
        /// <response code="201">Created investor object.</response>
        /// <response code="400">Response when input data is invalid.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        /// <response code="409">Response when the invester already exists.</response>
        /// <response code="415">Response when the provided data is not of accepted type ie. json.</response>
        [HttpPost]
        [Authorize(Roles = Constants.RoleConstants.AdminRole)]
        [Consumes(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult<InvestorResponse>> Create(
            [FromServices] IInvestorService investorService,
            [FromServices] IMapper mapper,
            [FromServices] IAuditLogService auditLogService,
            [FromBody] NewInvestorRequest investorRequest)
        {
            int? userId = User.GetUserIdFromClaims();

            if (userId is null)
            {
                return Unauthorized();
            }

            if (investorRequest.Type == Constants.DataConstants.IndividualInvestorType)
            {
                if (string.IsNullOrWhiteSpace(investorRequest.FirstName))
                {
                    ModelState.AddModelError(nameof(investorRequest.FirstName), "First name is required for individual type investors.");
                }
                if (string.IsNullOrWhiteSpace(investorRequest.LastName))
                {
                    ModelState.AddModelError(nameof(investorRequest.LastName), "Last name is required for individual type investors.");
                }
            }

            if (investorRequest.Type == Constants.DataConstants.CompanyInvestorType && string.IsNullOrWhiteSpace(investorRequest.CompanyName))
            {
                ModelState.AddModelError(nameof(investorRequest.CompanyName), "Company name is required for company type investors.");
            }

            if (!string.IsNullOrWhiteSpace(investorRequest.TaxNumber))
            {
                var existingInvestor = await investorService.FindByTaxNumberAsync(investorRequest.TaxNumber);
                if (existingInvestor is not null)
                {
                    ModelState.AddModelError(nameof(investorRequest.TaxNumber), $"{nameof(investorRequest.TaxNumber)} can not be duplicated.");
                }
            }

            if (!ModelState.IsValid)
            {
                return ValidationProblem(ModelState);
            }

            Investor investor = await investorService.CreateAsync(investorRequest, userId.Value);

            await auditLogService.InsertAuditLogAsync("Client Created", !string.IsNullOrWhiteSpace(investor.CompanyName)
                                                                                ? investor.CompanyName
                                                                                : $"{investor.LastName} {investor.FirstName}", userId.Value);

            return CreatedAtAction(nameof(GetById), new { id = investor.Id }, mapper.Map<InvestorResponse>(investor));
        }

        /// <summary>
        /// Edt an existing investor entity.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="investorService">Instance of <see cref="IInvestorService"/></param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <param name="investorRequest">Instance of <see cref="EditInvestorRequest"/></param>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /investors/123
        ///     {
        ///         "id": 123,
        ///         "firstName": "string",
        ///         "lastName": "string",
        ///         "companyName": "string",
        ///         "email": "string",
        ///         "officePhone": "string",
        ///         "homePhone": "string",
        ///         "mobile": "string",
        ///         "apartment": "string",
        ///         "address": "string",
        ///         "city": "string",
        ///         "state": "string",
        ///         "zip": "string",
        ///         "country": "string",
        ///         "note": "string",
        ///         "isActive": true
        ///     }
        /// </remarks>
        /// <response code="200">Updated investor object.</response>
        /// <response code="400">Response when input data is invalid.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        /// <response code="404">Response when requested resource is not found.</response>
        /// <response code="415">Response when the provided data is not of accepted type ie. json.</response>
        [HttpPut("{id}")]
        [Authorize(Roles = Constants.RoleConstants.AdminRole)]
        [Consumes(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult<InvestorResponse>> Put(int id,
            [FromServices] IInvestorService investorService,
            [FromServices] IMapper mapper,
            [FromBody] EditInvestorRequest investorRequest)
        {
            int? userId = User.GetUserIdFromClaims();

            if (userId is null)
            {
                return Unauthorized();
            }

            if (id != investorRequest.Id)
            {
                ModelState.AddModelError(nameof(investorRequest.Id), $"Id in the request body does not match the id in the url.");
            }

            var existingInvestor = await investorService.FindByIdAsync(investorRequest.Id);

            if (existingInvestor is null)
            {
                return NotFound();
            }
            // check if user has deleted company name for company type investor
            if (existingInvestor.Type == Constants.DataConstants.IndividualInvestorType)
            {
                if (string.IsNullOrWhiteSpace(investorRequest.FirstName))
                {
                    ModelState.AddModelError(nameof(investorRequest.FirstName), "First name is required for individual type investors.");
                }
                if (string.IsNullOrWhiteSpace(investorRequest.LastName))
                {
                    ModelState.AddModelError(nameof(investorRequest.LastName), "Last name is required for individual type investors.");
                }
            }

            if (existingInvestor.Type == Constants.DataConstants.CompanyInvestorType && string.IsNullOrWhiteSpace(investorRequest.CompanyName))
            {
                ModelState.AddModelError(nameof(investorRequest.CompanyName), "Company name is required for company type investors.");
            }

            if (!ModelState.IsValid)
            {
                return ValidationProblem(ModelState);
            }

            Investor investor = await investorService.UpdateAsync(investorRequest, userId.Value);

            return Ok(mapper.Map<InvestorResponse>(investor));
        }

        /// <summary>
        /// Retrieves account information for an investor
        /// </summary>
        /// <param name="Id">Id of the investor</param>
        /// <param name="mapper">Instance of <see cref="IMapper"/></param>
        /// <param name="accountService">Instance of <see cref="IInvestmentAccountService"/></param>
        /// <returns>account information.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /investors/1/accounts
        ///
        /// </remarks>
        /// <response code="200">List of accounts for client id.</response>
        /// <response code="401">Response when JWT token is missing or invalid.</response>
        /// <response code="403">Response when user does not have valid claims.</response>
        [HttpGet("{Id}/accounts")]
        [Authorize]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<InvestmentAccountResponse>>> Accounts(int Id,
            [FromServices] IMapper mapper,
            [FromServices] IInvestmentAccountService accountService)
        {
            var userId = User.GetUserIdFromClaims();
            if (userId is null)
            {
                return Forbid();
            }
            var accounts = await accountService.GetAccountsByInvestorAsync(Id, userId.Value);

            return Ok(accounts.Select(mapper.Map<InvestmentAccountResponse>));
        }
    }
}
