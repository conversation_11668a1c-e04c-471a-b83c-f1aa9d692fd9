﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>2af1f7f3-02d8-4e97-80bb-da00c7560c20</UserSecretsId>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="13.0.1" />
		<PackageReference Include="Azure.Monitor.OpenTelemetry.AspNetCore" Version="1.2.0" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.19.1" />
		<PackageReference Include="Azure.Storage.Queues" Version="12.17.1" />
		<PackageReference Include="CsvHelper" Version="31.0.2" />
		<PackageReference Include="Humanizer.Core" Version="2.14.1" />
		<PackageReference Include="MailKit" Version="4.3.0" />
		<PackageReference Include="MESCIUS.ActiveReports.Export.Excel" Version="18.0.0" />
		<PackageReference Include="MESCIUS.ActiveReports.Export.Pdf" Version="18.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.2" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.2" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.2" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.2">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Azure" Version="1.7.2" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
	</ItemGroup>

	<ItemGroup>
		<Compile Update="Resources.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>Resources.resx</DependentUpon>
		</Compile>
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Update="Resources.resx">
			<Generator>ResXFileCodeGenerator</Generator>
			<LastGenOutput>Resources.Designer.cs</LastGenOutput>
		</EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\TIQuest.Entities\TIQuest.Entities.csproj" />
	</ItemGroup>

	<ItemGroup>
		<None Update="Reports\Layouts\*">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
			<_Parameter1>TIQuest.UnitTests</_Parameter1>
		</AssemblyAttribute>
	</ItemGroup>

</Project>
