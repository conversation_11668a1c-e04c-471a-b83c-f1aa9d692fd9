﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System.Data;

namespace TIQuest.EmailWorker
{
    internal class EmailLogStatusService
    {
        private readonly IConfiguration _config;

        public EmailLogStatusService(IConfiguration configuration)
        {
            _config = configuration;
        }

        public async Task UpdateEmailLogStatusAsync(string location, string recipient, string subject, string status)
        {
            using (var conn = new SqlConnection(_config.GetConnectionString("TIQuest")))
            {
                await conn.OpenAsync();

                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandText = "UPDATE EmailLog SET Status = @status WHERE Location = @location AND Email = @recipient AND Subject = @subject";
                    cmd.Parameters.Add(new SqlParameter("@status", SqlDbType.NVarChar) { Value = status });
                    cmd.Parameters.Add(new SqlParameter("@location", SqlDbType.NVarChar) { Value = location });
                    cmd.Parameters.Add(new SqlParameter("@recipient", SqlDbType.NVarChar) { Value = recipient });
                    cmd.Parameters.Add(new SqlParameter("@subject", SqlDbType.NVarChar) { Value = subject });

                    await cmd.ExecuteNonQueryAsync();
                }
            }
        }
    }
}
