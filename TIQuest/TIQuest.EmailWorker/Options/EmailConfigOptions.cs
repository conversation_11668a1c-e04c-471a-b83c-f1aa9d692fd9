﻿using TIQuest.Entities.Models;

namespace TIQuest.EmailWorker.Options
{
    public class EmailConfigOptions
    {
        public const string MyConfig = "EmailConfig";

        public bool? EnableRedirection { get; set; }
        public string? RedirectionRecipients { get; set; }
        public IList<EmailAddress> RedirectedRecipients => GetRedirectedList(RedirectionRecipients);

        private IList<EmailAddress> GetRedirectedList(string? redirectionRecipients)
        {
            List<EmailAddress> list = new List<EmailAddress>();
            if (!string.IsNullOrWhiteSpace(redirectionRecipients))
            {
                string[] recipients = redirectionRecipients.Split(';');
                if (recipients.Length > 0)
                {
                    foreach (string recipient in recipients)
                    {
                        string[] recipientDetails = recipient.Split(',');
                        if (recipientDetails.Length == 2)
                        {
                            list.Add(new EmailAddress { Name = recipientDetails[0], Address = recipientDetails[1] });
                        }
                    }
                }
            }
            return list;
        }

        public string? Env { get; set; }
    }
}
