﻿using MimeKit;
using TIQuest.Entities.Models;

namespace TIQuest.EmailWorker
{
    internal class MimeMessageBuilder
    {
        private readonly MimeMessage _message = new MimeMessage();
        private readonly Multipart _multipart = new Multipart("mixed");

        public MimeMessageBuilder WithSender(EmailAddress sender)
        {
            _message.From.Add(new MailboxAddress(sender.Name, sender.Address));
            return this;
        }

        public MimeMessageBuilder WithRecipients(IEnumerable<EmailAddress> recipients)
        {
            foreach (var recipient in recipients)
            {
                _message.To.Add(new MailboxAddress(recipient.Name, recipient.Address));
            }
            return this;
        }

        public MimeMessageBuilder WithCC(IEnumerable<EmailAddress>? ccRecipients)
        {
            if (ccRecipients is not null && ccRecipients.Any())
            {
                foreach (var recipient in ccRecipients)
                {
                    _message.Cc.Add(new MailboxAddress(recipient.Name, recipient.Address));
                }
            }
            return this;
        }

        public MimeMessageBuilder WithBCC(IEnumerable<EmailAddress>? bccRecipients)
        {
            if (bccRecipients is not null && bccRecipients.Any())
            {
                foreach (var recipient in bccRecipients)
                {
                    _message.Bcc.Add(new MailboxAddress(recipient.Name, recipient.Address));
                }
            }
            return this;
        }

        public MimeMessageBuilder WithContent(string body, string subType, IEnumerable<EmailAttachment>? attachments)
        {
            var messageBody = new TextPart(subType)
            {
                Text = body
            };

            if (attachments is not null && attachments.Any())
            {
                _multipart.Add(messageBody);
                foreach (var attachment in attachments)
                {
                    var mimePart = new MimePart("application", "octet-stream")
                    {
                        Content = new MimeContent(new MemoryStream(attachment.Content)),
                        ContentDisposition = new ContentDisposition(ContentDisposition.Attachment),
                        ContentTransferEncoding = ContentEncoding.Base64,
                        FileName = attachment.Name
                    };
                    _multipart.Add(mimePart);
                }
            }
            else
            {
                _message.Body = messageBody;
            }

            return this;
        }

        public MimeMessageBuilder WithSubject(string subject)
        {
            _message.Subject = subject;
            return this;
        }

        public MimeMessageBuilder UseRedirection(IEnumerable<EmailAddress> redirectedRecipients, string env)
        {
            // reset recipients
            var originalRecipient = ResetRecipients(_message.To);

            foreach (var recipient in redirectedRecipients)
            {
                _message.To.Add(new MailboxAddress(recipient.Name, recipient.Address));
            }

            // reset CC
            var originalCC = ResetRecipients(_message.Cc);

            // reset BCC
            var originalBCC = ResetRecipients(_message.Bcc);

            // reset body
            var isMultipart = _message.Body is null;
            var originalBody = isMultipart ? _multipart.OfType<TextPart>().FirstOrDefault() : _message.Body as TextPart;
            if (originalBody is not null)
            {

                var originalTo = GetOriginalRecipients("To", originalRecipient);
                var originalCc = GetOriginalRecipients("Cc", originalCC);
                var originalBcc = GetOriginalRecipients("Bcc", originalBCC);
                var subject = _message.Subject;
                var body = originalBody.Text;
                var redirectionBody = new TextPart(originalBody.ContentType.MediaSubtype)
                {
						 Text = $"**** REDIRECTED EMAIL **** Original: {originalTo}{originalCc}{originalBcc}, Subject: [{subject}]{Environment.NewLine}{body}"
					 };
                if (isMultipart)
                {
                    var index = _multipart.IndexOf(originalBody);
                    _multipart[index] = redirectionBody;
                }
                else
                {
                    _message.Body = redirectionBody;
                }
            }

            // reset subject
            _message.Subject = $" ({env ?? "Test"}) {_message.Subject}";

            return this;
        }

        private IEnumerable<(string Name, string Address)> ResetRecipients(InternetAddressList originalRecipients)
        {
            var recipients = new List<(string Name, string Address)>();
            foreach (var address in originalRecipients)
            {
                var mailboxAddress = address as MailboxAddress;
                if (mailboxAddress is not null)
                {
                    recipients.Add((mailboxAddress.Name, mailboxAddress.Address));
                }
            }
            originalRecipients.Clear();



            return recipients;
        }

        public MimeMessage Build()
        {
            if (_message.Body is null)
            {
                _message.Body = _multipart;
            }
            return _message;
        }

        private string GetOriginalRecipients(string type, IEnumerable<(string Name, string Address)> recipients)
        {
            return $"{type}: [{string.Join(", ", recipients.Select(e => $"{e.Name} ({e.Address})"))}]";
        }
    }
}
