﻿using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using MailKit.Net.Smtp;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MimeKit;
using System.Text;
using System.Text.Json;
using TIQuest.EmailWorker.Options;
using TIQuest.Entities.Models;

namespace TIQuest.EmailWorker
{
    public class Functions
    {
        private readonly IConfiguration _configuration;
        private readonly EmailConfigOptions _emailOptions = new EmailConfigOptions();

        public Functions(IConfiguration configuration)
        {
            _configuration = configuration;

            configuration.GetSection(EmailConfigOptions.MyConfig).Bind(_emailOptions);
        }

        public async Task ProcessQueueMessageAsync(
            [QueueTrigger("email-queue")] string message,
            [Blob("email-container")] BlobContainerClient blobContainerClient,
            ILogger logger)
        {
            var statusService = new EmailLogStatusService(_configuration);

            var blobClient = blobContainerClient.GetBlobClient(message);
            EmailMessage? emailMessage = await RetrieveEmailMessageAsync(blobClient);
            if (emailMessage is not null)
            {
                try
                {
                    MimeMessage mimeMessage = GetMessage(emailMessage);
                    await SendEmailAsync(logger, emailMessage, mimeMessage);


                    foreach (var recipient in emailMessage.To)
                    {
                        await statusService.UpdateEmailLogStatusAsync(message, recipient.Address, emailMessage.Subject, "Sent");
                    }
                    await blobClient.DeleteIfExistsAsync();
                }

                catch (Exception ex)
                {
                    logger.LogError(ex, ex.Message);

                    foreach (var recipient in emailMessage.To)
                    {
                        await statusService.UpdateEmailLogStatusAsync(message, recipient.Address, emailMessage.Subject, "Failed");
                    }
                }
            }
        }

        private async Task SendEmailAsync(ILogger logger, EmailMessage emailMessage, MimeMessage mimeMessage)
        {
            string? smtpHost = _configuration["SMTP:Host"];
            if (string.IsNullOrWhiteSpace(smtpHost))
            {
                throw new ApplicationException("SMTP host is missing or empty.");
            }

            string? smtpPort = _configuration["SMTP:Port"];
            string? useSsl = _configuration["SMTP:UseSSL"];

            using (var client = new SmtpClient())
            {
                client.Connect(smtpHost,
                    string.IsNullOrWhiteSpace(smtpPort) ? 587 : int.Parse(smtpPort),
                    !string.IsNullOrWhiteSpace(useSsl) && bool.Parse(useSsl));

                var response = await client.SendAsync(mimeMessage);

                logger.LogInformation($"response from {smtpHost}" + response);

                logger.LogInformation($"Email sent successfully:\nFrom: {emailMessage.From.Name} <{emailMessage.From.Address}>\nTo: {string.Join(", ", emailMessage.To.Select(e => $"{e.Name} <{e.Address}>"))}\n{(emailMessage.Cc?.Any() == true ? $"Cc: {string.Join(", ", emailMessage.Cc.Select(e => $"{e.Name} <{e.Address}>"))}\n" : "")}{(emailMessage.Bcc?.Any() == true ? $"Bcc: {string.Join(", ", emailMessage.Bcc.Select(e => $"{e.Name} <{e.Address}>"))}\n" : "")}Subject: {emailMessage.Subject}\nBody:\n{emailMessage.Body}");

                client.Disconnect(true);
            }
        }

        private MimeMessage GetMessage(EmailMessage emailMessage)
        {
            var messageBuilder = new MimeMessageBuilder()
                                .WithSender(emailMessage.From)
                                .WithSubject(emailMessage.Subject)
                                .WithRecipients(emailMessage.To)
                                .WithCC(emailMessage.Cc)
                                .WithBCC(emailMessage.Bcc)
                                .WithContent(emailMessage.Body, emailMessage.SubType, emailMessage.Attachments);

            if (_emailOptions.EnableRedirection.HasValue && _emailOptions.EnableRedirection.Value)
            {
                messageBuilder.UseRedirection(_emailOptions.RedirectedRecipients, _emailOptions.Env);
            }

            var mimeMessage = messageBuilder.Build();
            return mimeMessage;
        }

        private async Task<EmailMessage?> RetrieveEmailMessageAsync(BlobClient blobClient)
        {
            BlobDownloadResult downloadResult = await blobClient.DownloadContentAsync();
            string blobContents = downloadResult.Content.ToString();

            var base64DecodedBytes = Convert.FromBase64String(blobContents);
            var base64DecodedString = Encoding.UTF8.GetString(base64DecodedBytes);

            return JsonSerializer.Deserialize<EmailMessage>(base64DecodedString);
        }
    }
}
