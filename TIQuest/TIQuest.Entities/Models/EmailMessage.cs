﻿using System.Runtime.Serialization;

namespace TIQuest.Entities.Models
{
    [DataContract]
    public class EmailMessage
    {
        [DataMember]
        public required EmailAddress From { get; set; }

        [DataMember]
        public required IList<EmailAddress> To { get; set; }

        [DataMember]
        public IList<EmailAddress>? Cc { get; set; }

        [DataMember]
        public IList<EmailAddress>? Bcc { get; set; }

        [DataMember]
        public required string Subject { get; set; }

        [DataMember]
        public required string Body { get; set; }

        [DataMember]
        public required string SubType { get; set; } = "plain";

        [DataMember]
        public IList<EmailAttachment>? Attachments { get; set; }
    }
}