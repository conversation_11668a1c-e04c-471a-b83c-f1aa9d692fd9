# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: Build and deploy ASP.Net Core app to Azure Web App - tiquest-be-prod

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  build:
    runs-on: windows-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up .NET Core
        uses: actions/setup-dotnet@v1
        with:
          dotnet-version: '8.x'
          include-prerelease: true
          
      - name: Azure SQL Deploy
        uses: Azure/sql-action@v2.2.1
        with:
          connection-string: ${{ secrets.CONNECTION_STRING_UAT }}
          path: TIQuest.Api/Scripts/Deploy.sql
          
      - name: Install ActiveReports
        run: dotnet tool install -g GrapeCity.LicenseManagerTool

      - name: Activate ActiveReports
        run: gclm "463c4179-288c-48fe-a4df-3b609586667d" -a ${{ secrets.ACTIVEREPORTSKEY }}

      - name: Build with dotnet
        run: dotnet build --configuration Release

      - name: dotnet publish
        run: dotnet publish TIQuest.Api.csproj -c Release -o ${{env.DOTNET_ROOT}}/myapp
        working-directory: TIQuest.Api

      - name: Deactivate ActiveReports
        if: always()
        run: gclm "463c4179-288c-48fe-a4df-3b609586667d" -d

      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v4
        with:
          name: .net-app
          path: ${{env.DOTNET_ROOT}}/myapp

  deploy:
    runs-on: windows-latest
    needs: build
    environment:
      name: 'uat'
      url: ${{ steps.deploy-to-webapp.outputs.webapp-url }}
    
    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: .net-app
      
      - name: Deploy to Azure Web App
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v2
        with:
          app-name: 'tiquest-be-prod'
          slot-name: 'uat'
          package: .
          publish-profile: ${{ secrets.AZUREAPPSERVICE_PUBLISHPROFILE_CE2F8C75DEDA4724875E5FFF3A80F54B }}
