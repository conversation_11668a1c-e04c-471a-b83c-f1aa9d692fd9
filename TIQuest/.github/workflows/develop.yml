# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: CD - Staging - TIQuest API

on:
  push:
    branches:
      - develop

jobs:
  initializer:
    runs-on: ubuntu-latest
    outputs:
      dbchanges: ${{steps.check_db_changes.outputs.db_changed}}
      codechanges: ${{steps.check_code_changes.outputs.code_changed}}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Check for DB Changes
        id: check_db_changes
        run: |
          # Check for changes in a specific path using Git commands
          if [ "$(git diff --name-only HEAD^ HEAD -- ':TIQuest.Api/Scripts')" == "" ];
          then
            echo "false"
            echo "db_changed=false" >> "$GITHUB_OUTPUT"
          else
            echo "true"
            echo "db_changed=true" >> "$GITHUB_OUTPUT"
          fi

      - name: Check for code Changes
        id: check_code_changes
        run: |
          # Check for changes in a specific path using Git commands
          if [ "$(git diff --name-only HEAD^ HEAD -- . ':!TIQuest.Api/Scripts' ':!Tests/')" == "" ];
          then
            echo "false"
            echo "code_changed=false" >> "$GITHUB_OUTPUT"
          else
            echo "true"
            echo "code_changed=true" >> "$GITHUB_OUTPUT"
          fi

      - name: Upload scripts
        if: steps.check_db_changes.outputs.db_changed == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: scripts
          path: TIQuest.Api/Scripts

  build:
    runs-on: ubuntu-latest
    needs: initializer
    if: ${{ success() && needs.initializer.outputs.codechanges == 'true' }}
    steps:
      - uses: actions/checkout@v4
        name: Checkout code
        
      - name: Set up .NET Core
        uses: actions/setup-dotnet@v2
        with:
          dotnet-version: '8.x'
      
      - name: Install ActiveReports
        run: dotnet tool install -g GrapeCity.LicenseManagerTool

      - name: Activate ActiveReports
        run: gclm "463c4179-288c-48fe-a4df-3b609586667d" -a ${{ secrets.ACTIVEREPORTSKEY }}

      # - name: Run Unit tests
      #   run: dotnet test TIQuest.UnitTests.csproj --configuration Release
      #   working-directory: Tests/TIQuest.UnitTests

      # - name: Create test report
      #   uses: dorny/test-reporter@v1.7.0
      #   with:
      #     name: 'Unit test report'
      #     reporter: 'dotnet-trx'
      #     fail-on-error: 'true'
      #     path: '**/test-results.trx'

      - name: dotnet publish
        run: dotnet publish TIQuest.Api.csproj -c Release -o ${{env.DOTNET_ROOT}}/tiquest-api
        working-directory: TIQuest.Api

      - name: Deactivate ActiveReports
        if: always()
        run: gclm "463c4179-288c-48fe-a4df-3b609586667d" -d

      - name: Upload binaries
        uses: actions/upload-artifact@v4
        with:
          name: webapp
          path: ${{env.DOTNET_ROOT}}/tiquest-api

  deployDatabase:
    runs-on: ubuntu-latest
    needs: [initializer, build]
    if: ${{ always() && needs.initializer.outputs.dbchanges == 'true' && (needs.build.result == 'success' ||  needs.build.result == 'skipped') }}
    steps:
      - name: Download scripts
        uses: actions/download-artifact@v4
        with:
          name: scripts

      - name: Apply Database Upgrades
        uses: azure/sql-action@v2.2
        with:
          connection-string: ${{ secrets.CONNECTION_STRING_STAGING }}
          path: './*.sql'

  deployApplication:
    runs-on: ubuntu-latest
    needs: build
    if: ${{ always() && needs.build.result == 'success' }}
    environment:
      name: 'Production'
      url: ${{ steps.deploy-to-webapp.outputs.webapp-url }}

    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: webapp

      - name: Deploy to Azure Web App
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v2
        with:
          app-name: 'tiquest-staging-api'
          slot-name: 'Production'
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE_STAGING }}
          package: .

  # apiTests:
  #   runs-on: ubuntu-latest
  #   needs: [deployDatabase, deployApplication]
  #   if: ${{ always() && (needs.deployDatabase.result == 'success' ||  needs.deployApplication.result == 'success') }}
  #   steps:
  #     - uses: actions/checkout@v4

  #     - name: Cypress run
  #       uses: cypress-io/github-action@v6
  #       with:
  #         working-directory: Tests/API
