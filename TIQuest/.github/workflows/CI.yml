# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: CI Build

on:
  pull_request:
    types:
      - opened
      - synchronize

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up .NET Core
        uses: actions/setup-dotnet@v2
        with:
          dotnet-version: '8.x'
      
      - name: Build with dotnet
        run: dotnet build TIQuest.sln --configuration Release

      # - name: Run Unit tests
      #   run: dotnet test TIQuest.UnitTests.csproj --configuration Release --logger "trx;LogFileName=test-results.trx" --no-build
      #   working-directory: Tests/TIQuest.UnitTests

